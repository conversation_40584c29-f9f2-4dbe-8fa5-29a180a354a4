import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, inject, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { NavigationEnd, Router, RouterLink } from '@angular/router';
import { SimplifiedMenuItem } from '@trendency/kesma-ui';
import { BehaviorSubject, filter, fromEvent, map, of, skip, Subject, switchMap, takeUntil, tap } from 'rxjs';
import { writeToUs } from '../../constants';
import { UtilService } from '@trendency/kesma-core';
import { AsyncPipe, NgClass, NgIf, NgTemplateOutlet } from '@angular/common';
import { HeaderDropdownComponent } from './components/header-dropdown/header-dropdown.component';
import { ArticleService } from '../../services';

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [Ng<PERSON>lass, AsyncPipe, NgIf, RouterLink, HeaderDropdownComponent, NgTemplateOutlet],
})
export class HeaderComponent implements OnInit, OnDestroy {
  @Input() mainMenu: SimplifiedMenuItem[] = [];
  @Input() subMenu: SimplifiedMenuItem[] = [];
  @ViewChild('hamburgerMenu') hamburgerMenuElement?: ElementRef;
  highlighted: SimplifiedMenuItem;
  titlePage: SimplifiedMenuItem;
  isLightTheme = false;
  isHomePage = true;
  email = writeToUs.email;
  destroy$ = new Subject();
  private readonly utils = inject(UtilService);
  isScrolled$ = this.utils.isBrowser() ? fromEvent(window, 'scroll').pipe(map(() => window.scrollY !== 0)) : of(false);
  readonly #isMenuOpen$ = new BehaviorSubject<boolean>(false);
  readonly isMenuOpen$ = this.#isMenuOpen$.asObservable();

  constructor(
    private readonly router: Router,
    private readonly changeDetectionRef: ChangeDetectorRef,
    private readonly articleService: ArticleService
  ) {}

  ngOnInit(): void {
    this.detectUrlChanges(this.router.url);
    this.router.events
      .pipe(
        filter((e) => e instanceof NavigationEnd),
        takeUntil(this.destroy$)
      )
      .subscribe((router) => {
        if (this.#isMenuOpen$.value) {
          this.closeHamburgerMenu();
        }
        this.detectUrlChanges((router as NavigationEnd)?.url);
        this.changeDetectionRef.markForCheck();
      });
    this.articleService.isOpinion.pipe(takeUntil(this.destroy$)).subscribe((isOpinion) => {
      this.isLightTheme = isOpinion;
      this.changeDetectionRef.markForCheck();
    });

    this.isMenuOpen$
      .pipe(
        filter((openState) => openState),
        switchMap(() => {
          return fromEvent(document, 'click').pipe(
            skip(1),
            tap((event: Event) => {
              const isEventTargetInHamburgerMenu = this.hamburgerMenuElement?.nativeElement.contains(event.target);
              if (!isEventTargetInHamburgerMenu) {
                this.closeHamburgerMenu();
              }
            }),
            takeUntil(this.isMenuOpen$.pipe(skip(1)))
          );
        })
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe();

    // TODO: replace mainMenu[0] with last 'Déli szó' column
    this.highlighted = this.mainMenu[0];
    // TODO: replace subMenu[0] with title page
    this.titlePage = this.subMenu[0];
  }

  getLogo(isScrolled: boolean): string {
    return `/assets/images/${isScrolled ? 'szma' : 'szegedma'}${this.isLightTheme ? '-black' : ''}.svg`;
  }

  logoClick(): void {
    if (this.isHomePage) {
      window.location.href = '/';
    } else {
      this.router.navigate(['/']);
    }
  }

  toggleHamburgerMenu(): void {
    this.#isMenuOpen$.next(!this.#isMenuOpen$.value);
  }

  closeHamburgerMenu(): void {
    this.#isMenuOpen$.next(false);
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
    this.destroy$.complete();
  }

  private detectUrlChanges(url: string): void {
    this.isHomePage = url === '/';
  }
}
