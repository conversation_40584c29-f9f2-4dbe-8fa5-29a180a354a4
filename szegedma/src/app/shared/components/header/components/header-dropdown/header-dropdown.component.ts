import { ChangeDetectionStrategy, Component, EventEmitter, HostListener, Input, Output } from '@angular/core';
import { ReactiveFormsModule, UntypedFormControl } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { SimplifiedMenuItem } from '@trendency/kesma-ui';
import { NgIf, NgTemplateOutlet } from '@angular/common';
import { SocialBoxComponent } from '../../../social-box/social-box.component';

@Component({
  selector: 'app-header-dropdown',
  templateUrl: './header-dropdown.component.html',
  styleUrls: ['./header-dropdown.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ReactiveFormsModule, RouterLink, NgIf, SocialBoxComponent, NgTemplateOutlet],
})
export class HeaderDropdownComponent {
  @Input() mainMenu: SimplifiedMenuItem[] = [];
  @Input() subMenu: SimplifiedMenuItem[] = [];

  @Output() searchEvent = new EventEmitter();

  searchKey = new UntypedFormControl('');

  constructor(private readonly router: Router) {}

  @HostListener('document:keydown', ['$event'])
  public onKeyDown(event: KeyboardEvent): void {
    this.handleEvent(event);
  }

  search(): void {
    this.router.navigate(['/kereses'], {
      queryParams: { global_filter: this.searchKey.value },
    });
    this.searchEvent.emit();
    this.searchKey.setValue('');
  }

  private handleEvent(event: KeyboardEvent): void {
    switch (event.key) {
      case 'Enter':
        event.preventDefault();
        event.stopPropagation();
        this.search();
        break;
    }
  }
}
