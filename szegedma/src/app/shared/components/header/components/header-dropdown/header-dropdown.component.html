<nav class="header-dropdown">
  <div class="header-dropdown-search">
    <button class="btn-icon light" (click)="search()">
      <i class="icon icon-search-orange"></i>
    </button>
    <input class="input-search" [formControl]="searchKey" type="text" placeholder="Keresés" />
  </div>
  <div class="header-dropdown-highlighted">
    @for (item of mainMenu; track item?.id) {
      @if (item?.isCustomUrl) {
        <a class="header-dropdown-highlighted-item" [href]="item?.link" [target]="item.target">
          <ng-container *ngTemplateOutlet="menuItemTemplate; context: { item }"></ng-container>
        </a>
      } @else {
        <a class="header-dropdown-highlighted-item" [routerLink]="item.link">
          <ng-container *ngTemplateOutlet="menuItemTemplate; context: { item }"></ng-container>
        </a>
      }
    }
  </div>
  <ng-container *ngIf="subMenu?.length">
    <div class="separator in-dropdown"></div>
    <div class="header-dropdown-columns">
      @for (item of subMenu; track item?.id) {
        @if (item?.isCustomUrl) {
          <a class="btn-stroked" [href]="item?.link" [target]="item.target">
            <ng-container *ngTemplateOutlet="menuItemTemplate; context: { item, isSubmenu: true }"></ng-container>
          </a>
        } @else {
          <a class="btn-stroked" [routerLink]="item.link">
            <ng-container *ngTemplateOutlet="menuItemTemplate; context: { item, isSubmenu: true }"></ng-container>
          </a>
        }
      }
    </div>
  </ng-container>
  <div class="separator in-dropdown"></div>
  <div class="header-dropdown-navs">
    <a class="btn-icon" href="mailto:<EMAIL>" rel="noopener noreferrer" target="_blank">
      <i class="icon icon-send"></i>
      <p>Írjon nekünk!</p>
    </a>
    <!-- TODO: replace mainMenu[0] with last 'Déli szó' column -->
    <!--
    <a class="btn-icon" [target]="mainMenu[0].target" [routerLink]="mainMenu[0].link">
      <i class="icon icon-news"></i>
      <p>Déli Szó: {{ mainMenu[0].title }}</p>
    </a>
    -->
  </div>
  <div class="separator last"></div>
  <div class="header-dropdown-social">
    <szegedma-social-box></szegedma-social-box>
  </div>
</nav>

<ng-template #menuItemTemplate let-item="item" let-isSubmenu="isSubmenu">
  @if (!isSubmenu) {
    <i class="icon icon-bullet-point"></i>
  } @else if (item.relatedType === 'Opinion') {
    <i class="icon icon-quotation-mark-orange"></i>
  }

  <p>{{ item.title }}</p>
</ng-template>
