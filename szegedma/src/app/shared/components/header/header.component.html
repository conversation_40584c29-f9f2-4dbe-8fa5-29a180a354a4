<header class="header" [class.light]="isLightTheme" *ngIf="{ value: isScrolled$ | async } as isScrolled">
  <div class="header-main">
    <div class="header-main-container">
      <button class="btn-icon" (click)="toggleHamburgerMenu()">
        <i class="icon" [ngClass]="(isMenuOpen$ | async) ? 'icon-times' : 'icon-hamburger'"></i>
      </button>
      <!--      <ng-container *ngIf="!isScrolled.value; else scrolled">-->
      <!--        <a class="btn-icon desktop" [target]="highlighted.target" [routerLink]="highlighted.link">-->
      <!--          <i class="icon icon-news"></i>-->
      <!--          <span>Dé<PERSON>: {{ highlighted.title }}</span>-->
      <!--        </a>-->
      <!--      </ng-container>-->
      <ng-template #scrolled>
        <a class="btn-icon desktop" [target]="titlePage?.target ?? ''" [routerLink]="titlePage?.link">
          <i class="icon icon-news"></i>
          <span>{{ titlePage?.title }}</span>
        </a>
      </ng-template>
    </div>
    <ng-container>
      <div class="header-main-logo" [class.scrolled]="isScrolled.value" (click)="logoClick()">
        <img [src]="getLogo(!!isScrolled.value)" alt="Szegedma" />
        <span class="header-main-logo-title mobile">| {{ titlePage?.title }}</span>
      </div>
    </ng-container>
    <div class="header-main-container">
      <a class="btn-icon desktop" [href]="'mailto:' + email" rel="noopener noreferrer" target="_blank">
        <i class="icon icon-send"></i>
        <span>Írjon nekünk!</span>
      </a>
      <a class="btn-icon" [routerLink]="['/kereses']">
        <i class="icon icon-search-orange"></i>
      </a>
    </div>
  </div>
  <div class="header-sub">
    @for (item of mainMenu; track item?.id) {
      @if (item?.isCustomUrl) {
        <a class="header-sub-item" [href]="item?.link" [target]="item.target">
          <ng-container *ngTemplateOutlet="menuItemTemplate; context: { item }"></ng-container>
        </a>
      } @else {
        <a class="header-sub-item" [routerLink]="item.link">
          <ng-container *ngTemplateOutlet="menuItemTemplate; context: { item }"></ng-container>
        </a>
      }
    }
  </div>
  <div *ngIf="isMenuOpen$ | async" #hamburgerMenu>
    <app-header-dropdown [mainMenu]="mainMenu" [subMenu]="subMenu" (searchEvent)="closeHamburgerMenu()"></app-header-dropdown>
  </div>
</header>

<ng-template #menuItemTemplate let-item="item">
  <i class="icon icon-bullet-point"></i>
  <p>{{ item.title }}</p>
</ng-template>
