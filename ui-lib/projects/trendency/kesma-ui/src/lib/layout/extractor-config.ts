import { DataExtractor } from './definitions';
import { LayoutElementContentType } from '../definitions';
import {
  ArticleArrayExtractor,
  ArticleCardExtractor,
  BrandingBoxExtractor,
  BreakingExtractor,
  Do<PERSON>rExtractor,
  ImageExtractor,
  NoteExtractor,
  OpinionWithMultipleAuthorsExtractor,
  PodcastArrayExtractor,
  PublicAuthorsExtractor,
  QuizExtractor,
  TabsExtractor,
  VideoBlockExtractor,
  VideoCardExtractor,
  VoteExtractor,
  WysiwygExtractor,
} from './data-extractors';
import { LatestNewsExtractor } from './data-extractors/latest-news.extractor';
import { GalleryCardExtractor } from './data-extractors/gallery-card.extractor';
import { EbCountdownBlockExtractor } from './data-extractors/eb-countdown-block.extractor';
import { FastNewsExtractor } from './data-extractors/fast-news.extractor';
import { ServicesBoxExtractor } from './data-extractors/services-box.extractor';
import { RecipeCategorySelectExtractor } from './data-extractors/recipe-category-select.extractor';
import { TopTenTagsExtractor } from './data-extractors/top-ten-tags-extractor';
import { SponsoredVoteExtractor } from './data-extractors/sponsored-vote.extractor';

export const EXTRACTOR_CONFIG: DataExtractor<unknown>[] = [
  {
    extractor: EbCountdownBlockExtractor,
    supportedContentTypes: [LayoutElementContentType.COUNTDOWN_BOX],
  },
  {
    extractor: GalleryCardExtractor,
    isIterable: true,
    supportedContentTypes: [LayoutElementContentType.Gallery],
  },
  {
    extractor: LatestNewsExtractor,
    supportedContentTypes: [LayoutElementContentType.LATEST_NEWS],
  },
  {
    extractor: SponsoredVoteExtractor,
    supportedContentTypes: [LayoutElementContentType.SPONSORED_VOTE],
  },
  {
    extractor: ArticleArrayExtractor,
    supportedContentTypes: [
      LayoutElementContentType.ARTICLE_BLOCK,
      LayoutElementContentType.BRANDING_BOX_ARTICLE,
      LayoutElementContentType.GALLERY_ARTICLE_LIST,
      LayoutElementContentType.ARTICLES_WITH_VIDEO_CONTENT,
      LayoutElementContentType.ARTICLES_WITH_PODCAST_CONTENT,
      LayoutElementContentType.MOST_VIEWED,
      LayoutElementContentType.EB_NEWS,
      LayoutElementContentType.STAR_BIRTHS,
      LayoutElementContentType.COLUMN_BLOCK,
      LayoutElementContentType.LATEST_AND_MOST_READ_ARTICLES,
      LayoutElementContentType.TOP_COMMENTED_ARTICLES,
    ],
  },
  {
    extractor: ArticleCardExtractor,
    isIterable: true,
    supportedContentTypes: [LayoutElementContentType.Article, LayoutElementContentType.PrBlock, LayoutElementContentType.MinuteToMinute],
  },
  {
    extractor: FastNewsExtractor,
    isIterable: true,
    supportedContentTypes: [LayoutElementContentType.FastNews],
  },
  {
    extractor: OpinionWithMultipleAuthorsExtractor,
    isIterable: true,
    supportedContentTypes: [LayoutElementContentType.Opinion, LayoutElementContentType.WHERE_THE_BALL_WILL_BE],
  },
  {
    extractor: QuizExtractor,
    supportedContentTypes: [LayoutElementContentType.Quiz, LayoutElementContentType.SponsoredQuiz],
  },
  {
    extractor: VideoBlockExtractor,
    supportedContentTypes: [LayoutElementContentType.VideoBlock],
  },
  {
    extractor: VideoCardExtractor,
    isIterable: true,
    supportedContentTypes: [LayoutElementContentType.Video],
  },
  {
    extractor: DossierExtractor,
    supportedContentTypes: [LayoutElementContentType.Dossier, LayoutElementContentType.NEWS_FEED, LayoutElementContentType.DossierList],
  },
  {
    extractor: NoteExtractor,
    supportedContentTypes: [LayoutElementContentType.Note],
  },
  {
    extractor: TabsExtractor,
    supportedContentTypes: [LayoutElementContentType.Tabs],
  },
  {
    extractor: VoteExtractor,
    supportedContentTypes: [LayoutElementContentType.Vote],
  },
  {
    extractor: BreakingExtractor,
    supportedContentTypes: [LayoutElementContentType.Breaking],
  },
  {
    extractor: ImageExtractor,
    supportedContentTypes: [LayoutElementContentType.Image],
  },
  {
    extractor: WysiwygExtractor,
    supportedContentTypes: [LayoutElementContentType.Wysiwyg],
  },
  {
    extractor: PublicAuthorsExtractor,
    supportedContentTypes: [LayoutElementContentType.PUBLIC_AUTHORS],
  },
  {
    extractor: PodcastArrayExtractor,
    supportedContentTypes: [LayoutElementContentType.PODCAST_LIST],
  },
  {
    extractor: BrandingBoxExtractor, // Belső brandingbox, cikkajánlóval.
    supportedContentTypes: [LayoutElementContentType.BrandingBox],
  },
  {
    extractor: ServicesBoxExtractor,
    supportedContentTypes: [LayoutElementContentType.SERVICES_BOX],
  },
  {
    extractor: RecipeCategorySelectExtractor,
    supportedContentTypes: [LayoutElementContentType.RECIPE_CATEGORY_SELECT],
  },
  {
    extractor: TopTenTagsExtractor,
    supportedContentTypes: [LayoutElementContentType.TOP_TEN_TAGS],
  },
];
