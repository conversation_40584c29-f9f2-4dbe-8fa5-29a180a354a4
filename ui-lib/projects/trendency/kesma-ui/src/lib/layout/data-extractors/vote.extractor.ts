import { inject, Injectable } from '@angular/core';
import { DataExtractorFunction, LayoutDataExtractorService } from '../definitions';
import { LayoutElementContent, LayoutElementContentConfigurationVote, VoteDataWithAnswer } from '../../definitions';
import { backendVotingDataToVotingData } from '../../utils';
import { VoteService } from '../../services';

@Injectable()
export class VoteExtractor implements LayoutDataExtractorService<VoteDataWithAnswer | undefined> {
  private readonly voteService = inject(VoteService);

  extractData: DataExtractorFunction<VoteDataWithAnswer | undefined> = (element: LayoutElementContent) => {
    const conf = element.config as LayoutElementContentConfigurationVote;

    if (!conf || !conf?.selectedVote?.data) {
      return;
    }

    const voteData = backendVotingDataToVotingData(conf?.selectedVote?.data);
    const data = this.voteService.getVoteData(voteData);

    this.voteService.setVoteCache(data);

    return {
      data,
      meta: {
        extractedBy: VoteExtractor.name,
      },
    };
  };
}
