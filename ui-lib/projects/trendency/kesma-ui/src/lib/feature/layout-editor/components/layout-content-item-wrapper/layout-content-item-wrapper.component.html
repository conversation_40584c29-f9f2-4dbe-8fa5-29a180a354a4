<div *ngIf="isHovered$ | async" @fade class="item-options">
  <div class="item-options-top-bar">
    <div class="item-options-top-bar-title" [title]="contentTypeLabel || elementType" [class.small]="contentTypeLabel?.length > 20">
      {{ contentTypeLabel?.length ? contentTypeLabel : '(Ismeretlen komponens)' }}
    </div>
    <div class="item-options-top-bar-buttons" *ngIf="buttons && buttons.length > 0">
      <kesma-layout-content-item-wrapper-toolbar-button *ngFor="let button of buttons" [button]="button"></kesma-layout-content-item-wrapper-toolbar-button>
    </div>
  </div>
</div>
<ng-content></ng-content>
