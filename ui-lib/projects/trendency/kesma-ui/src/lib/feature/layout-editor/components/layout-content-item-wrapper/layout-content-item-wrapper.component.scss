:host {
  display: block;
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 10px;
  --kui-layout-editor-options-margin: 10px;
  --kui-layout-editor-options-color: #4c5865;
  --kui-layout-editor-options-border-width: 3px;
  .item-options {
    position: absolute;
    left: calc(0px - (var(--kui-layout-editor-options-margin) / 2));
    top: calc(0px - (var(--kui-layout-editor-options-margin) / 2));
    width: calc(100% + var(--kui-layout-editor-options-margin));
    height: calc(100% + var(--kui-layout-editor-options-margin));
    // It uses box shadow instead of border as it causes rendering issues because of some kind of rounding errors.
    box-shadow: 0 0 0 var(--kui-layout-editor-options-border-width) var(--kui-layout-editor-options-color);
    animation: pulse 2s infinite;
    z-index: 1001;
    &-top-bar {
      position: absolute;
      width: calc(100% + var(--kui-layout-editor-options-border-width) * 2);
      height: 40px;
      top: -40px;
      left: calc(var(--kui-layout-editor-options-border-width) * -1);
      display: flex;
      justify-content: space-between;
      align-content: center;
      &-title,
      &-buttons {
        background-color: var(--kui-layout-editor-options-color);
        padding: 10px 15px;
        color: #fff;
      }
      &-title {
        border-bottom-right-radius: 3px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        &.small {
          font-size: 14px;
        }
      }
      &-buttons {
        display: flex;
        justify-content: center;
        gap: 10px;
        border-bottom-left-radius: 3px;
        fill: #fff;
        kesma-icon {
          color: #fff;
        }

        &-item {
          &.no-click-fn {
            pointer-events: none;
            cursor: default;
          }
          display: flex;
          align-items: center;
          justify-content: center;
          flex-wrap: nowrap;
          transition: 0.2s all ease;
          color: #fff;
          font-weight: 600;
          &:hover {
            opacity: 0.7;
            transform: scale3d(1.1, 1.1, 1.1);
          }
          &:active {
            transform: scale3d(0.9, 0.9, 0.9);
          }
        }
      }
    }
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 var(--kui-layout-editor-options-border-width) rgba(0, 0, 0, 0);
  }
  50% {
    box-shadow: 0 0 0 var(--kui-layout-editor-options-border-width) var(--kui-layout-editor-options-color);
  }

  100% {
    box-shadow: 0 0 0 var(--kui-layout-editor-options-border-width) rgba(0, 0, 0, 0);
  }
}
