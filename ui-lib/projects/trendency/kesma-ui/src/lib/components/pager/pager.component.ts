import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { ActivatedRoute, Params, Router, RouterLink } from '@angular/router';
import { UtilService } from '@trendency/kesma-core';
import { Subject, takeUntil } from 'rxjs';
import { NgClass, NgIf } from '@angular/common';

const FIRST_PAGE = 1;

@Component({
  selector: 'kesma-pager',
  templateUrl: './pager.component.html',
  styleUrls: ['./pager.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgClass, RouterLink],
})
export class PagerComponent implements OnInit, OnChanges, OnDestroy {
  @Input() rowAllCount = 0;
  @Input() rowOnPageCount = 0;
  @Input() allowAutoScrollToTop = true;
  @Input() hasFirstLastButton = false;
  @Input() hasSkipButton = false;
  @Input() isListPager = false;
  @Input() isCountPager = false;
  @Input() showTotalPagesPositionAtRight = false;
  @Input() showFirstPage = false;
  @Input() showLastPage = false;
  @Input() scrollAfterNavigation = true;
  @Input() navType: 'navigation' | 'event' = 'navigation';
  @Input() currentPage = 1;
  @Input() removeDisabledElement = false;

  @Output() currentPageChange = new EventEmitter<number>();

  FIRST_PAGE = FIRST_PAGE;
  totalPages = 1;
  pages: number[] = [];
  isOpeningEllipses = false;
  isClosingEllipses = false;
  private destroy$: Subject<boolean> = new Subject<boolean>();
  #maxDisplayedPages = 0;

  constructor(
    public readonly router: Router,
    public readonly route: ActivatedRoute,
    private readonly utils: UtilService,
    private readonly cd: ChangeDetectorRef
  ) {}

  get maxDisplayedPages(): number {
    return this.#maxDisplayedPages;
  }

  @Input() set maxDisplayedPages(maxDisplayedPages: number) {
    this.#maxDisplayedPages = maxDisplayedPages;
    this.calculationLogic(this.currentPage);
  }

  get baseFragment(): string | undefined {
    return this.router.url.includes('#') ? (this.router.url.split('#').pop() ?? undefined) : undefined;
  }

  get baseRoute(): string {
    // remove all queryParams just to get the base route
    let url = this.router.url.split('?')[0].split('#').shift() ?? '';
    if (url.includes('%')) {
      url = decodeURI(url);
    }
    return url;
  }

  get skipLeft(): Params {
    const preFirst = this.pages[0] - 1;
    return this.getParams(preFirst);
  }

  get skipRight(): Params {
    const preLast = this.pages[this.pages.length - 1] + 1;
    return this.getParams(preLast);
  }

  get firstPage(): Params {
    return this.getParams(1);
  }

  get listParams(): Params[] {
    const params = this.getParams(undefined, true);
    return this.pages.map((page) => {
      return { ...params, page: page };
    });
  }

  get prevPage(): Params {
    const previousPage = this.currentPage - 1 === 0 ? this.FIRST_PAGE : this.currentPage - 1;

    return this.getParams(previousPage);
  }

  get nextPage(): Params {
    const nextPage = this.currentPage + 1;
    return this.getParams(nextPage);
  }

  get lastPage(): Params {
    return this.getParams(this.totalPages);
  }

  get isFirst(): boolean {
    return this.currentPage === 1;
  }

  get isLast(): boolean {
    return this.currentPage >= this.totalPages;
  }

  ngOnInit(): void {
    this.initState();
  }

  ngOnChanges({ rowAllCount, rowOnPageCount, currentPage }: SimpleChanges) {
    if (!currentPage?.isFirstChange() && [rowAllCount, rowOnPageCount, currentPage].some((field) => field?.previousValue !== field?.currentValue)) {
      this.initState();
    }

    if (this.totalPages) {
      this.onTotalPagesInput();
    }

    if (this.currentPage) {
      this.onPageNoInput();
    }

    if (this.isOutOfRange(this.currentPage)) {
      this.currentPage = this.totalPages;
    }
  }

  ngOnDestroy() {
    this.destroy$.next(true);
    this.destroy$.complete();
  }

  getParams(page?: number, isPageExcluded?: boolean): Params {
    let link: any;

    this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe((queryParams: Params) => {
      link = isPageExcluded ? queryParams : { ...queryParams, page: page };
    });

    return link;
  }

  onPageClick(page: number) {
    if (this.currentPage === page) {
      return;
    }
    this.currentPage = page;
    this.currentPageChange.emit(this.currentPage);
    this.cd.markForCheck();
  }

  onFirstPageClick() {
    this.onPageClick(this.FIRST_PAGE);
  }

  onPrevClick() {
    if (this.currentPage <= 1) {
      return;
    }
    this.onPageClick(this.currentPage - 1);
  }

  onSkipLeftClick() {
    if (this.currentPage <= 1) {
      return;
    }
    this.onPageClick(this.pages[0] - 1);
  }

  onSkipRightClick() {
    if (this.currentPage >= this.totalPages) {
      return;
    }

    this.onPageClick(this.pages[this.pages.length - 1] + 1);
  }

  onLastPageClick() {
    this.onPageClick(this.totalPages);
  }

  onNextClick() {
    if (this.currentPage >= this.totalPages) {
      return;
    }

    this.onPageClick(this.currentPage + 1);
  }

  private initState() {
    this.totalPages = Math.ceil(this.rowAllCount / this.rowOnPageCount);

    this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe((params) => {
      if (this.navType !== 'event') {
        const page = params['page'];
        this.currentPage = page ? parseInt(page, 10) : this.FIRST_PAGE;
      }

      this.calculationLogic(this.currentPage);

      if (this.scrollAfterNavigation) {
        this.scrollTopAfterChange();
      }
      this.cd.markForCheck();
    });
  }

  private calculationLogic(pageNo: number) {
    if (!this.isListPager) {
      return;
    }

    this.pages = this.calculateVisiblePages(pageNo, this.totalPages, this.maxDisplayedPages);
    const [isOpeningEllipses, isClosingEllipses] = this.calculateIsEllipses(pageNo, this.totalPages, this.maxDisplayedPages);
    this.isOpeningEllipses = isOpeningEllipses;
    this.isClosingEllipses = isClosingEllipses;
  }

  private calculateVisiblePages(current: number, total: number, max: number): number[] {
    const pageNumbers = [];
    const half = Math.floor(max / 2);
    const to = current + half >= total ? total : current > half ? current + half : Math.min(max, total);

    const from = to - max >= 0 ? to - max : 0;
    let i = from;
    while (i <= to - 1) {
      pageNumbers.push(i + 1);
      i += 1;
    }
    return pageNumbers;
  }

  private calculateIsEllipses(current: number, total: number, max: number): [boolean, boolean] {
    const halfWay = Math.ceil(max / 2);
    const isStart = current <= halfWay;

    const isEnd = current >= total - Math.floor(max / 2);
    const isEllipses = max < total;
    const isMiddle = !isEnd && !isStart;

    let isOpeningEllipses = false;
    let isClosingEllipses = false;

    if (isEllipses) {
      isOpeningEllipses = isMiddle || isEnd;
      isClosingEllipses = isMiddle || isStart;
    }
    return [isOpeningEllipses, isClosingEllipses];
  }

  private scrollTopAfterChange() {
    if (this.allowAutoScrollToTop && this.utils.isBrowser()) {
      window.scrollTo(0, 0);
    }
  }

  private isOutOfRange(val: number): boolean {
    return val > this.totalPages;
  }

  private onTotalPagesInput() {
    if (typeof this.totalPages !== 'number') {
      this.totalPages = 1;
    }
  }

  private onPageNoInput() {
    if (typeof this.currentPage !== 'number' || this.currentPage < 1 || this.currentPage > this.totalPages) {
      this.currentPage = 1;
    }
  }
}
