@use '../../../scss/shared' as *;

.hidden-container {
  position: absolute;
  visibility: hidden;
  opacity: 0;
}

.ad-wrapper {
  text-align: center;
}

.advertisement-block {
  margin: 0 auto;
  display: table;
}

.ad-parent {
  min-height: 0px;
}

section {
  &.hidden {
    visibility: hidden;
    margin: 0;
  }
}

/* Debug mode */
:host.ad-debug .ad-wrapper {
  background-color: #4caf50;
  outline: 2px solid #2e7d32;
}
