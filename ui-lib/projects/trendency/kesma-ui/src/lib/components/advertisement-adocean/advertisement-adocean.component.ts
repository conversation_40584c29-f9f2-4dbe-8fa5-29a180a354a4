import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  HostBinding,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges,
  SkipSelf,
  ViewChild,
} from '@angular/core';

import { Ng<PERSON><PERSON>, NgIf, NgStyle } from '@angular/common';
import { UtilService } from '@trendency/kesma-core';
import { BehaviorSubject, Subject, timer } from 'rxjs';
import { distinctUntilChanged, filter, map, takeUntil } from 'rxjs/operators';
import { Advertisement, SlaveConfig } from '../../definitions';
import { AdvertisementAdoceanStoreService } from '../../services';

@Component({
  selector: 'kesma-advertisement-adocean',
  templateUrl: './advertisement-adocean.component.html',
  styleUrls: ['./advertisement-adocean.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgClass, NgStyle],
})
export class AdvertisementAdoceanComponent implements On<PERSON>nit, OnChanges, OnDestroy {
  @Input() ad?: Omit<Advertisement, 'element'>;
  @Input() config?: SlaveConfig;
  @Input() isHidden = false;
  @Input() isExceptionAdvertEnabled = false;
  /*
  isWidthCheckingDisabled - disables hard screen width checker. both advert sizes will render, but gives room to custom
  media queries via css
  */
  @Input() isWidthCheckingDisabled = false;
  @Input() isTraditionalAd?: boolean;
  @Input() hasNoParentHeight?: boolean;
  @Input() style: Record<string, string> | null = null;
  @HostBinding('class.empty-zone') hasEmptyClass = false;
  @HostBinding('class.ad-debug') adDebugClass = false;
  @ViewChild('advertContainer') advertContainer?: ElementRef;

  isAdvertDelayed$ = new BehaviorSubject(true);

  public enableAds: boolean = true;
  public isEmpty$ = this.adStoreAdo.emptyAdReceived$.pipe(
    filter((zoneId) => zoneId === this.ad?.zonaId),
    map((zoneId) => !!zoneId)
  );

  private destroy$ = new Subject<boolean>();

  constructor(
    private readonly utilsService: UtilService,
    private readonly adStoreAdo: AdvertisementAdoceanStoreService,
    // You need to import the ChangeDetectorRef with @SkipSelf to make the updates in the parent.
    // This needed for the empty-zone class representation to work.
    @SkipSelf() private readonly cd: ChangeDetectorRef
  ) {}

  public localAdDebug = false;

  ngOnInit(): void {
    if (!this.utilsService.isBrowser()) {
      this.enableAds = false;
      this.hasEmptyClass = false;
      return;
    }

    this.getLocalStorageDebug();
    this.onAdvertHidden();

    this.adStoreAdo.enableAds$.pipe(distinctUntilChanged()).subscribe((res) => {
      this.enableAds = this.isExceptionAdvertEnabled || res;
      this.cd.detectChanges();
    });
    this.isEmpty$.pipe(takeUntil(this.destroy$)).subscribe((isEmpty) => {
      this.hasEmptyClass = isEmpty;

      this.cd.detectChanges();
    });
  }

  ngOnChanges({ ad }: SimpleChanges) {
    if (ad.previousValue?.zonaId === ad.currentValue?.zonaId) {
      return;
    }
    if (!this.utilsService.isBrowser()) {
      return;
    }

    queueMicrotask(() => {
      setTimeout(() => {
        this.initAD();
      }, 1000);
      this.cd.markForCheck();
    });
  }

  /* Get adDebug value from localStorage */
  getLocalStorageDebug(): void {
    this.localAdDebug = localStorage.getItem('adDebug') === 'true';
    this.adDebugClass = this.localAdDebug;
  }

  advertDelayer() {
    if (this.isHidden || !this.ad) return;

    const checkIfHasAdvert$ = timer(1500).pipe(
      takeUntil(this.isAdvertDelayed$.pipe(filter((isAdvertDelayed) => !isAdvertDelayed))),
      takeUntil(this.destroy$)
      // takeUntil(this.isEmpty$)
    );

    checkIfHasAdvert$.subscribe(() => {
      if (this.advertContainer?.nativeElement) {
        this.advertContainer.nativeElement.classList.remove('hidden-container');
        // this.cd.detectChanges();
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
  }

  onAdvertHidden() {
    if (!this.utilsService.isBrowser() || !this.ad || this.isWidthCheckingDisabled) {
      return;
    }

    const isMobile = window?.innerWidth <= 768;

    if ((isMobile && this.ad.medium === 'desktop') || (!isMobile && this.ad.medium === 'mobile')) {
      this.isHidden = true;
    }
  }

  initAD() {
    if (!this.utilsService.isBrowser() || !this.ad || this.isHidden) {
      return;
    }

    if (this.isTraditionalAd || this.hasTraditionalAdCode(this.ad)) {
      this.adStoreAdo.initTraditionalAdvertisement(this.ad);
    } else {
      this.adStoreAdo.initSlaveAdvertisement(this.ad, this.config, this.isExceptionAdvertEnabled);
    }

    /* We need this to avoid the flickering when the advert loads,
     and want to replace the minimum height of the component as well */
    this.advertDelayer();
  }

  hasTraditionalAdCode(ad: Advertisement) {
    const isTraditional = ad.zonaId.includes('ado-') && !ad.masterId;
    return isTraditional;
  }
}
