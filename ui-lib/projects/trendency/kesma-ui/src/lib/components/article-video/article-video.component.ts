import { ChangeDetectionStrategy, Component, inject, Input, OnChanges, OnInit } from '@angular/core';
import { VideoComponentObject } from './article-video.definitions';
import { BaseComponent } from '../base/base.component';
import { UtilService } from '@trendency/kesma-core';
import { BypassPipe } from '../../pipes';
import { NgIf } from '@angular/common';

@Component({
  selector: 'kesma-article-video',
  templateUrl: './article-video.component.html',
  styleUrls: ['./article-video.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, BypassPipe],
})
export class ArticleVideoComponent extends BaseComponent<VideoComponentObject> implements OnInit, OnChanges {
  @Input() useCoverThumbnail: boolean = false;
  @Input() coverImgThumbnail: string = 'assets/images/placeholder.svg';

  public videoUrl: string = '';
  public coverThumbnailIsClicked: boolean = false;
  public autoPlay = false;

  private readonly utilService = inject(UtilService);

  override ngOnInit(): void {
    super.ngOnInit();
    this.videoUrl = this.data?.videaUrl ?? '';
  }

  ngOnChanges() {
    this.videoUrl = this.data?.videaUrl ?? '';
  }

  getUrl(): string {
    if (!this.utilService.isBrowser()) {
      return '';
    }
    if (this.videoUrl.includes('videa')) {
      return this.getVideaUrl();
    }
    if (this.videoUrl.match(/\/\/(youtu.be|www.youtube.com)/)) {
      return this.getYoutubeUrl();
    }
    if (this.videoUrl.includes('facebook.com')) {
      return this.videoUrl;
    }
    return '';
  }

  play(): void {
    this.coverThumbnailIsClicked = true;
    this.autoPlay = true;
  }

  private getVideaUrl(): string {
    let videaUrl = '';
    if (this.videoUrl.startsWith('//videa.hu/player?v=')) {
      videaUrl = this.videoUrl;
    } else {
      const urlSplit = this.videoUrl.split('-');
      videaUrl = `//videa.hu/player?v=${urlSplit[urlSplit.length - 1]}`;
    }
    return `${videaUrl}${this.useCoverThumbnail ? '&autoplay=1' : ''}`;
  }

  private getYoutubeUrl(): string {
    const videoId = this.extractVideoId(this.videoUrl);
    return videoId ? `https://www.youtube.com/embed/${videoId}${this.useCoverThumbnail ? '?autoplay=1' : ''}` : '';
  }

  private extractVideoId(url: string) {
    // Youtube video id extract regex
    const regExp = /(?:\?v=|\/embed\/|\/watch\?v=|\/)([a-zA-Z0-9_-]{11})/;
    const match = url.match(regExp);
    return match && match[1];
  }
}
