import { ChangeDetectionStrategy, Component, computed, input, TemplateRef } from '@angular/core';
import { Ng<PERSON>tyle, NgTemplateOutlet } from '@angular/common';
import { ArticleCard, VariableDidYouKnowBox } from '../../definitions';
import { mapBackendArticleDataToArticleCard } from '../../utils';

@Component({
  selector: 'kesma-variable-sponsored-did-you-know',
  templateUrl: './variable-sponsored-did-you-know.component.html',
  styleUrls: ['./variable-sponsored-did-you-know.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [NgStyle, NgTemplateOutlet],
})
export class VariableSponsoredDidYouKnowComponent {
  readonly data = input.required<VariableDidYouKnowBox>();
  readonly article = computed<ArticleCard | undefined>(() => {
    const articles = this.data()?.articles;
    if (!articles?.length) return;
    const [article] = articles;
    return mapBackendArticleDataToArticleCard(article);
  });
  readonly articleTemplate = input<TemplateRef<{ $implicit: ArticleCard }> | undefined>(undefined);
}
