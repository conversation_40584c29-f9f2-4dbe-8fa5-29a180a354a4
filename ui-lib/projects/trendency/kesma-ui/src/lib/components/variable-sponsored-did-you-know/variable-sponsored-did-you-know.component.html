@if (data(); as box) {
  <div class="box-wrapper">
    <div
      class="box-header"
      [ngStyle]="{
        'background-color': box.headerBackgroundColor || undefined,
        'background-image': box.headerBackgroundImage ? 'url(' + box.headerBackgroundImage.fullSizeUrl + ')' : undefined,
      }"
    >
      <ng-template #logoImg>
        <img [src]="box.logoImage?.fullSizeUrl" [style.width.px]="box.logoWidth" [style.height.px]="box.logoHeight" alt="Logo" />
      </ng-template>
      @if (box.logoUrl && box.logoImage?.fullSizeUrl) {
        <a [href]="box.logoUrl" target="_blank">
          <ng-container *ngTemplateOutlet="logoImg"></ng-container>
        </a>
      } @else if (box.logoImage?.fullSizeUrl) {
        <ng-container *ngTemplateOutlet="logoImg"></ng-container>
      }
    </div>
    <div
      class="box-body"
      [ngStyle]="{
        color: box.didYouKnowTextColor || '#000',
        'background-color': box.didYouKnowBackgroundColor || undefined,
        'background-image': box.boxBackgroundImage ? 'url(' + box.boxBackgroundImage.fullSizeUrl + ')' : undefined,
      }"
    >
      <h2 class="box-name" [ngStyle]="{ color: box.boxNameColor || '#000' }">{{ box.boxName }}</h2>
      @if (box.didYouKnowDescription) {
        <p class="description">{{ box.didYouKnowDescription }}</p>
      }
      @if (box.texts[0]?.title) {
        <p class="description">{{ box.texts[0].title }}</p>
      }
      @if (article() && articleTemplate()) {
        <p class="article-wrapper" style="--kui-variable-sponsored-did-you-know-title-color: {{ box.didYouKnowTextColor || '#000' }}">
          <ng-container *ngTemplateOutlet="articleTemplate(); context: { $implicit: article() }"></ng-container>
        </p>
      }
    </div>
    <div
      class="box-footer"
      [ngStyle]="{
        color: box.footerTextColor || '#000',
        'background-color': box.footerBackgroundColor || undefined,
        'background-image': box.footerImage ? 'url(' + box.footerImage.fullSizeUrl + ')' : undefined,
      }"
    >
      @if (box.footerText?.length) {
        <a [href]="box.footerUrl" target="_blank">
          <div class="footer-text">{{ box.footerText }}</div>
        </a>
      }
    </div>
  </div>
  @if (box.isAdTextVisible) {
    <div class="ad-text">Hirdetés</div>
  }
}
