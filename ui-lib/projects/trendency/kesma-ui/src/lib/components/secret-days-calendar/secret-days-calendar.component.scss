@use 'shared' as *;

.calendar-wrapper {
  display: flex;
  flex-direction: column;
  background-size: cover;
  background-position: center;
  width: 100%;
  padding: 12px;
}

.calendar-sponsorship {
  display: flex;
  gap: 16px;
  align-items: center;
  justify-content: center;
}

.calendar-days-wrapper {
  display: flex;
  gap: 16px;
  height: 100%;
  background-size: cover;
  background-position: center;
}

.calendar-day-list {
  padding: 12px;
  display: flex;
  width: 100%;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: space-around;
}

.calendar-day {
  cursor: pointer;
  padding: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-size: cover;
  background-position: center;
  min-width: 50px;
}

.secret-day-modal {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 10000;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;

  &-content {
    width: max-content;
    max-height: 80vh;
    max-width: 80vw;
    background-color: var(--kui-white);
    padding: 15px;
    overflow-y: auto;
    margin: 30px;

    @include media-breakpoint-down(md) {
      max-width: 100%;
      margin: 15px;
    }
  }
}
