import {
  Article,
  ArticleCard,
  BackendArticleSearchResult,
  BackendAuthorArticle,
  BackendRecommendedArticle,
  BasicDossier,
  ExternalRecommendation,
  FakeBool,
  PersonalizedRecommendationArticle,
  PreviewBackendArticle,
  PrimaryColumnColorCombo,
  SubsequentDossier,
} from '../definitions';
import { toBool } from './bool.utils';
import { backendDateToDate } from './common-utils';

export const previewBackendArticleToArticleCard = (data: PreviewBackendArticle): ArticleCard => {
  const {
    id,
    slug,
    title,
    publishDate,
    column,
    preTitle,
    excerpt,
    thumbnailUrl,
    lead,
    primaryColumn,
    status,
    dbcache,
    publicAuthor,
    foundationTagSlug,
    foundationTagTitle,
    thumbnailUrlFocusedImages,
  } = data;

  let publishYear: string | number;
  let publishMonth: string | number;
  const publishDateFromBackend: Date | undefined =
    publishDate && publishDate !== '' ? (backendDateToDate(typeof publishDate === 'string' ? publishDate : publishDate.date) as Date) : undefined;
  publishYear = publishDateFromBackend ? publishDateFromBackend?.getUTCFullYear() : '';
  publishMonth = (publishDateFromBackend?.getUTCMonth() || 0) + 1;

  return {
    id,
    slug,
    title,
    preTitle,
    excerpt,
    publishDate: publishDateFromBackend ?? '',
    columnTitle: column?.title,
    lead: excerpt ? excerpt : lead,
    columnSlug: column?.slug || primaryColumn?.slug,
    thumbnail:
      (thumbnailUrl && {
        url: thumbnailUrl,
      }) ||
      undefined,
    publishYear,
    publishMonth,
    primaryColumnColorCombo: getPrimaryColumnColorComboByColumnTitleColor(column?.titleColor ?? ''),
    category: {
      slug: column?.slug || primaryColumn?.slug,
      name: column?.title || primaryColumn?.title,
    },
    label: {
      text: 'Ezt ne hagyja ki!',
    },
    isOpinion: status?.opinionType,
    author: {
      name: dbcache?.publicAuthor ?? publicAuthor ?? '',
      avatarUrl: dbcache?.publicAuthorAvatarThumbnailUrl ?? '',
    },
    articleMedium: status?.articleMedium,
    isAdultsOnly: status?.isAdultsOnly,
    isVideoType: dbcache?.isVideoType,
    isPodcastType: dbcache?.isPodcastType,
    hasGallery: dbcache?.hasGallery,
    foundationTagSlug: foundationTagSlug,
    foundationTagTitle: foundationTagTitle,
    thumbnailFocusedImages: thumbnailUrlFocusedImages,
    recommendedTitle: dbcache?.recommendedTitle,
  };
};

export const getPrimaryColumnColorComboByColumnTitleColor = (columnTitleColor: string | null): PrimaryColumnColorCombo => {
  const combinations = new Map()
    .set('#FFB800', '#000000')
    .set('#EB0000', '#FFFFFF')
    .set('#8629CC', '#FFFFFF')
    .set('#FD82B4', '#000000')
    .set('#56C9FF', '#000000')
    .set('#FF913F', '#000000')
    .set('#45F0C8', '#000000')
    .set('#C4C8D1', '#000000')
    .set('#6DDE4A', '#000000')
    .set('#000EEB', '#FFFFFF');

  if (columnTitleColor) {
    const matchingTextColor = combinations.get(columnTitleColor);
    if (matchingTextColor) {
      return { background: columnTitleColor, color: matchingTextColor };
    } else {
      return {} as PrimaryColumnColorCombo;
    }
  } else {
    return {} as PrimaryColumnColorCombo;
  }
};

/**
 * Maps articles from the backend author page to ArticleCards.
 * @param {BackendAuthorArticle} backendAuthorArticle - Article from the backend.
 * @returns {ArticleCard} ArticleCard input.
 */
export const mapBackendAuthorArticleToArticleCard = (backendAuthorArticle: BackendAuthorArticle): ArticleCard => {
  return {
    title: backendAuthorArticle.title,
    slug: backendAuthorArticle.slug,
    category: {
      name: backendAuthorArticle.columnTitle,
      slug: backendAuthorArticle.columnSlug,
    },
    columnTitle: backendAuthorArticle.columnTitle,
    columnSlug: backendAuthorArticle.columnSlug,
    thumbnailUrl: backendAuthorArticle.thumbnail,
    thumbnail: {
      url: backendAuthorArticle.thumbnail,
    },
    publishDate: backendAuthorArticle.publishDate,
    regions: backendAuthorArticle.regions,
    regionSlug: backendAuthorArticle.regions?.[0]?.slug,
    regionTitle: backendAuthorArticle.regions?.[0]?.title,
    lead: backendAuthorArticle.lead,
    isVideoType: backendAuthorArticle.isVideo === '1' || backendAuthorArticle.isVideo === 1,
    foundationTagSlug: backendAuthorArticle.foundationTagSlug,
    foundationTagTitle: backendAuthorArticle.foundationTagTitle,
  };
};

export const mapBackendArticleDataToArticleCardWithHideThumbnail = (data: BackendArticleSearchResult): ArticleCard => ({
  ...mapBackendArticleDataToArticleCard(data),
  thumbnail: {
    url: !toBool(data.hideThumbnailFromBody) ? (data.thumbnail ?? '') : '',
  },
});

export const mapBackendArticleDataToArticleCard = (data: BackendArticleSearchResult): ArticleCard =>
  data &&
  ({
    ...data,
    id: data.id,
    title: data.title ?? '',
    preTitle: data.preTitle,
    contentType: data.contentType?.toString() || '',
    slug: data.slug,
    columnSlug: data.columnSlug ?? (data as unknown as Article).primaryColumn?.slug,
    columnTitle: data.columnTitle ?? (data as unknown as Article).primaryColumn?.title,
    publishDate: data.publishDate,
    length: data.length ? +data.length : undefined,
    lead: data.lead || data.chLead,
    excerpt: data.excerpt || data.chExcerpt,
    thumbnail: {
      url: data.thumbnail ?? '',
    },
    author: {
      name: data.publicAuthor ?? data.author ?? '',
      avatarUrl: data.avatarImage ?? data.avatar ?? '',
      slug: data?.authorSlug ?? '',
    },
    publicAuthorM2M: data.publicAuthorM2M,
    category: {
      slug: data.columnSlug ?? (data as unknown as Article).primaryColumn?.slug,
      name: data.columnTitle ?? (data as unknown as Article).primaryColumn?.title,
    },
    tag: data.tag,
    tags: data?.tags,
    regions: data.regions,
    firstTagId: data.firstTagId,
    firstRegionId: data.firstRegionId,
    isVideoType: data.isVideo ? !!+data.isVideo : false,
    isPodcastType: !!+data.isPodcastType,
    isAdultsOnly: !!+data.isAdultsOnly,
    hasGallery: data.hasGallery ? !!+data.hasGallery : false,
    hasDossiers: data.hasDossiers ? !!+data.hasDossiers : false,
    articleMedium: data.articleMedium,
    likeCount: data.likeCount,
    dislikeCount: data.dislikeCount,
    commentCount: data.commentCount,
    isCommentsDisabled: data.isCommentsDisabled,
    isLikesAndDislikesDisabled: data.isLikesAndDislikesDisabled,
    isPaywalled: data.isPaywalled ? !!+data.isPaywalled : false,
    foundationTagSlug: data.foundationTagSlug,
    foundationTagTitle: data.foundationTagTitle,
    columnEmphasizeOnArticleCard: data.columnEmphasizeOnArticleCard,
    isGuaranteeType: data?.isGuaranteeType === '1',
    thumbnailFocusedImages: data?.thumbnailFocusedImages,
    avatarImageFocusedImages: data?.avatarImageFocusedImages,
    brand: data?.brand,
  } as ArticleCard);

export const externalRecommendationToArticleCard = (externalRecommendation: ExternalRecommendation): ArticleCard => ({
  id: externalRecommendation.spr,
  title: externalRecommendation.title,
  columnTitle: externalRecommendation.siteName,
  category: {
    name: externalRecommendation.siteName,
    slug: undefined,
  },
  thumbnail: externalRecommendation.imagePath
    ? {
        url: externalRecommendation.imagePath,
      }
    : undefined,
  slug: undefined,
  publishDate: undefined,
  label: {
    text: externalRecommendation.siteName,
    url: externalRecommendation.siteName ? `https://${externalRecommendation.siteName.replace(/^https?:\/\//, '')}` : undefined,
  },
  publishYear: undefined,
  publishMonth: undefined,
  url: externalRecommendation.url,
});

export const backendRecommendedArticleToArticleCard = ({
  id,
  slug,
  title,
  publishDate,
  thumbnailUrl,
  thumbnail,
  excerpt,
  readingLength,
  columnTitle,
  columnTitleColor,
  titleColor,
  columnSlug,
  preTitle,
  preTitleColor,
  regions,
  tags,
  foundationTagSlug,
  foundationTagTitle,
}: BackendRecommendedArticle): ArticleCard => {
  const [publishYear, publishMonth] = publishDate.split('-');
  return {
    id,
    title,
    slug,
    regions,
    tags,
    publishDate: backendDateToDate(publishDate) as Date,
    publishMonth,
    publishYear,
    thumbnail:
      thumbnailUrl || thumbnail
        ? {
            url: thumbnailUrl || thumbnail || '',
          }
        : undefined,
    category: {
      name: columnTitle,
      slug: columnSlug,
    },
    readingTime: readingLength?.toString(),
    columnTitle: columnTitle,
    preTitle: preTitle,
    primaryColumnColorCombo:
      columnTitleColor || columnTitleColor ? getPrimaryColumnColorComboByColumnTitleColor(columnTitleColor ? columnTitleColor : (titleColor ?? '')) : undefined,
    columnSlug,
    preTitleColor,
    lead: excerpt,
    label: {
      text: preTitle ?? '',
    },
    foundationTagSlug,
    foundationTagTitle,
  };
};
export const subsequentDossierToBasicDossier = ({
  slug,
  title,
  coverImage: thumbnailUrl,
  relatedArticles: articles,
}: SubsequentDossier): BasicDossier<Date> | undefined =>
  slug
    ? {
        slug,
        title,
        thumbnailUrl,
        articles: articles?.map((article) => ({
          ...article,
          publishDate: backendDateToDate(article.publishDate) as Date,
        })),
      }
    : undefined;

export const mapPersonalizedRecommendationToArticleCard = (personalizedRecommendationArticle: PersonalizedRecommendationArticle): ArticleCard => ({
  id: personalizedRecommendationArticle.campaign_article_id
    ? `${personalizedRecommendationArticle.id}/${personalizedRecommendationArticle.campaign_article_id}`
    : `${personalizedRecommendationArticle.id}`,
  title: personalizedRecommendationArticle.title,
  columnTitle: personalizedRecommendationArticle.domain,
  category: {
    name: personalizedRecommendationArticle.domain,
    slug: undefined,
  },
  thumbnail: personalizedRecommendationArticle.image
    ? {
        url: personalizedRecommendationArticle.image,
      }
    : undefined,
  slug: undefined,
  publishDate: personalizedRecommendationArticle.pub_date,
  label: {
    text: personalizedRecommendationArticle.domain,
    url: personalizedRecommendationArticle.domain_url ? `https://${personalizedRecommendationArticle.domain_url.replace(/^https?:\/\//, '')}` : undefined,
  },
  publishYear: undefined,
  publishMonth: undefined,
  url: personalizedRecommendationArticle.url,
  isAdultsOnly: personalizedRecommendationArticle.is_adult === 1,
  isVideo: `${personalizedRecommendationArticle.is_video}` as FakeBool,
  lead: personalizedRecommendationArticle.head,
  author: {
    name: personalizedRecommendationArticle.author,
  },
});
