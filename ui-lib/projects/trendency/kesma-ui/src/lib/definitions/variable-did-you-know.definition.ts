import { BackendArticleSearchResult } from './article-card.definitions';

export type VariableDidYouKnowBoxImage = {
  id: string;
  variantId: number;
  fullSizeUrl: string;
};
export type SerializedPhpDateTime = {
  date: string;
  timezone_type: 1 | 2 | 3;
  timezone: string;
};
export type VariableDidYouKnowBox = {
  id: string;
  title: string;
  isActive: boolean;
  logoImage?: VariableDidYouKnowBoxImage | null;
  logoUrl?: string;
  didYouKnowDescription?: string;
  didYouKnowTextColor?: string;
  didYouKnowBackgroundColor?: string;
  description?: string;
  footerImage?: VariableDidYouKnowBoxImage | null;
  footerUrl?: string;
  createdAt: SerializedPhpDateTime;
  adOceanId?: string;
  logoWidth?: number;
  logoHeight?: number;
  headerBackgroundColor?: string;
  headerBackgroundImage?: VariableDidYouKnowBoxImage | null;
  boxName?: string;
  boxNameColor?: string;
  boxBackgroundImage?: VariableDidYouKnowBoxImage | null;
  articles?: [BackendArticleSearchResult];
  texts?: [
    {
      title: string;
    },
  ];
  footerText?: string;
  footerTextColor?: string;
  footerBackgroundColor?: string;
  isAdTextVisible: boolean;
};
