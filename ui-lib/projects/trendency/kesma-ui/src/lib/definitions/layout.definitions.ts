import { LayoutDataExtractor } from '../utils';
import { ApiResponseMeta } from './api-result';
import { ArticleBody, ArticleCard, Author, MinuteToMinuteState, PublicMultiAuthor, Region, Sponsorship, Tag } from './article-card.definitions';
import { FakeBool } from './common.definitions';
import { DossierData } from './dossier-card.definitions';
import {
  LayoutElement,
  LayoutElementContent,
  LayoutElementContentConfiguration,
  LayoutElementRow,
  LayoutStruct,
  OverwriteImage,
} from './layout/layout-elements';
import { FocusPointUrlWithAspectRatio } from './focus-point.definitions';

export interface LayoutContentParams {
  layoutElement: LayoutElementContent;
  index: number;
  extractor: LayoutDataExtractor;
  desktopWidth?: number;
  first?: boolean;
  last?: boolean;
  backgroundColor?: string;
}

export interface BlockHeaderTemplateParams {
  layoutElement: LayoutElementContent;
  layoutType: LayoutPageType;
  desktopWidth?: number;
  backgroundColor?: string;
}

export type LayoutApiData = Readonly<{
  struct: LayoutElementRow[];
  content: LayoutElementContentConfiguration[];
  type?: LayoutPageType;
}>;

export enum LayoutElementType {
  Row = 'row',
  Column = 'column',
  Content = 'content',
}

export enum LayoutPageType {
  HOME = 'HomePage',
  COLUMN = 'Column',
  OPINION = 'Opinion',
  SIDEBAR = 'Sidebar',
}

export enum LayoutElementContentType {
  Article = 'article',
  Ad = 'ad',
  StockChart = 'stock',
  Opinion = 'opinion',
  OpinionList = 'opinion-list',
  OpinionBlock = 'opinion-block',
  INGATLANBAZAR = 'ingatlanbazar',
  Video = 'video',
  Dossier = 'dossier',
  PodcastBlock = 'podcast-block',
  VideoBlock = 'video-block',
  NewsletterBlock = 'newsletter-block',
  NewsletterBlockGong = 'newsletter-block-gong',
  FreshBlock = 'fresh-block',
  FreshNews = 'fresh-news',
  FastNews = 'fast-news',
  LinkList = 'link-list',
  Vote = 'vote',
  Gallery = 'gallery',
  BrandingBox = 'branding-box',
  VisegradPost = 'visegrad-post',
  CultureNation = 'culture-nation',
  HtmlEmbed = 'html-embed',
  TabsBlock = 'tab',
  Breaking = 'breaking',
  Note = 'note',
  Wysiwyg = 'wysiwyg',
  Image = 'image',
  Astrology = 'astrology',
  Tabs = 'tabs',
  PrBlock = 'pr-block',
  Quiz = 'quiz',
  SponsoredQuiz = 'sponsored-quiz',
  TrendingTagsBlock = 'trending-tags-block',
  TagBlock = 'tag-block',
  KompostBlock = 'kompost-block',
  YessfactorBlock = 'yessfactor-block',
  BrownRecommender = 'brown-recommender',
  BestRecommender = 'best-recommender',
  FinalCountdown = 'final-countdown',
  HelloBudapest = 'hello-budapest',
  koponyeg = 'koponyeg',
  IngatlanbazarSearch = 'ingatlanbazar-search',
  IngatlanbazarConfigurable = 'ingatlanbazar-configurable',
  DossierList = 'dossier-list',
  BrandingBoxEx = 'branding-box-ex',
  CategoryStepper = 'category-stepper',
  WeeklyNewspaperBox = 'weekly-newspaper-box',
  ARTICLES_WITH_VIDEO_CONTENT = 'articles-with-video-content',
  ARTICLES_WITH_PODCAST_CONTENT = 'articles-with-podcast-content',
  PODCAST_LIST = 'podcast-list',
  GALLERY_ARTICLE_LIST = 'gallery-article-list',
  PdfBox = 'pdf-box',
  DossierRepeater = 'dossier-repeater',
  MANUAL_OPINION = 'manual-opinion',
  MANUAL_ARTICLE = 'manual-article',
  RSS_BOX = 'rss-box',
  MAP_RECOMMENDATIONS = 'map-recommendations',
  HERO = 'hero',
  MEDICAL_METEOROLOGY = 'medical-meteorology',
  TWELVE_DAYS_FORECAST = 'twelve-days-forecast',
  NEWSPAPER = 'newspaper',
  DETECTIONS = 'detections',
  NEWS_FEED = 'news-feed',
  DATA_BANK = 'data-bank',
  SPORT_RADIO_PLAYER = 'sport-radio-player',
  LEAD_EDITORS = 'lead-editors',
  TEAMS = 'teams',
  TRIP_BOX = 'trip-box',
  UPCOMING_MATCHES = 'upcoming-matches',
  CHAMPIONSHIP_TABLE = 'championship-table',
  SOCIAL_MEDIA = 'social-media',
  MEDIA_PANEL = 'media-panel',
  MOST_VIEWED = 'most-viewed',
  IMAGE_MAP_LIST = 'image-map-list',
  DRAWN_MAP_LIST = 'drawn-map-list',
  TOP_STORIES = 'top-stories',
  GP_NEWS_BOX = 'gp-news-box',
  SPORT_BLOCK = 'sport-block',
  MORE_ARTICLES = 'more-articles',
  RELATED_ARTICLES = 'related-articles',
  RECIPE_CATEGORY_SELECT = 'recipe-category-select',
  MAESTRO_BOX = 'maestro-box',
  TEXT_BOX = 'text-box',
  ARTICLE_SLIDER = 'article-slider',
  TURPI_BOX = 'turpi-box',
  AUTHOR = 'author',
  TURPI_CARD = 'turpi-card',
  INGREDIENT = 'ingredient',
  RECIPE = 'recipe',
  RECIPE_SWIPER = 'recipe-swiper',
  LIVE_BAR = 'live-bar',
  GUARANTEE_BOX = 'guarantee-box',
  Kulturnemzet = 'kulturnemzet',
  Sorozatveto = 'sorozatveto',
  OFFER_BOX = 'offer-box',
  MinuteToMinute = 'minute-to-minute',
  ARTICLE_BLOCK = 'article-block',
  LATEST_NEWS = 'latest-news',
  BLOCK_SEPARATOR = 'block-separator',
  HIGHLIGHTED_SELECTION = 'highlighted-selection',
  SELECTION = 'selection',
  BRANDING_BOX_ARTICLE = 'branding-box-article',
  BLOG = 'blog',
  DAILY_MENU = 'daily-menu',
  CONFERENCE = 'conference',
  TELEKOM_VIVICITTA = 'telekom-vivicitta',
  AGROKEP = 'agrokep',
  AGROKEP_LIST = 'agrokep-list',
  COUNTDOWN_BOX = 'countdown-box',
  ELECTIONS_BOX = 'elections-box',
  EB_SINGLE_ELIMINATION = 'eb-single-elimination',
  EB_COUNTDOWN_BLOCK_TITLE = 'eb-countdown-block-title',
  PROGRAM = 'program',
  DAILY_PROGRAM = 'daily-program',
  DID_YOU_KNOW = 'did-you-know',
  VISITOR_COUNTER = 'visitor-counter',
  OLIMPIA_COUNTDOWN_BLOCK_TITLE = 'olimpia-countdown-block-title',
  EB_NEWS = 'eb-news',
  OLIMPIA_NEWS = 'olimpia-news',
  OLIMPIA_HUNGARIAN_TEAM = 'olimpia-hungarian-team',
  OLIMPIA_ARTICLES_WITH_PODCAST_CONTENT = 'olimpia-articles-with-podcast-content',
  OLIMPIA_LARGE_NAVIGATOR = 'olimpia-large-navigator',
  BAYER_BLOG = 'bayer-blog',
  WEEKLY_MENU = 'weekly-menu',
  TENYEK_BOX = 'tenyek-box',
  PUBLIC_AUTHORS = 'public-authors',
  MULTI_VOTE = 'multi-vote',
  SPOTLIGHT = 'spotlight',
  APP_DOWNLOAD = 'app-download',
  SPONSORED_ARTICLE_BOX = 'sponsored-article-box',
  PODCAST_ARTICLE_LIST = 'podcast-article-list',
  SERVICES_BOX = 'services-box',
  OPINION_NEWSLETTER = 'opinion-newsletter-box',
  PODCAST_APP_RECOMMENDER = 'podcast-app-recommender',
  RIPOST7_BLOCK = 'ripost7-block',
  ASTRONET_CIKKEK = 'astronet-cikkek',
  ASTRONET_HOROSZKOP = 'astronet-horoszkop',
  ASTRONET_JOSLAS = 'astronet-joslas',
  ASTRONET_BRANDING_BOX = 'astronet-branding-box',
  ASTRONET_COLUMNS = 'astronet-columns',
  EXPERIENCE_GIFT = 'experience-gift',
  EVENT_CALENDAR = 'event-calendar',
  GASTRO_EXPERIENCE_RECOMMENDATION = 'gastro-experience-recommendation',
  GASTRO_OCCASION_RECOMMENDER = 'gastro-occasion-recommender',
  GASTRO_EXPERIENCE_OCCASION = 'gastro-experience-occasion',
  GASTRO_THEMATIC_RECOMMENDER = 'gastro-thematic-recommender',
  GASTRO_EXPERIENCE_OCCASION_SWIPER = 'gastro-experience-occasion-swiper',
  TOP_RANKING_GLOSSARY = 'top-ranking-glossary',
  WRITE_TO_US = 'write-to-us',
  JOB_LISTINGS = 'job-listings',
  WHERE_THE_BALL_WILL_BE = 'where-the-ball-will-be',
  CONFIGURABLE_SPONSORED_BOX = 'configurable-sponsored-box',
  TOP_TEN_TAGS = 'top-ten-tags',
  STAR_BIRTHS = 'star-births',
  COLUMN_BLOCK = 'column-block',
  LATEST_AND_MOST_READ_ARTICLES = 'latest-and-most-read-articles',
  SPONSORED_VOTE = 'sponsored-vote',
  SUB_COLUMNS = 'sub-columns',
  SECRET_DAYS_CALENDAR = 'secret-days-calendar',
  TOPIC_SUGGESTION = 'topic-suggestion',
  TOP_COMMENTED_ARTICLES = 'top-commented-articles',
}

export enum LayoutElementContentItemType {
  QUIZ = 'selectedQuiz',
  SHORT_NEWS = 'selectedShortNews',
  FAST_NEWS = 'selectedFastNews',
  ARTICLES = 'selectedArticles',
  NOTES = 'selectedNotebooks',
  OPINIONS = 'selectedOpinions',
  VIDEOS = 'selectedVideos',
  VOTE = 'selectedVote',
  MULTI_VOTE = 'selectedMultiVote',
  OCCASION = 'selectedOccasions',
  DOSSIERS = 'selectedDossiers',
  PODCASTS = 'selectedPodcasts',
  GALLERIES = 'selectedGalleries',
  BREAKING = 'selectedBreakings',
  PROGRAM = 'selectedPrograms',
  BRANDING = 'selectedBrandingBoxes',
  TABS = 'tabs',
  IMAGE = 'selectedImage',
  BLOG = 'selectedBlogs',
  DETECTIONS = 'selectedDetections',
  DATA_BANK = 'selectedCompetitions',
  RECIPE = 'selectedRecipes',
  INGREDIENT = 'selectedIngredients',
  WYSIWYG = 'formControls',
}

export enum LayoutElementContentExplicitDragContentTypes {
  Article = 'article',
  Opinion = 'opinion',
}

/**
 * LayoutElementContentTypeMapping can be used to identify which drag content type belongs to which content item type.
 * This is because when you drag an article, we can only see it's type as 'article' or 'opinion'. This does not help in determining which
 * property should be used when setting the explicit item. For article: selectedArticles for example.
 */
export const LayoutElementDragContentTypeMapping: Partial<Record<LayoutElementContentExplicitDragContentTypes, LayoutElementContentItemType>> = {
  [LayoutElementContentExplicitDragContentTypes.Article]: LayoutElementContentItemType.ARTICLES,
  [LayoutElementContentExplicitDragContentTypes.Opinion]: LayoutElementContentItemType.OPINIONS,
};

export interface BlockTitle<I = string> {
  text: string;
  url?: string;
  urlName?: string;
  visibleIcon?: boolean;
  icon?: I;
  checkbox?: boolean;
  isDark?: boolean;
  selectedSponsorship?: Sponsorship;

  overrideColor?: string;
}

export interface BrandingArticle {
  id: string;
  slug: string;
  publishDate: string;
  brand: string;
  original?: any;
  lead?: string;
  thumbnail?: string;
  title?: string;
  data?: BrandingArticleAutomaticData;
}

export interface BrandingArticleAutomaticData {
  id: string;
  lead: string;
  publishDate: string;
  slug: string;
  thumbnailUrl: string;
  thumbnailUrlCreatedAt: string;
  title: string;
  typeId: string;
  typeName: string;
}

export type LayoutMeta = ApiResponseMeta &
  Readonly<{
    columnTitle: string;
    columnParentTitle?: string;
    columnParentSlug?: string;
    columnLead?: string;
    sponsorship?: Sponsorship | null;
  }>;

export type LayoutWithExcludeIds = Readonly<{
  data?: Layout | null;
  excludedIds: string[];
  columnTitle: string;
  columnLead?: string;
  columnParentTitle?: string;
  columnParentSlug?: string;
  sponsorship?: Sponsorship | null;
}>;

export type Layout = Readonly<{
  struct: LayoutStruct[];
  content: LayoutContent[];
}>;

export type LayoutContent = Readonly<{
  autoFill: AutoFill;
  hasImage: boolean;
  layoutElementId: string;
  selectedArticles: SelectedArticle[];
  selectedOpinions: SelectedOpinion[];
  selectedBlogs?: SelectedOpinion[];
}>;

export type AutoFill = Readonly<{
  filterColumns?: AutoFillChoice[];
  filterPriorities?: AutoFillChoice[];
  filterSponsorships?: AutoFillChoice[];
  filterTags?: AutoFillChoice[];
  orderPriority?: 'date' | 'popularity';
}>;

type AutoFillChoice = Readonly<{
  id: string;
  title: string;
  slug?: string;
}>;

export type Config = Readonly<{
  autoFill: AutoFill;
  hasImage: boolean;
  layoutElementId: string;
  selectedArticles: SelectedArticle[];
}>;

export interface SelectedArticle {
  id: string;
  data?: LayoutArticle;
  original?: OriginalArticle;
  overwrite?: ArticleOverwriteData;
}

export type SelectedOpinion = Readonly<{
  id: string;
  data: LayoutArticle;
}>;

export type VideoOverwriteData = {
  title?: string;
  image?: any;
  thumbnailFocusedImages?: FocusPointUrlWithAspectRatio;
};

export type SelectedVideo = Readonly<{
  data: any; //FIXME any
  id: string;
  original: any; //FIXME any
  overwrite?: VideoOverwriteData;
  title: string;
}>;

export interface ArticleOverwriteData {
  lead?: string;
  image?: OverwriteImage;
  title?: string;
  customTag?: string;
  thumbnailFocusedImages?: FocusPointUrlWithAspectRatio;
}

export interface OriginalArticle {
  lead?: string;
  slug: string;
  title: string;
  excerpt: string;
  isPrint: boolean;
  isActive: boolean;
  isDeleted: boolean;
  authorName: string;
  authorEmail: string;
  publishDate: string;
  thumbnailUrl?: string;
  primaryColumn: PrimaryColumn;
  readingLength?: number;
  authorAvatarFullSizeUrl: string;
  authorAvatarThumbnailUrl: string;
  recommended_title?: string;
}

export type PrimaryColumn = Readonly<{
  slug: string;
  title: string;
}>;

export interface LayoutArticle {
  id: string;
  slug: string;
  title: string;
  excerpt?: string;
  columnId: string;
  isActive?: string;
  sponsorId?: string; // | string[];
  columnSlug: string;
  priorityId?: string;
  columnTitle: string;
  publishDate?: string | Date;
  sponsorTitle?: string; // | string[];
  thumbnailUrl?: string;
  priorityTitle?: string;
  columnParentId?: any;
  reading_length?: string;
  columnParentSlug?: any;
  publicAuthorName?: string;
  authorRank?: string;
  columnParentTitle?: any;
  publicAuthorAvatarThumbnailUrl?: string;
  lead?: string;
  allTags?: Tag[];
  length?: string;
}

export type LayoutGalleryImage = Readonly<{
  altText?: string;
  caption?: string;
  fullSizeUrl: string;
  id: string;
  photographer?: string;
  source?: string;
  thumbnailUrl: string;
  title?: string;
}>;

export type LayoutBreakingNews = Readonly<{
  authorAvatarFullSizeUrl?: string;
  authorAvatarThumbnailUrl?: string;
  authorEmail?: string;
  authorName?: string;
  'editedVersion.dataPrimary.preTitle'?: boolean;
  'editedVersion.dataSecondary.videoType'?: boolean;
  excerpt?: string;
  isActive?: boolean;
  isDeleted?: boolean;
  isPrint?: boolean;
  lead: string;
  primaryColumn?: PrimaryColumn;
  publishDate?: string | Date;
  readingLength: string | number;
  slug: string;
  thumbnailUrl: string;
  secondaryThumbnailUrl?: string;
  title: string;
}>;

export type LayoutImage = Readonly<{
  altText: string;
  caption: string;
  date: string;
  fileName: string;
  fileSize: number;
  focalPoint: { xPercent: number; yPercent: number };
  id: string;
  photographer: string;
  resolution: number[];
  selectedVariant: any;
  source: string;
  title: string;
  url: {
    fullSize: string;
    thumbnail: string;
  };
  variantIds: string[];
}>;

export type LayoutWysiwygFormControl = SimpleGeneratedFormControl &
  Readonly<{
    componentId: string;
    componentType: string;
    formControls: SimpleGeneratedFormControl[];
    index: number;
    inputType: 'wysiwyg';
    isNew: boolean;
    label: 'label.wysiwyg';
    markedToDelete: boolean;
  }>;

export type SimpleGeneratedFormControl<Type = string> = Readonly<{
  key: string;
  label: string;
  inputType: string;
  inputInfo: Record<string, any>;
  isDisabled: boolean;
  isHidden: boolean;
  asserts?: Record<string, any>;
  errors?: Record<string, any>;
  value: Type;
  initialValue: Type;
  wordCount?: number;
  characterCount?: number;
  rowCount?: number;
}>;

export type BackendResponsePublicAuthor = Readonly<{
  id: string;
  slug: string;
  fullName: string;
  avatar: {
    fullSizeUrl: string;
    thumbnailUrl: string;
    thumbnailUrl43: string;
    thumbnailUrl4071: string;
    thumbnailUrlHuge: string;
  };
  isMaestroAuthor: boolean;
}>;

export type LayoutDisplayDirection = 'horizontal' | 'vertical';

export type LayoutContentItemWrapperTemplateData = Readonly<{
  layoutElement: LayoutElement;
  direction?: LayoutDisplayDirection;
  desktopWidth?: number;
}>;

export type LayoutContentItemInnerWrapperTemplateData = Readonly<{
  layoutElement: LayoutElement;
  direction?: LayoutDisplayDirection;
  desktopWidth?: number;
  index?: number;
  first?: number;
  last?: number;
  backgroundColor?: string;
}>;

export type BlockWrapperTemplateData = Readonly<{
  layoutElement: LayoutElement;
  direction?: LayoutDisplayDirection;
  desktopWidth?: number;
  backgroundColor?: string;
}>;

export type RecipeCardData = Readonly<{
  category: {
    name: string;
  };
  title: string;
  preparation: {
    time: number;
    unitOfTime: string;
    servings: number;
    price: string;
  };
  author: Author & { verified: boolean };
  hasVideo: boolean;
  mmeWarranty?: FakeBool;
  guarantee: boolean;
  thumbnail: {
    url: string;
    alt: string;
  };
  cover_image_thumbnail?: string;
  cover_image?: string;
  publicAuthor?: BackendResponsePublicAuthor;
  containsVideo?: FakeBool;
  prepareTime?: number;
  madeForPeople?: number;
}>;

export interface LayoutArticleData {
  id: string;
  isActive: FakeBool;
  publishDate?: string | Date;
  reading_length: string;
  publicAuthorName: string;
  publicAuthorRank?: string;
  publicAuthorSlug?: string;
  publicAuthorAvatarThumbnailUrl: string;
  slug: string;
  title: string;
  preTitle?: string;
  subTitle?: string;
  recommendedTitle: string;
  excerpt: string;
  lead?: string; // FIXME should be checked if valid (@link getArticleData)
  thumbnail?: string; // FIXME should be checked if valid (@link getArticleData)
  thumbnailUrl: string;
  thumbnailUrl43?: string;
  thumbnailUrlHuge?: string;
  secondaryThumbnailUrl?: string;
  secondaryThumbnailUrl43?: string;
  firstVideoId?: string;
  firstGalleryId?: string;
  firstVotingId?: string;
  isPodcastType: FakeBool;
  isVideoType: FakeBool;
  columnId: string;
  columnTitle: string;
  columnSlug: string;
  columnTitleColor: string;
  columnParentId?: string;
  columnParentTitle?: string;
  columnParentSlug?: string;
  regionId?: string;
  regionSlug?: string;
  regionTitle?: string;
  regions?: Region[];
  tags?: Tag[];
  allTags?: Tag[];
  priorityId: string;
  priorityTitle: string;
  sponsorId?: string; // | string[];
  sponsorTitle?: string; // | string[];
  isAdultsOnly: FakeBool;
  preTitleColor: string;
  is_hidden_on_layout: FakeBool;
  hidden_on_layout_from?: string | Date;
  hidden_on_layout_until?: string | Date;
  gameTitle?: string;
  gameLogoUrl?: string;
  gameBackgroundColor?: string;
  firstRegionId?: string;
  firstTagId?: string;
  hasGallery?: FakeBool;
  articleSource?: string;
  articleMedium?: string;
  minuteToMinute?: MinuteToMinuteState;
  hasDossiers?: FakeBool;
  isPaywalled?: FakeBool;
  length?: string;
  publicAuthorM2M?: PublicMultiAuthor[];
  foundationTagSlug?: string;
  foundationTagTitle?: string;
  columnEmphasizeOnArticleCard?: FakeBool;
  isGuaranteeType?: FakeBool;
  primaryColumn?: PrimaryColumn;
  thumbnailUrlFocusedImages?: FocusPointUrlWithAspectRatio;
  thumbnailFocusedImages?: FocusPointUrlWithAspectRatio;
  isVidcast?: FakeBool;
}

export type LayoutDossierArticle = Readonly<{
  id: string;
  isActive: string;
  publishDate: string;
  reading_length: string;
  publicAuthorName: string;
  publicAuthorAvatarThumbnailUrl: string;
  slug: string;
  title: string;
  preTitle: string;
  lead: string;
  recommendedTitle?: string;
  excerpt: string;
  thumbnailUrl: string;
  secondaryThumbnailUrl?: string;
  firstVideoId: string;
  firstGalleryId: string;
  firstVotingId: string;
  firstTagId?: string;
  isPodcastType: string;
  isVideoType: string;
  isInterviewType: string;
  columnId: string;
  columnTitle: string;
  columnSlug: string;
  columnTitleColor?: string;
  columnParentId?: string;
  columnParentTitle?: string;
  columnParentSlug?: string;
  priorityId: string;
  priorityTitle: string;
  sponsorId: string;
  sponsorTitle: string;
  isAdultsOnly: string;
  preTitleColor?: string;
  is_hidden_on_layout: string;
  hidden_on_layout_from?: boolean;
  hidden_on_layout_until?: boolean;
  tags?: Tag[];
  hasGallery: string;
  columnEmphasizeOnArticleCard?: FakeBool;
  thumbnailFocusedImages?: FocusPointUrlWithAspectRatio;
  thumbnailUrlFocusedImages?: FocusPointUrlWithAspectRatio;
  publicAuthorM2M?: PublicMultiAuthor[];
}>;

export type SelectedDossier = Readonly<{
  id: string;
  overwriteTitle: string;
  overwriteMoreButtonLabel?: string;
  mainArticle: {
    id: string;
    // TODO: overwrite like in article config
    original?: LayoutDossierArticle;
    data?: LayoutDossierArticle;
  };
  secondaryArticles: {
    id: string;
    // TODO: overwrite like in article config
    original?: LayoutDossierArticle;
    data?: LayoutDossierArticle;
  }[];
  description?: string;
  original?: DossierData;
  data?: DossierData;
  newsFeedTitle?: string;
}>;

export type ImageLayoutData = Readonly<{
  cancelMargin: boolean;
  altText: string;
  fullSize: string;
  source: string;
  photographer: string;
  caption: string;
  url: string;
}>;

export type SelectedBreakingRec = Readonly<{
  id: string;
  original: LayoutBreakingNews;
  data?: LayoutBreakingNews;
  overwrite: ArticleOverwriteData;
}>;

export type LayoutTurpiCardData = Readonly<{
  id: string;
  title: string;
  description: string;
  sender: string;
  sentByUser: {
    name: string;
    id: string;
  };
}>;

export type Tabs = Readonly<{
  id: string;
  topArticles: ArticleCard[];
  bottomArticles: ArticleCard[];
  region: string;
  regionSlug: string;
}>;

export type StaticPageResponse = Readonly<{
  body: ArticleBody[];
  id: string;
  publishDate?: Date;
  slug: string;
  title: string;
}>;
