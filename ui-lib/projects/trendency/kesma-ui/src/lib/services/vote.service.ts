import { inject, Injectable } from '@angular/core';
import { ReqService, StorageService, UtilService } from '@trendency/kesma-core';
import { Observable, of, switchMap } from 'rxjs';
import { ApiResponseMetaList, ApiResult, Article, ArticleBodyType, BackendVoteData, VoteData, VoteDataWithVotedId, VoteSubmitResponse } from '../definitions';
import { addMonths } from 'date-fns';
import { RecaptchaService } from './recaptcha.service';
import { catchError, map, shareReplay } from 'rxjs/operators';
import { backendVotingDataToVotingData } from '../utils';

const VOTE_STORAGE_KEY = 'votes';
const VOTE_RECAPTCHA_ACTION_ID = 'app_publicapi_voting_vote_vote';

@Injectable({
  providedIn: 'root',
})
export class VoteService {
  private readonly utilsService = inject(UtilService);
  private readonly reqService = inject(ReqService);
  private readonly storageService = inject(StorageService);
  private readonly recaptchaService = inject(RecaptchaService);

  /** Currently active votes */
  readonly #votes: Record<
    string,
    {
      value: string;
      expires: number;
    }
  > = {};
  /** Cached vote detail responses */
  readonly voteCache = {} as Record<string, Observable<VoteDataWithVotedId | undefined>>;

  constructor() {
    this.#votes = this.storageService.getLocalStorageData(VOTE_STORAGE_KEY) || {};
  }

  /**
   * Handle submitting user vote. Including recaptcha and post.
   * @param votedId ID of the voted OPTION (not the vote itself)
   * @param voteData other options (e.g. question, answers, id of the VOTE)
   */
  onVotingSubmit(votedId: string, voteData: VoteDataWithVotedId): Observable<VoteDataWithVotedId> {
    return this.recaptchaService.acquire(VOTE_RECAPTCHA_ACTION_ID).pipe(
      switchMap((token) => this.saveVoting(token, votedId)),
      switchMap((res: VoteSubmitResponse) => {
        voteData.data.answers = res.data.answers;
        voteData.votedId = votedId;
        this.#votes[voteData.data.id] = {
          value: votedId,
          expires: addMonths(new Date(), 1).getTime(),
        };

        // Clear expired votes, to avoid clutter
        Object.keys(this.#votes)
          .filter((key) => this.#votes[key].expires < Date.now())
          .forEach((key) => delete this.#votes[key]);
        this.storageService.setLocalStorageData(VOTE_STORAGE_KEY, this.#votes);
        return of(voteData);
      })
    );
  }

  /**
   * Returns which ID the user voted for
   * @param voteData Options
   */
  getVotedId(voteData: VoteData): string | undefined {
    if (!this.utilsService.isBrowser()) {
      return undefined;
    }

    return this.#votes[voteData.id]?.value;
  }

  /**
   * Constructs a reusable VoteData from VoteData response. Includes the user's vote if possible.
   * @param value API response
   */
  getVoteData(value: VoteData): VoteDataWithVotedId {
    return {
      data: value,
      votedId: value ? this.getVotedId(value) : undefined,
    };
  }

  initArticleVotes(article: Article): void {
    const votes = article.body.filter((element) => element.type === ArticleBodyType.Voting);

    for (const voting of votes) {
      this.setVoteCache({ data: voting.details?.[0]?.value });
    }
  }

  setVoteCache(voteData: VoteDataWithVotedId): void {
    const voteId = voteData.data.id;
    this.voteCache[voteId] = this.fetchVoteDetails(voteData);
  }

  private fetchVoteDetails(voteData: VoteDataWithVotedId): Observable<VoteDataWithVotedId | undefined> {
    const voteId = voteData.data.id;
    return this.utilsService.isBrowser()
      ? this.recaptchaService.acquire(VOTE_RECAPTCHA_ACTION_ID).pipe(
          switchMap((token) =>
            this.reqService.get<ApiResult<BackendVoteData, ApiResponseMetaList>>(`/voting/${voteId}`, {
              params: {
                recaptcha: token,
              },
            })
          ),
          map(({ data }) =>
            this.getVoteData(
              backendVotingDataToVotingData({
                ...data,
                id: voteId,
              })
            )
          ),
          shareReplay({ bufferSize: 1, refCount: true }),
          catchError((err) => {
            console.error('Failed to fetch vote details', err);
            return of(undefined);
          })
        )
      : of(voteData);
  }

  /**
   * Sends the vote to API
   * @param token
   * @param voteId
   */
  private saveVoting(token: string, voteId: string): Observable<VoteSubmitResponse> {
    return this.reqService.get(`/voting/vote/${voteId}`, {
      params: {
        recaptcha: token,
      },
    });
  }
}
