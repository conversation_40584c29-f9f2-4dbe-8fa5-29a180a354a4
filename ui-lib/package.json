{"name": "trendency-portal-ui", "version": "2.0.0", "scripts": {"start": "npm run build", "build": "npm run build:kesma-core && npm run build:kesma-ui", "build:kesma-ui": "ng build --project @trendency/kesma-ui", "build:kesma-core": "ng build --project @trendency/kesma-core", "watch:kesma-ui": "ng build --watch --configuration development --project @trendency/kesma-ui", "watch:kesma-core": "ng build --watch --configuration development --project @trendency/kesma-core", "publish:kesma-ui": "cd ./dist/trendency/kesma-ui && npm publish", "publish:kesma-core": "cd ./dist/trendency/kesma-core && npm publish", "release:kesma-ui:minor": "cd projects/trendency/kesma-ui && npm version minor && cd ../../ && npm run build:kesma-ui && npm run publish:kesma-ui", "release:kesma-ui:patch": "cd projects/trendency/kesma-ui && npm version patch && cd ../../ && npm run build:kesma-ui && npm run publish:kesma-ui", "release:kesma-core:minor": "cd projects/trendency/kesma-core && npm version minor && cd ../../ && npm run build:kesma-core && npm run publish:kesma-core", "release:kesma-core:patch": "cd projects/trendency/kesma-core && npm version patch && cd ../../ && npm run build:kesma-core && npm run publish:kesma-core", "show:version:kesma-ui": "grep version ./projects/trendency/kesma-ui/package.json|cut -d'\"' -f4", "show:version:core-core": "grep version ./projects/trendency/kesma-core/package.json|cut -d'\"' -f4", "prepare": "husky"}, "private": true, "dependencies": {"@angular/animations": "^19.2.1", "@angular/cdk": "^19.2.1", "@angular/common": "^19.2.1", "@angular/compiler": "^19.2.1", "@angular/core": "^19.2.1", "@angular/forms": "^19.2.1", "@angular/platform-browser": "^19.2.1", "@angular/platform-browser-dynamic": "^19.2.1", "@angular/router": "^19.2.1", "@angular/ssr": "^19.2.1", "@ng-select/ng-select": "^14.2.3", "@trendency/kesma-core": "3.1.3", "@trendency/kesma-ui": "3.18.3", "bootstrap": "^5.3.3", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "flatpickr": "^4.6.13", "lodash-es": "^4.17.21", "ngx-captcha": "^13.0.0", "ngx-date-fns": "^12.0.0", "rxjs": "^7.8.2", "swiper": "^11.2.5", "tslib": "^2.8.1", "zone.js": "^0.15.0", "chart.js": "^4.4.9"}, "devDependencies": {"@angular-devkit/architect": "^0.1902.1", "@angular-devkit/core": "^19.2.1", "@angular/build": "^19.2.1", "@angular/cli": "^19.2.1", "@angular/compiler-cli": "^19.2.1", "@semantic-release/gitlab": "^13.2.4", "@types/express": "^5.0.0", "@types/lodash-es": "^4.17.12", "@types/node": "^22.13.10", "husky": "^9.1.7", "lint-staged": "^15.4.3", "ng-packagr": "^19.2.0", "prettier": "^3.5.3", "semantic-release": "^22.0.12", "semantic-release-monorepo": "^8.0.2", "typescript": "^5.8.2"}, "engines": {"node": "^18.13.0 || ^20.9.0", "npm": ">=8.0.0"}}