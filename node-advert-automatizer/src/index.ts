import { createAdFilesMegyei } from "./PORTALS/megyei-lapok/utils/createFileByPortals.utils";
import { sequenceMegyeiLapokRequests } from "./PORTALS/megyei-lapok/utils/iterateCmsRequestsByPortal.utils";
import "./env-config";
import { runCMSRequests } from "./utils/CMSRequestSequence.utils";
import { createAdFilesAllPortals } from "./utils/createAdFilesAllPortals.utils";
import { exportPortalAdToJson } from "./utils/exportPortalAdToJson.utils";

//// Exports the cms-ready ads into a .json file, based on the PORTAL env.
// exportPortalAdToJson();

// //// DELETES, ADDS, ACTIVATES commercials in a sequence, based on the PORTAL env.
// runCMSRequests();

//// CREATES ALL MEGYEI LAPOK JSON BACKEND READY ADS
createAdFilesMegyei();

//// IT LOOPS THROUGH THE ARRAY OF MEGYEI PORTALS
sequenceMegyeiLapokRequests();

// GENERATE ALL PORTALS - WIP / not working
// createAdFilesAllPortals();
