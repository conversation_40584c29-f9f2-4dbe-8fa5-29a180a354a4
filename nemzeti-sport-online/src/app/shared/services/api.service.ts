import { Injectable } from '@angular/core';
import { IHttpOptions, ReqService } from '@trendency/kesma-core';
import {
  AllColumns,
  ApiListResult,
  ApiResponseMetaList,
  ApiResult,
  ArticleCard,
  ArticleCategoryParams,
  ArticleSearchResult,
  ArticleSocial,
  BackendArticle,
  BackendArticleSearchResult,
  BackendComment,
  BasicDossier,
  buildMenuItem,
  ColumnTreeElement,
  DossierArticle,
  GalleryData,
  InitResponse,
  Layout,
  MenuTreeResponse,
  PortalBasedMenuLinkOverride,
  PortfolioResponse,
  Region,
  SearchQuery,
  SimplifiedMenuTree,
  VariableDidYouKnowBox,
} from '@trendency/kesma-ui';
import { Observable, of } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import {
  BackendAllowedLoginMethodsResponse,
  BackendSocialLoginResponse,
  BackendUserLoginResponse,
  LoginFormData,
  RealTimeStatistics,
  RegistrationFormData,
  TeamsLeageTable,
} from '../definitions';
import { PortalConfigService } from './portal-config.service';
import { HttpParams } from '@angular/common/http';
import { ChampionshipDetails, ChampionshipSchedules } from '../../feature/championship/api/championship.definitions';
import { BackendArticleSocial, CommentType } from '../../feature/article-page/api/article-comments.definitions';
import { AuthorData } from '../../feature/author-page/api/author-page.definitions';
import { requestPasswordResetDataToBackendRequest, resetPasswordDataToBackendRequest } from '../../feature/forgot-password/forgot-password.utils';
import { SubscriptionSessionData } from '../../feature/subscription/subscription.definitions';
import { subscriptionSessionDataToBackendRequest } from '../../feature/subscription/subscription.utils';
import { BackendMatchDatesResponse, BackendScheduleResponse } from '../../feature/layout/definitions/matches.definitions';
import { loginFormDataToBackendRequest, registrationFormDataToBackendRequest } from '../utils';

@Injectable({
  providedIn: 'root',
})
export class ApiService {
  private cachedInit: ApiResult<InitResponse> | undefined;

  constructor(
    private readonly reqService: ReqService,
    private readonly portalConfigService: PortalConfigService
  ) {}

  public init(): Observable<ApiResult<InitResponse>> {
    if (this.cachedInit) {
      return of(this.cachedInit);
    }

    return this.reqService.get<ApiResult<InitResponse>>('/init').pipe(
      tap((initResponse) => {
        this.portalConfigService.setConfig(initResponse?.data?.portalConfigs);
        this.cachedInit = initResponse;
      })
    );
  }

  public getMenu(overrides?: PortalBasedMenuLinkOverride): Observable<SimplifiedMenuTree> {
    return this.reqService.get<ApiResult<MenuTreeResponse>>('menu/tree').pipe(
      map(
        ({ data }) =>
          ({
            header: (data?.header ?? []).map((item) => buildMenuItem(item, '', overrides)),
            header_0: (data?.header_0 ?? []).map((item) => buildMenuItem(item, '', overrides)),
            header_1: (data?.header_1 ?? []).map((item) => buildMenuItem(item, '', overrides)),
            footer: (data?.footer ?? []).map((item) => buildMenuItem(item, '', overrides)),
            footer_0: (data?.footer_0 ?? []).map((item) => buildMenuItem(item, '', overrides)),
            footer_1: (data?.footer_1 ?? []).map((item) => buildMenuItem(item, '', overrides)),
          }) as SimplifiedMenuTree
      )
    );
  }

  public getRealTimeStatistics(): Observable<ApiResult<RealTimeStatistics, ApiResponseMetaList>> {
    return this.reqService.get('/site-realtime-statistics/default-last-90-minutes-ago');
  }

  public getLayoutPreview(hash: string): Observable<ApiResult<Layout>> {
    return this.reqService.get(`layout/preview/view?previewHash=${hash}`);
  }

  public getCategoryLayout(categorySlug: string): Observable<ApiResult<Layout>> {
    return this.reqService.get<ApiResult<Layout>>(`column-layout/${categorySlug}`);
  }

  public getChampionship(categorySlug: string): Observable<ApiResult<ChampionshipDetails>> {
    return this.reqService.get<ApiResult<ChampionshipDetails>>(`sport/competition/${categorySlug}`);
  }

  public getChampionshipTable(categorySlug: string): Observable<ApiResult<TeamsLeageTable>> {
    return this.reqService.get<ApiResult<TeamsLeageTable>>(`sport/competition/${categorySlug}/tabella`);
  }

  public getChampionshipFutureMatches(categorySlug: string): Observable<ApiResult<ChampionshipSchedules>> {
    return this.reqService.get<ApiResult<ChampionshipSchedules>>(`sport/schedule/by-competition/${categorySlug}`);
  }

  public getChampionshipMatchesByDate(categorySlug: string, matchDate: string): Observable<ApiResult<any>> {
    return this.reqService.get<ApiResult<any>>(`/sport/schedule/by-date/${categorySlug}/${matchDate}`);
  }

  public getCategoryArticles(
    categorySlug: string,
    page = 0,
    itemsPerPage = 10,
    year?: string,
    month?: string,
    excludedIds: string[] = []
  ): Observable<ApiResult<BackendArticle[], ApiResponseMetaList>> {
    let params: ArticleCategoryParams = {
      columnSlug: categorySlug,
      rowCount_limit: itemsPerPage.toString(),
      page_limit: page.toString(),
      'excludedArticleIds[]': excludedIds ? excludedIds : [],
    };
    params = year ? { ...params, year } : params;
    params = month ? { ...params, month } : params;
    return this.reqService.get<ApiResult<BackendArticle[], ApiResponseMetaList>>(`content-page/articles-by-column`, { params: params }).pipe(
      map(({ data, meta }) => {
        return {
          meta,
          data: data.map((article) => {
            const [publishYear, publishMonth] = (article.publishDate as string).split('-');
            return {
              ...article,
              publishYear,
              publishMonth,
            };
          }),
        };
      })
    );
  }

  public getSidebarArticleRecommendations(
    count: number,
    columnSlug?: string,
    excludedIds: string[] = []
  ): Observable<ApiResult<ArticleCard[], ApiResponseMetaList>> {
    let { params }: IHttpOptions = {
      params: {
        rowCount_limit: count.toString(),
        'excludedArticleIds[]': excludedIds,
      },
    };
    params = columnSlug ? { ...params, columnSlug } : params;

    return this.reqService.get<ApiResult<ArticleCard[], ApiResponseMetaList>>(
      columnSlug ? '/content-page/articles-by-column?dev' : '/content-page/articles-by-last-day',
      { params }
    );
  }

  getSocialData(id: string): Observable<ArticleSocial> {
    return this.reqService.get<ApiResult<BackendArticleSocial>>(`/content-page/article/${id}/social-count`).pipe(
      map(({ data }) => ({
        ...data,
        isCommentsDisabled: data.disableComments,
        isLikesAndDislikesDisabled: data.disableLikesAndDislikes,
      }))
    );
  }

  getAuthorFromPublicAuthor(authorSlug: string): Observable<ApiResult<AuthorData>> {
    return this.reqService.get(`/user/author_social`, {
      params: {
        global_filter: authorSlug,
      },
    });
  }

  public getOpinionAuthor(authorSlug: string, page = 0, itemsPerPage = 12): Observable<ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>> {
    return this.reqService.get(`content-page/articles-by-opinion-type`, {
      params: {
        author: authorSlug,
        rowCount_limit: itemsPerPage.toString(),
        page_limit: page.toString(),
      },
    });
  }

  public getArticlesByTag(slug: string): Observable<ApiResult<ArticleCard[], ApiResponseMetaList>> {
    return this.reqService.get(`content-page/articles-by-tag?tagSlug=${slug}`);
  }

  public getGalleries(page = 0, itemsPerPage = 16): Observable<ApiResult<GalleryData[], ApiResponseMetaList>> {
    return this.reqService.get(`/media/galleries`, {
      params: {
        rowCount_limit: itemsPerPage.toString(),
        page_limit: page.toString(),
      },
    });
  }

  public getDossier(dossierSlug: string, page = 0, itemsPerPage = 21): Observable<ApiResult<DossierArticle[], ApiResponseMetaList & Partial<BasicDossier>>> {
    return this.reqService.get(`/content-group/dossiers/${dossierSlug}`, {
      params: { rowCount_limit: itemsPerPage?.toString(), page_limit: page?.toString() },
    });
  }

  public getRegionPage(
    regionSlug: string,
    page = 0,
    itemsPerPage = 21
  ): Observable<
    ApiResult<
      BackendArticle[],
      ApiResponseMetaList & {
        regionName: string;
      }
    >
  > {
    return this.reqService.get(`/content-page/articles-by-region`, {
      params: { regionSlug: regionSlug, rowCount_limit: itemsPerPage?.toString(), page_limit: page?.toString() },
    });
  }

  public getRegions(
    page = 0,
    itemsPerPage = 50
  ): Observable<
    ApiResult<
      Region[],
      ApiResponseMetaList & {
        regionName: string;
      }
    >
  > {
    return this.reqService.get(`/source/content-group/regions`, {
      params: { rowCount_limit: itemsPerPage?.toString(), page_limit: page?.toString() },
    });
  }

  public getColumns(): Observable<ApiListResult<AllColumns>> {
    return this.reqService.get('/source/content-group/columns');
  }

  public getSearch(searchQuery: SearchQuery, page = 0, itemsPerPage = 20, column: string[] = []): Observable<ApiResult<ArticleSearchResult[]>> {
    return this.reqService.get(`content-page/search?`, {
      params: {
        ...searchQuery,
        'columnSlugs[]': column ? column : [],
        rowCount_limit: itemsPerPage.toString(),
        page_limit: page.toString(),
      },
    });
  }

  public searchByKeyword(
    searchQuery?: string,
    page?: number,
    rowCount_limit?: number,
    columns?: string[]
  ): Observable<ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>> {
    const params = {
      global_filter: searchQuery,
      rowCount_limit: rowCount_limit?.toString(),
      page_limit: page?.toString(),
      'columns[]': columns,
    };
    if (!columns) {
      delete params['columns[]'];
    }
    return this.reqService.get(`/content-page/search`, {
      params,
    });
  }

  public searchByDate(
    fromDate: string,
    toDate: string,
    page = 0,
    itemsPerPage = 20,
    params?:
      | HttpParams
      | {
          readonly [param: string]: string | readonly string[];
        }
  ): Observable<ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>> {
    const searchQuery = {
      from_date: fromDate,
      to_date: toDate,
    };
    return this.reqService.get(`/content-page/search`, {
      params: {
        ...searchQuery,
        rowCount_limit: itemsPerPage.toString(),
        page_limit: page.toString(),
        ...params,
      },
    });
  }

  public getPortfolioFooter(): Observable<PortfolioResponse> {
    return this.reqService.get('portal/portfolio-footer');
  }

  register(formData: RegistrationFormData, recaptchaToken: string): Observable<void> {
    return this.reqService.post(`user/register`, registrationFormDataToBackendRequest(formData, recaptchaToken));
  }

  verifyRegister(id: string, hashedEmail: string, expiration: string, signature: string): Observable<void> {
    return this.reqService.get(`user/register/verify/${id}/${hashedEmail}`, { params: { expiration, signature } });
  }

  login(formData: LoginFormData, recaptchaToken: string): Observable<BackendUserLoginResponse> {
    return this.reqService.post(`portal_user/auth/login_check`, loginFormDataToBackendRequest(formData, recaptchaToken));
  }

  requestPasswordReset(email: string, recaptchaToken: string): Observable<void> {
    return this.reqService.post(`user/password/forget`, requestPasswordResetDataToBackendRequest(email, recaptchaToken));
  }

  resetPassword(email: string, password: string, resetPasswordToken: string): Observable<void> {
    return this.reqService.post(`user/password/reset`, resetPasswordDataToBackendRequest(email, password, resetPasswordToken));
  }

  getAllowedLoginMethods(): Observable<BackendAllowedLoginMethodsResponse> {
    return this.reqService.get(`user/auth/allowed-logins`);
  }

  loginWithFacebook(code: string, redirectUri: string): Observable<BackendSocialLoginResponse> {
    return this.reqService.post(`user/auth/login-facebook`, { code, redirectUri });
  }

  loginWithGoogle(code: string, redirectUri: string): Observable<BackendSocialLoginResponse> {
    return this.reqService.post(`user/auth/login-google`, { code, redirectUri });
  }

  saveSubscription(formData: SubscriptionSessionData, recaptchaToken: string): Observable<{ getPaymentUrl: string }> {
    return this.reqService.post(`simple-subscription/save`, subscriptionSessionDataToBackendRequest(formData, recaptchaToken));
  }

  getCommentsFor(id: string, params: Record<string, unknown>, type: CommentType = 'article'): Observable<ApiResult<BackendComment[], ApiResponseMetaList>> {
    const urlSuffix = type === 'article' ? 'comments' : 'answers';
    return this.reqService.get<ApiResult<BackendComment[], ApiResponseMetaList>>(`comments/${type}/${id}/${urlSuffix}`, {
      params,
      headers: {
        'Cache-Control': 'no-cache',
      },
    });
  }

  getCompetitionMatchDates(competitionSlug: string): Observable<BackendMatchDatesResponse> {
    return this.reqService.get(`sport/schedule/match-days/${competitionSlug}`);
  }

  /**
   * Request's backend date format: Y-m-d
   */
  getSchedulesByDay(competitionSlug: string, date: string): Observable<ApiResult<BackendScheduleResponse>> {
    return this.reqService.get(`sport/schedule/by-date/${competitionSlug}/${date}`);
  }

  getVariableSponsoredDidYouKnowBox(id: string): Observable<VariableDidYouKnowBox> {
    return this.reqService.get(`/content-group/did-you-know/${id}?getRandomData=1`);
  }

  getColumnsAsTree(): Observable<ApiResult<ColumnTreeElement[], ApiResponseMetaList>> {
    return this.reqService.get(`source/content-group/columns-as-tree`);
  }
}
