import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  effect,
  ElementRef,
  inject,
  OnDestroy,
  OnInit,
  Signal,
  signal,
  ViewChild,
} from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { FormatDatePipe, IMetaData, SchemaOrgService, SeoService, StorageService, UtilService } from '@trendency/kesma-core';
import {
  AnalyticsService,
  Article,
  ArticleBody,
  ArticleBodyDetails,
  ArticleBodyType,
  ArticleCard,
  ArticleCardWithSocial,
  ArticleFileLinkDirective,
  ArticleResolverData,
  ArticleRouteParams,
  ArticleVideoComponent,
  backendRecommendedArticleToArticleCard,
  buildArticleUrl,
  CommentListResponse,
  FocusPointDirective,
  GalleryData,
  GalleryElementData,
  MinuteToMinuteBlock,
  MinuteToMinuteState,
  MultiVoteDataWithVotedId,
  MultiVoteService,
  NativtereloComponent,
  PortalConfigSetting,
  ProtectedContentService,
  VoteDataWithAnswer,
  VoteService,
} from '@trendency/kesma-ui';
import { finalize, forkJoin, interval, merge, Observable, of, share, Subject, take } from 'rxjs';
import { distinctUntilChanged, map, switchMap, takeUntil } from 'rxjs/operators';

import { AsyncPipe, NgForOf, NgIf, NgSwitch, NgSwitchCase, NgTemplateOutlet } from '@angular/common';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import {
  AdvertService,
  ArticleCardType,
  ArticleDossierComponent,
  AuthService,
  defaultMetaInfo,
  getStructuredDataForArticle,
  IosAppService,
  NsoAdultComponent,
  NsoArticleCardComponent,
  NsoArticleHeaderComponent,
  NsoArticlesRelatedContentComponent,
  NsoEbDailyProgramComponent,
  NsoEbSingleEliminationComponent,
  NsoEbTeamsComponent,
  NsoGalleryCardComponent,
  NsoMultiVotingComponent,
  NsoPopupComponent,
  NsoProtectedContentNavigatorComponent,
  NsoProtectedContentUserInfoComponent,
  NsoQuizComponent,
  NsoSimpleButtonComponent,
  NsoSocialButtonsComponent,
  NsoTagsComponent,
  NsoVotingComponent,
  NsoWysiwygBoxComponent,
  PersonalizedRecommendationService,
  PortalConfigService,
  SecureApiService,
} from 'src/app/shared';
import { MultiVotingCacheService } from 'src/app/shared/services/multi-voting-cache.service';
import { RadioService } from 'src/app/shared/services/radio.service';
import { environment } from '../../../../../environments/environment';
import { GalleryApiService } from '../../../gallery-layer/api/gallery-api.service';
import { SidebarComponent } from '../../../layout/components/sidebar/sidebar.component';
import { NewsFeedService } from '../../../news-feed/api/news-feed.service';
import { ArticleCommentsService } from '../../api/article-comment.service';
import { ArticleService } from '../../api/article-page.service';
import { MinuteToMinuteService } from '../../api/minute-to-minute.service';
import { OlympicsNewsFeedArticleListComponent } from '../olympics-news-feed-article-list/olympics-news-feed-article-list.component';

@Component({
  selector: 'app-article-page',
  templateUrl: './article-page.component.html',
  styleUrls: ['./article-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [FormatDatePipe],
  imports: [
    NgIf,
    AsyncPipe,
    NsoAdultComponent,
    NgTemplateOutlet,
    NsoArticleHeaderComponent,
    NsoProtectedContentUserInfoComponent,
    NsoWysiwygBoxComponent,
    NsoProtectedContentNavigatorComponent,
    NsoSocialButtonsComponent,
    NsoTagsComponent,
    NgForOf,
    NsoArticleCardComponent,
    FocusPointDirective,
    NsoSimpleButtonComponent,
    RouterLink,
    NativtereloComponent,
    SidebarComponent,
    NgSwitch,
    NgSwitchCase,
    ArticleFileLinkDirective,
    NsoVotingComponent,
    NsoMultiVotingComponent,
    NsoQuizComponent,
    ArticleVideoComponent,
    NsoGalleryCardComponent,
    NsoArticlesRelatedContentComponent,
    ArticleDossierComponent,
    FormatDatePipe,
    NsoPopupComponent,
    OlympicsNewsFeedArticleListComponent,
    NsoEbDailyProgramComponent,
    NsoEbSingleEliminationComponent,
    NsoEbTeamsComponent,
  ],
})
export class ArticlePageComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('dataTrigger', { static: false }) readonly dataTrigger: ElementRef<HTMLDivElement>;
  @ViewChild('externalRecommendationsBlock', { static: false }) readonly externalRecommendationsBlock: ElementRef<HTMLDivElement>;

  readonly #iosAppService: IosAppService = inject(IosAppService);
  isIos: Signal<boolean> = this.#iosAppService.isIosApp;
  article: Article;
  relatedArticles: ArticleCard[] = [];
  galleries: Record<string, GalleryData> = {};
  categoryArticlesFullList: ArticleCard[] = [];
  externalRecommendation: ArticleCard[] = [];
  isUserAdultChoice: boolean;
  articleSlug: string;
  categorySlug: string;
  metaData: IMetaData;
  articleLink: string[] = [];
  comments$: Observable<CommentListResponse | null>;
  readonly ArticleBodyType = ArticleBodyType;
  readonly ArticleCardType = ArticleCardType;
  readonly MinuteToMinuteState = MinuteToMinuteState;
  isProtectedContentLoading = false;
  readonly minuteToMinuteRealtime$: Observable<MinuteToMinuteBlock[]> = this.utilsService.isBrowser()
    ? interval(30_000).pipe(switchMap(() => this.minuteToMinuteService.getBlocks(this.article)))
    : of([]);
  readonly routeData = this.route.data.pipe(map(({ data }) => data)) as Observable<ArticleResolverData>;
  readonly minuteToMinutes$: Observable<MinuteToMinuteBlock[]> = merge(
    this.minuteToMinuteService.extractBlocksFrom(this.routeData, (routeData) => routeData?.article?.data),
    this.minuteToMinuteRealtime$
  );
  isEbEnabled = false;
  isOlympicsArticle = signal(false);
  isOlympicsNewsFeed = signal(false);
  olympicsNewsFeedArticles = signal<Article[]>([]);
  embedPrAdvert?: SafeHtml;
  isLoading = false;
  isLoggedIn = false;
  isAlreadyReacted = false;
  isReactionDisabled = false;
  showNotLoggedInPopup = false;
  showAlreadyReactedPopup = false;
  tereloUrl: string =
    'https://terelo.mediaworks.hu/nativterelo/nativterelo.html?utmSource=nemzetisport.hu' +
    '&traffickingPlatforms=Nemzetisport%20Nat%C3%ADv' +
    '&domain=Nemzeti%20Sport';
  showCompleteArticlePage$: Observable<boolean> = this.route.data.pipe(
    map((data) => data['isMobileApp']),
    map((onlyBody) => !onlyBody)
  );
  private readonly destroy$ = new Subject<void>();
  private readonly unSubscribe$: Subject<boolean> = new Subject();
  private canonicalUrl: string;

  readonly multiVoteCache = this.multiVotingCacheService.multiVoteCache;
  readonly voteCache = this.voteService.voteCache;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly utilsService: UtilService,
    private readonly schemaService: SchemaOrgService,
    private readonly storage: StorageService,
    private readonly cdr: ChangeDetectorRef,
    private readonly articleService: ArticleService,
    private readonly galleryService: GalleryApiService,
    private readonly voteService: VoteService,
    private readonly protectedContentService: ProtectedContentService,
    private readonly commentService: ArticleCommentsService,
    public readonly radioService: RadioService,
    private readonly analyticsService: AnalyticsService,
    private readonly formatDate: FormatDatePipe,
    private readonly advertService: AdvertService,
    private readonly minuteToMinuteService: MinuteToMinuteService,
    private readonly personalizedRecommendationService: PersonalizedRecommendationService,
    private readonly sanitizer: DomSanitizer,
    private readonly portalConfigService: PortalConfigService,
    private readonly authService: AuthService,
    private readonly router: Router,
    private readonly secureApi: SecureApiService,
    private readonly newsFeedService: NewsFeedService,
    private readonly multiVotingCacheService: MultiVotingCacheService,
    protected readonly multiVoteService: MultiVoteService
  ) {
    effect(() => {
      const isOlympicsNewsFeed = this.isOlympicsNewsFeed();
      if (isOlympicsNewsFeed && this.article?.dossier?.slug) {
        this.newsFeedService
          .getNewsFeed(this.article.dossier.slug, 0, 11)
          .pipe(share(), take(1))
          .subscribe(({ data }) => {
            // We need to query 11 articles in order to filter out the current article from the list.
            // We also need to check if the current article is not in the list, in that case we need to trim to have only 10 articles.
            const filteredArticles = data.filter((article) => article.id !== this.article.id);
            this.olympicsNewsFeedArticles.set(filteredArticles?.length > 10 ? filteredArticles.slice(0, 10) : filteredArticles);
          });
      }
    });
  }

  get showCompleteArticlePage(): boolean {
    return !this.route.snapshot.data?.['isMobileApp'];
  }

  ngOnInit(): void {
    this.isEbEnabled = this.portalConfigService.isConfigSet(PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS);

    this.routeData.pipe(takeUntil(this.unSubscribe$)).subscribe(({ article, recommendations, relatedArticles, articleSlug, categorySlug, url }) => {
      this.article = {
        ...article?.data,
        slug: articleSlug,
        body: this.articleService.prepareArticleBody(article?.data?.body, this.showCompleteArticlePage),
        excerpt: article?.data?.lead || article?.data?.excerpt,
        articleSource: this.transformArticleSource(article?.data?.articleSource || ''),
      };
      if (!this.article) {
        return;
      }

      this.embedPrAdvert = this.sanitizer.bypassSecurityTrustHtml(this.article?.embedPrAdvert ?? '');

      this.comments$ = this.article.isCommentsDisabled ? of(null) : this.commentService.getCommentsFor(this.article.id, 'article');

      this.relatedArticles = (relatedArticles?.data as [])?.map(backendRecommendedArticleToArticleCard);

      const userChoice = this.storage.getSessionStorageData('isAdultChoice', false);
      this.isUserAdultChoice = (userChoice ?? false) && this.article.isAdultsOnly;

      this.articleSlug = articleSlug;
      this.categorySlug = categorySlug || this.article?.primaryColumn?.slug;
      this.articleLink = this.article ? buildArticleUrl(this.article) : [];
      this.isReactionDisabled = (this.article as ArticleCardWithSocial)?.isLikesAndDislikesDisabled as boolean;

      this.isOlympicsArticle.set((this.article as any).isOlympic);

      if (this.utilsService.isBrowser()) {
        const multiVoteItems = article.data?.body?.filter((element: any) => element.type === ArticleBodyType.MultiVoting);
        for (const item of multiVoteItems) {
          this.multiVotingCacheService.setMultiVote(item.details?.[0]?.value?.id, item.details?.[0]?.value?.endDate);
        }
      }

      this.categoryArticlesFullList =
        recommendations?.data?.categoryArticles && recommendations?.data?.categoryArticles.length > 0 ? recommendations?.data?.categoryArticles : [];
      this.externalRecommendation = recommendations?.data?.externalRecommendation;
      this.getPersonalizedRecommendations();

      if (this.article?.videoLead?.video) {
        this.article.videoLead.video = {
          ...this.article.videoLead.video,
          coverImageUrl: this.article.videoLead.video?.coverImageUrl || '/assets/images/nemzetisport.png',
        };
      }

      if (this.article?.dossier?.slug === 'olimpia-hirfolyam') {
        this.isOlympicsNewsFeed.set(true);
      }

      this.canonicalUrl = this.article.seo?.seoCanonicalUrl || this.article?.canonicalUrl || `${this.seo.hostUrl}/${url}`;
      this.seo.updateCanonicalUrl(this.canonicalUrl ?? '', { addHostUrl: false, skipSeoMetaCheck: true });

      this.setMetaData();

      if (this.article) {
        this.schemaService.removeStructuredData(); // Only NewsArticle needed, we need to skip the WebPage schema for client request
        this.schemaService.insertSchema(getStructuredDataForArticle(this.article, this.seo.currentUrl, environment?.siteUrl ?? ''));
      }
      this.loadEmbeddedGalleries();

      this.setIsLoggedIn();

      this.handleProtectedArticle();

      this.handleAdDisabling();

      this.voteService.initArticleVotes(this.article);

      this.cdr.markForCheck();

      setTimeout(() => {
        this.analyticsService.sendPageView({
          pageCategory: this.categorySlug,
          customDim2: this.article?.topicLevel1,
          customDim1: this.article?.aniCode,
          title: this.article.title,
          articleSource: this.article.articleSource ? this.article.articleSource : 'no source',
          publishDate: this.formatDate.transform(this.article.publishDate as Date, 'dateTime'),
          lastUpdatedDate: this.formatDate.transform((this.article.lastUpdated ? this.article.lastUpdated : this.article.publishDate) as Date, 'dateTime'),
        });
      }, 0);
    });
  }

  ngAfterViewInit(): void {
    if (this.utilsService.isBrowser()) {
      setTimeout(() => {
        this.route.data.pipe(takeUntil(this.destroy$)).subscribe(() => {
          if ('IntersectionObserver' in window) {
            this.observeExternalRecommendations();
            this.observeArticleEnd();
          }
        });
      }, 1000);
    }
  }

  ngOnDestroy(): void {
    this.schemaService.removeStructuredData();
    this.destroy$.next();
    this.destroy$.complete();

    if (this.article?.withoutAds) {
      this.advertService.enableAds();
    }
  }

  handleReactionClicked(): void {
    this.showNotLoggedInPopup = !this.isLoggedIn;
    this.showAlreadyReactedPopup = this.isAlreadyReacted;

    if (this.isLoading || this.showNotLoggedInPopup || this.showAlreadyReactedPopup) {
      return;
    }

    this.isLoading = true;
    this.secureApi
      .postReaction(this.article.id)
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => {
          this.article = {
            ...this.article,
            likeCount: Number(this.article.likeCount) + 1,
          };
          this.isLoading = false;
          this.isAlreadyReacted = true;
          this.cdr.markForCheck();
        })
      )
      .subscribe();
  }

  handleNotLoggedInUserReaction(accepted: boolean): void {
    this.showNotLoggedInPopup = false;
    if (!accepted) {
      return;
    }
    this.storage.setLocalStorageData('redirectUrl', buildArticleUrl(this.article).join('/'));
    this.router.navigate(['/bejelentkezes']).then();
  }

  onVotingSubmit($event: string, voteData: VoteDataWithAnswer): void {
    this.voteService.onVotingSubmit($event, voteData).subscribe(() => this.cdr.detectChanges());
  }

  onMultiVotingSubmit($event: Record<string, string>, voteData: MultiVoteDataWithVotedId): void {
    this.multiVoteService.onVotingSubmit($event, voteData).subscribe(() => this.cdr.detectChanges());
  }

  onIsUserAdultChoose(isUserAdult: boolean): void {
    this.isUserAdultChoice = isUserAdult;
    // this.initDataLayer();
  }

  doubleArticleRecommendations(arr: ArticleBodyDetails[]): ArticleBodyDetails[] {
    return arr.filter((elem: ArticleBodyDetails) => elem?.value?.id);
  }

  doubleArticleRecommendation(articleBodyDetails: ArticleBodyDetails[]): ArticleCard[] {
    return this.doubleArticleRecommendations(articleBodyDetails)?.map((details: ArticleBodyDetails) => {
      const article = details.value;
      if (article.thumbnailUrl) {
        article.thumbnail = { url: article?.thumbnailUrl };
      }
      return {
        ...article,
        lead: article?.excerpt || article?.lead,
        category: {
          name: article?.primaryColumn?.title,
          slug: article?.primaryColumn?.slug,
        },
        thumbnailFocusedImages: article?.thumbnailUrlFocusedImages,
      };
    }) as ArticleCard[];
  }

  transformArticleSource(source: string): string {
    if (!source) {
      return '';
    }

    const whiteList = ['www.', 'http://', 'https://'];

    if (!whiteList.some((item) => source?.startsWith(item))) return '';
    if (source?.startsWith('www.')) source = 'https://' + source.slice(4);

    return source;
  }

  getPersonalizedRecommendations(): void {
    if (this.utilsService.isBrowser()) {
      this.personalizedRecommendationService
        .getPersonalizedRecommendations()
        .pipe(takeUntil(this.unSubscribe$))
        .subscribe((data: ArticleCard[]): void => {
          this.externalRecommendation = data;
          this.cdr.detectChanges();
        });
    }
  }

  private setIsLoggedIn(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }

    this.authService
      .isAuthenticated()
      .pipe(
        switchMap((isLoggedIn) => {
          this.isLoggedIn = isLoggedIn;
          return isLoggedIn ? this.secureApi.getJudgement(this.article.id) : of({ data: { like: false } });
        }),
        takeUntil(this.destroy$)
      )
      .subscribe(({ data: { like } }) => {
        this.isAlreadyReacted = like;
        this.cdr.markForCheck();
      });
  }

  private loadEmbeddedGalleries(): void {
    const galleries$ = ((this.article?.body ?? []) as GalleryElementData[])
      .filter(({ type }) => type === ArticleBodyType.Gallery)
      .filter((bodyElem) => !!bodyElem.details[0].value)
      .map((body) => this.galleryService.getGalleryDetails(body?.details?.[0]?.value?.slug));

    forkJoin(galleries$)
      .pipe(takeUntil(this.unSubscribe$))
      .subscribe((galleries) => {
        galleries?.forEach((gallery) => {
          if (!gallery?.id) {
            return;
          }
          this.galleries[gallery.id] = {
            ...gallery,
            highlightedImageUrl: gallery?.highlightedImage?.url,
          } as GalleryData;

          this.cdr.markForCheck();
        });
      });
  }

  private setMetaData(): void {
    const { thumbnail, publicAuthor, publishDate, alternativeTitle, metaThumbnail, seo } = this.article || {};

    if (!this.article) {
      return;
    }

    const title = this.article.seo?.seoTitle || this.article.title;

    const seoFacebookTitle = seo?.facebookTitle ?? '';
    const seoImage = seo?.seoImage ?? '';

    const finalTitle = alternativeTitle && alternativeTitle.length > 0 ? alternativeTitle : title;
    this.metaData = {
      ...defaultMetaInfo,
      title: finalTitle,
      description: this.article.seo?.seoDescription || this.article.excerpt || this.article.lead || defaultMetaInfo.description,
      robots: this.getRobotsMeta(),
      ogTitle: seoFacebookTitle || finalTitle,
      ogImage: seoImage || metaThumbnail || thumbnail,
      ogType: 'article',
      articleAuthor: publicAuthor,
      articlePublishedTime: publishDate?.toISOString(),
    };

    if (!this.showCompleteArticlePage) {
      this.metaData = { robots: 'noindex, nofollow' };
    }

    this.seo.setMetaData(this.metaData);
  }

  private getRobotsMeta(): string {
    if (this.article.seo?.seoRobotsMeta) {
      return this.article.seo?.seoRobotsMeta;
    }
    if (this.article.robotsTag && this.article.robotsTag !== 'index, follow') {
      // By default, BE sends 'index, follow' for the columns seo Robots value, but we want to also have max-image-preview:large
      return this.article.robotsTag;
    }
    return 'index, follow, max-image-preview:large';
  }

  private handleProtectedArticle(): void {
    if (this.article.isProtectedContent) {
      this.protectedContentService.zoePermissionStatus$
        .pipe(
          distinctUntilChanged(),
          switchMap((status: boolean) => {
            this.isProtectedContentLoading = true;
            return status
              ? this.protectedContentService.getProtectedArticleBody((token: string | null) =>
                  this.articleService.getArticle(this.categorySlug, `${this.article.year}`, `${this.article.month}`, this.articleSlug, token)
                )
              : of([]);
          }),
          takeUntil(this.unSubscribe$)
        )
        .subscribe((body: ArticleBody[]) => {
          this.article = {
            ...this.article,
            body,
          };
          this.isProtectedContentLoading = false;
          this.cdr.detectChanges();
        });

      if (!this.protectedContentService.validateToken()) {
        this.protectedContentService.openLoginModal();
      }
    }
  }

  private observeArticleEnd(): void {
    if (!this.dataTrigger?.nativeElement) {
      return;
    }

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(({ isIntersecting }) => {
        if (isIntersecting) {
          this.sendEcommerceEvent();
          observer.unobserve(this.dataTrigger.nativeElement);
        }
      });
    });
    observer.observe(this.dataTrigger.nativeElement);
  }

  private sendEcommerceEvent(): void {
    const routeParams: ArticleRouteParams = this.route.snapshot.params as ArticleRouteParams;
    this.analyticsService.sendEcommerceEvent({
      id: `T${this.article.id}`,
      title: this.article.title,
      articleSlug: routeParams.articleSlug ? routeParams.articleSlug : 'cikk-elonezet',
      category: this.article.columnTitle ?? '',
      articleSource: this.article.articleSource ? this.article.articleSource : 'no source',
      publishDate: this.formatDate.transform(this.article.publishDate as Date, 'dateTime'),
      lastUpdatedDate: this.formatDate.transform((this.article.lastUpdated ? this.article.lastUpdated : this.article.publishDate) as Date, 'dateTime'),
    });
  }

  private handleAdDisabling(): void {
    if (!this.article.withoutAds === this.advertService.enableAdsSubject.getValue()) {
      return;
    }

    if (this.article.withoutAds) {
      this.advertService.disableAds();
    } else {
      this.advertService.enableAds();
    }
    if (!this.utilsService.isBrowser()) {
      return;
    }
    window.adsDoNotServeAds = this.article.withoutAds;
  }

  private observeExternalRecommendations(): void {
    if (!this.externalRecommendationsBlock?.nativeElement) {
      return;
    }

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(({ isIntersecting }) => {
        if (isIntersecting && this.externalRecommendation) {
          this.personalizedRecommendationService.sendPersonalizedRecommendationAv(this.externalRecommendation).subscribe();
          observer.unobserve(this.externalRecommendationsBlock.nativeElement);
        }
      });
    });
    observer.observe(this.externalRecommendationsBlock.nativeElement);
  }
}
