<kesma-layout
  [blockSeparatorRef]="blockSeparator"
  [blockTitleRef]="blockTitles"
  [breakingNews]="breakingNews"
  [configuration]="configuration"
  [contentComponentsRef]="contentComponents"
  [contentComponentWrapperRef]="contentComponentsWrapper"
  [contentComponentInnerWrapperRef]="contentComponentsInnerWrapper"
  [blockTitleWrapperRef]="blockTitleWrapper"
  [layoutType]="layoutType"
  [structure]="structure"
  [editorFrameSize]="editorFrameSize"
>
</kesma-layout>

<ng-template #blockTitles let-desktopWidth="desktopWidth" let-layoutElement="layoutElement" let-layoutType="layoutType">
  <nso-block-title-row [data]="layoutElement.blockTitle" [desktopWidth]="desktopWidth"></nso-block-title-row>
</ng-template>

<ng-template #blockSeparator let-layoutElement="layoutElement">
  <hr class="block-separator" />
</ng-template>

<ng-template #contentComponents let-desktopWidth="desktopWidth" let-extractor="extractor" let-index="index" let-layoutElement="layoutElement">
  <ng-container *ngIf="layoutElement?.config || layoutElement?.configurable === false">
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.UPCOMING_MATCHES && index === 0">
      <nso-latest-results
        [blockTitle]="'Közelgő mérkőzések'"
        [data]="getUpcomingMatchesData(layoutElement.config.selectedCompetition?.slug, layoutElement.id, layoutElement.contentLength) | async"
        [styleID]="1"
      ></nso-latest-results>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.CHAMPIONSHIP_TABLE && index === 0">
      <nso-table-widget
        [data]="getChampionshipTableData(layoutElement.config.selectedChampionship?.id, layoutElement.id) | async"
        [leagueSlug]="layoutElement.config.selectedChampionship?.slug"
        [leagueThumbnailUrl]="layoutElement.config.selectedChampionship?.logoThumbnailUrl"
        [title]="layoutElement.config.selectedChampionship?.title"
      ></nso-table-widget>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.ARTICLES_WITH_VIDEO_CONTENT">
      <nso-article-video-box *ngIf="layoutElement.extractorData as data" [blockTitle]="layoutElement?.blockTitle" [data]="data"></nso-article-video-box>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.LEAD_EDITORS">
      <nso-lead-editors [data]="layoutElement?.config?.editors"></nso-lead-editors>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.TEAMS">
      <!-- Meglévő csapatok doboz konfigurációval -->
      <nso-teams-box *ngIf="!layoutElement?.hideConfiguration" [data]="layoutElement?.config"></nso-teams-box>

      <!-- EB-s -->
      <app-eb-teams *ngIf="layoutElement?.hideConfiguration && isEBEnabled" [desktopWidth]="desktopWidth"></app-eb-teams>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Opinion">
      <nso-opinion-card *ngIf="layoutElement?.extractorData?.[index] as data" [data]="data" [styleID]="layoutElement.styleId"></nso-opinion-card>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Article">
      <!-- [isSidebar]="layoutType === LayoutPageTypes.SIDEBAR" -->
      <nso-article-card
        *ngIf="layoutElement.extractorData?.[index] as data"
        [data]="data"
        [desktopWidth]="desktopWidth"
        [styleID]="layoutElement.styleId"
        nso-article-card
      ></nso-article-card>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.FastNews">
      !!! ARTICLE CARD - FASTNEWS !!!
      <!--
      <article nso-article-card
         *ngIf="getData('getFastNewsData', layoutElement, extractor, index) as data"
         [styleID]="layoutElement.styleId"
         [data]="data"
         [isSidebar]="layoutType === LayoutPageTypes.SIDEBAR"
      ></article>
      -->
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.PrBlock">
      !!! ARTICLE CARD - PR BLOCK !!!
      <!-- [isSidebar]="layoutType === LayoutPageTypes.SIDEBAR"
      <article nso-article-card
        *ngIf="getData('getArticleData', layoutElement, extractor, index) as data"
        [styleID]="layoutElement.styleId"
        [data]="data">
      </article>
      -->
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.BLOCK_SEPARATOR">
      <hr class="block-separator" />
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Gallery">
      <nso-gallery-card *ngIf="layoutElement.extractorData?.[index] as data" [data]="data" [showCount]="false" [showDate]="false"></nso-gallery-card>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Wysiwyg">
      !!! WYSIWYG BOX!!!
      <!--
      <nso-wysiwyg-box [htmlArray]="getData('getWysiwyg', layoutElement, extractor)"></nso-wysiwyg-box>
      -->
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Dossier">
      !!! DOSSIER !!!
      <!--
      <nso-dossier-card
        [data]="getData('getDossierData', layoutElement, extractor)">
      </nso-dossier-card>
      -->
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Ad"> </ng-container>

    <ng-container *ngIf="layoutElement.contentType === 'google-ad'">
      <app-strossle-advert [element]="layoutElement"></app-strossle-advert>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.NewsletterBlock">
      <nso-subscription [desktopWidth]="desktopWidth" [isSidebar]="layoutType === LayoutPageTypes.SIDEBAR"></nso-subscription>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Image">
      !!! IMAGE BOX!!!
      <!--
      <nso-image-box [layoutImageData]="getData('getImage', layoutElement, extractor)"></nso-image-box>
      -->
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Vote">
      @if (layoutElement.extractorData; as extractorData) {
        @if ((voteCache[extractorData?.data?.id] | async) || extractorData; as voteData) {
          <nso-voting (vote)="onVotingSubmit($event, voteData)" [data]="voteData.data" [desktopWidth]="desktopWidth" [voteId]="voteData.votedId" />
        }
      }
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.MULTI_VOTE">
      <ng-container *ngIf="layoutElement.extractorData as voteData">
        <nso-multi-voting
          (vote)="onMultiVotingSubmit($event, voteData)"
          [data]="(multiVoteData?.[voteData?.data?.id] | async)?.data ?? voteData?.data"
          [desktopWidth]="layoutType === LayoutPageTypes.SIDEBAR ? 1 : desktopWidth"
          [showResults]="multiVoteService.hasVoted(voteData)"
          [userVotes]="(multiVoteData?.[voteData?.data?.id] | async)?.voteIds ?? voteData.voteIds"
        >
        </nso-multi-voting>
      </ng-container>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Breaking">
      !!! BREAKING CARD !!!
      <!--
      <nso-breaking-article-card
        [hasImage]="layoutElement.hasImage"
        [styleID]="layoutElement.styleId"
        [data]="getData('getBreakingBlockData', layoutElement, extractor)">
      </nso-breaking-article-card>
      -->
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Quiz">
      <ng-container *ngIf="layoutElement.extractorData as data">
        <nso-quiz *ngIf="data?.id" [desktopWidth]="layoutType === LayoutPageTypes.SIDEBAR ? 1 : desktopWidth" [data]="data"></nso-quiz>
      </ng-container>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.VideoBlock">
      <nso-video-box *ngIf="layoutElement.extractorData as data" [data]="data" [blockTitle]="layoutElement?.blockTitle"></nso-video-box>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Video">
      <nso-video-card *ngIf="layoutElement?.extractorData?.[index] as data" [data]="data" [styleID]="layoutElement.styleId"></nso-video-card>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.HtmlEmbed">
      <kesma-html-embed *ngIf="getData('getHtmlEmbedData', layoutElement, extractor) as data" [data]="data"></kesma-html-embed>
    </ng-container>
  </ng-container>
  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.DID_YOU_KNOW">
    <app-variable-sponsored-did-you-know-wrapper [id]="layoutElement.config?.selectedDidYouKnowBox?.[0]?.id"></app-variable-sponsored-did-you-know-wrapper>
  </ng-container>
  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.DATA_BANK">
    <nso-competition-card *ngIf="layoutElement?.extractorData?.[index] as data" [data]="data"></nso-competition-card>
  </ng-container>
  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.SPORT_RADIO_PLAYER">
    <nso-sport-radio
      (playingChange)="radioService.radioClick()"
      (volumeChange)="radioService.volumeChange($event)"
      [desktopWidth]="desktopWidth"
      [playing]="(radioService.isPlaying$ | async) || false"
    ></nso-sport-radio>
  </ng-container>
  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.ARTICLE_BLOCK && index === 0">
    <nso-article-block *ngIf="layoutElement.extractorData as data" [data]="data"></nso-article-block>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.BrandingBoxEx">
    <app-external-branding-box [type]="layoutElement.brand"></app-external-branding-box>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.NEWSPAPER">
    <nso-newspaper-box *ngIf="getNewspaperBoxData() | async as data" [data]="data"></nso-newspaper-box>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === 'trip-box'">
    <app-trip-box-adapter [data]="layoutElement.config"></app-trip-box-adapter>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === 'visitor-counter'">
    <nso-audience-widget [views]="activeUsersCount"></nso-audience-widget>
  </ng-container>
  <ng-container *ngIf="layoutElement.contentType === 'did-you-know'">
    <ng-container *ngIf="getDidYouKnowData() as data">
      <ng-container *ngIf="isEBEnabled; else simpleDidYouKnowElement">
        <kesma-eb-did-you-know [styleID]="EBPortalEnum.NSO" [data]="data"></kesma-eb-did-you-know>
      </ng-container>
      <ng-template #simpleDidYouKnowElement>
        <kesma-did-you-know [styleId]="DidYouKnowType.NSO" [data]="data"></kesma-did-you-know>
      </ng-template>
    </ng-container>
  </ng-container>
  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.LATEST_NEWS">
    <nso-latest-news-block *ngIf="layoutElement.extractorData as data" [data]="data"></nso-latest-news-block>
  </ng-container>
  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.BRANDING_BOX_ARTICLE && index === 0">
    <nso-brandingbox-article *ngIf="layoutElement.extractorData as data" [data]="data"></nso-brandingbox-article>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.COUNTDOWN_BOX">
    <nso-countdown-box *ngIf="layoutElement.extractorData as data" [data]="data" [endDate]="data.endDate" [exactTime]="data.exactTime"></nso-countdown-box>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.EB_SINGLE_ELIMINATION && isEBEnabled">
    <app-eb-single-elimination></app-eb-single-elimination>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.DAILY_PROGRAM && isEBEnabled">
    <app-eb-daily-program></app-eb-daily-program>
  </ng-container>
</ng-template>

<ng-container *ngIf="layoutType === LayoutPageTypes.HOME">
  <app-origo-sport-branding-box></app-origo-sport-branding-box>
</ng-container>
