import { AsyncPipe, NgIf } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, inject, Input, OnDestroy, OnInit, Signal, TemplateRef, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { UtilService } from '@trendency/kesma-core';
import {
  ArticleCard,
  BreakingNews,
  DidYouKnow,
  DidYouKnowComponent,
  DidYouKnowType,
  EbDidYouKnowComponent,
  EBPortalEnum,
  HtmlEmbedComponent,
  LayoutComponent as KesmaLayoutComponent,
  LayoutContentParams,
  LayoutElement,
  LayoutElementContentConfiguration,
  LayoutElementContentType,
  LayoutElementRow,
  LayoutPageType,
  MultiVoteDataWithVotedId,
  MultiVoteService,
  PortalConfigSetting,
  provideLayoutDataExtractors,
  ThumbnailImage,
  VoteDataWithAnswer,
  VoteService,
} from '@trendency/kesma-ui';
import { BehaviorSubject, Subject } from 'rxjs';
import { map, takeUntil, tap } from 'rxjs/operators';
import { RadioService } from 'src/app/shared/services/radio.service';
import {
  ApiService,
  IosAppService,
  LeagueTable,
  MatchData,
  NsoArticleCardBlockComponent,
  NsoArticleCardComponent,
  NsoArticleVideoBoxComponent,
  NsoAudienceWidgetComponent,
  NsoBlockTitleRowComponent,
  NsoBrandingboxArticleComponent,
  NsoCompetitionCardComponent,
  NsoCountdownBoxComponent,
  NsoEbDailyProgramComponent,
  NsoEbSingleEliminationComponent,
  NsoEbTeamsComponent,
  NsoGalleryCardComponent,
  NsoLatestNewsBlockComponent,
  NsoLatestResultsComponent,
  NsoLeadEditorsComponent,
  NsoMultiVotingComponent,
  NsoNewspaperBoxComponent,
  NsoOpinionCardComponent,
  NsoQuizComponent,
  NsoSportRadioComponent,
  NsoSubscriptionComponent,
  NsoTableWidgetComponent,
  NsoTeamsBoxComponent,
  NsoVideoBoxComponent,
  NsoVideoCardComponent,
  NsoVotingComponent,
  OrigoSportBrandingBoxComponent,
  PortalConfigService,
  StrossleAdvertComponent,
  TripBoxAdapterComponent,
} from '../../../../shared';
import { MultiVotingCacheService } from '../../../../shared/services/multi-voting-cache.service';
import { NSO_EXTRACTORS_CONFIG } from '../../extractors/extractor.config';
import { ChampionshipTableService } from '../../services/championship-table.service';
import { UpcomingMatchesService } from '../../services/upcoming-matches.service';
import { ExternalBrandingBoxComponent } from '../external-branding-box/external-branding-box.component';
import { VariableSponsoredDidYouKnowWrapperComponent } from '../../../../shared/components/variable-sponsored-did-you-know-wrapper/variable-sponsored-did-you-know-wrapper.component';

@Component({
  selector: 'app-layout',
  templateUrl: './layout.component.html',
  styleUrls: ['./layout.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [provideLayoutDataExtractors(NSO_EXTRACTORS_CONFIG, true)],
  imports: [
    KesmaLayoutComponent,
    NsoBlockTitleRowComponent,
    NgIf,
    NsoLatestResultsComponent,
    NsoTableWidgetComponent,
    AsyncPipe,
    NsoArticleVideoBoxComponent,
    NsoLeadEditorsComponent,
    NsoTeamsBoxComponent,
    NsoOpinionCardComponent,
    NsoArticleCardComponent,
    NsoGalleryCardComponent,
    StrossleAdvertComponent,
    NsoSubscriptionComponent,
    NsoVotingComponent,
    NsoMultiVotingComponent,
    NsoQuizComponent,
    NsoVideoBoxComponent,
    NsoVideoCardComponent,
    HtmlEmbedComponent,
    NsoCompetitionCardComponent,
    NsoSportRadioComponent,
    NsoArticleCardBlockComponent,
    ExternalBrandingBoxComponent,
    NsoNewspaperBoxComponent,
    TripBoxAdapterComponent,
    NsoAudienceWidgetComponent,
    EbDidYouKnowComponent,
    DidYouKnowComponent,
    NsoLatestNewsBlockComponent,
    NsoBrandingboxArticleComponent,
    NsoCountdownBoxComponent,
    OrigoSportBrandingBoxComponent,
    NsoEbDailyProgramComponent,
    NsoEbSingleEliminationComponent,
    NsoEbTeamsComponent,
    VariableSponsoredDidYouKnowWrapperComponent,
  ],
})
export class LayoutComponent implements OnInit, OnDestroy {
  @Input() structure: LayoutElementRow[];
  @Input() configuration: LayoutElementContentConfiguration[];
  @Input() cachePrefix = '';
  @Input() layoutType?: LayoutPageType;
  @Input() breakingNews: BreakingNews[] = [];
  @Input() contentComponentsWrapper: TemplateRef<any>;
  @Input() contentComponentsInnerWrapper: TemplateRef<any>;
  @Input() blockTitleWrapper: TemplateRef<any>;
  @Input() editorFrameSize?: 'desktop' | 'mobile';

  @ViewChild('contentComponents', {
    read: TemplateRef,
    static: false,
  })
  contentComponents: TemplateRef<LayoutContentParams>;

  readonly #iosAppService: IosAppService = inject(IosAppService);
  isIos: Signal<boolean> = this.#iosAppService.isIosApp;

  readonly LayoutPageTypes = LayoutPageType;
  readonly LayoutElementContentType = LayoutElementContentType;
  readonly EBPortalEnum = EBPortalEnum;
  readonly DidYouKnowType = DidYouKnowType;

  newsPaperBoxData$ = new BehaviorSubject<ArticleCard | undefined>(undefined);
  private readonly destroy$ = new Subject();
  private extractedData: any = {};
  private newsPaperBoxDataRequested = false;
  activeUsersCount = 0;
  articleLimit = 12;
  isEBEnabled = false;

  didYouKnow: DidYouKnow | undefined = undefined;
  readonly multiVoteData = this.multiVotingCacheService.multiVoteCache;
  readonly voteCache = this.voteService.voteCache;

  constructor(
    private readonly changeDetector: ChangeDetectorRef,
    public readonly voteService: VoteService,
    public readonly multiVoteService: MultiVoteService,
    public readonly radioService: RadioService,
    private readonly apiService: ApiService,
    private readonly upcomingMatches: UpcomingMatchesService,
    private readonly championshipTables: ChampionshipTableService,
    private readonly route: ActivatedRoute,
    private readonly portalConfigService: PortalConfigService,
    private readonly utilService: UtilService,
    private readonly cdr: ChangeDetectorRef,
    private readonly multiVotingCacheService: MultiVotingCacheService
  ) {
    this.isEBEnabled = this.portalConfigService.isConfigSet(PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS);
  }

  ngOnInit(): void {
    this.getAudienceWidgetData();
  }

  getData(type: string, layoutElement: LayoutElement, extractor: any, index?: number): any {
    const id = `${type}_${this.cachePrefix}_${layoutElement.id}_${index}`;
    if (!this.extractedData[id]) {
      this.extractedData[id] = index || index == 0 ? extractor[type]?.(layoutElement, index) : extractor[type]?.(layoutElement);
    }

    return this.extractedData[id];
  }

  getDidYouKnowData(): DidYouKnow | undefined {
    if (this.didYouKnow) {
      return this.didYouKnow;
    }
    let route = this.route;
    let didYouKnows: DidYouKnow[] | undefined = undefined;
    while (route.parent) {
      const { data } = route.snapshot.data;
      if (data?.init) {
        didYouKnows = data?.init?.didYouKnow;
      }
      route = route.parent;
    }

    if (!didYouKnows?.length) {
      return undefined;
    }
    const randomIndex = Math.floor(Math.random() * didYouKnows.length);
    this.didYouKnow = didYouKnows[randomIndex];
    return this.didYouKnow;
  }

  onVotingSubmit($event: string, voteData: VoteDataWithAnswer): void {
    if (voteData.votedId || voteData.showResults) {
      return;
    }

    this.voteService.onVotingSubmit($event, voteData).subscribe((res) => {
      voteData.data = res.data;
      voteData.votedId = res.votedId;
    });
  }

  onMultiVotingSubmit($event: Record<string, string>, voteData: MultiVoteDataWithVotedId): void {
    this.multiVoteService.onVotingSubmit($event, voteData).subscribe();
  }

  clearExtractedData(): void {
    this.extractedData = {};
  }

  getNewspaperBoxData(): BehaviorSubject<ArticleCard | undefined> {
    if (!this.newsPaperBoxDataRequested) {
      this.newsPaperBoxDataRequested = true;
      this.apiService
        .getCategoryArticles('napilap', 0, 1)
        .pipe(map((res) => res?.data?.[0] as ArticleCard))
        .subscribe((article: ArticleCard & { magazineCover?: string }) => {
          this.newsPaperBoxData$.next({
            ...article,
            thumbnail: article?.magazineCover as unknown as ThumbnailImage,
          });
        });
    }

    return this.newsPaperBoxData$;
  }

  getUpcomingMatchesData(competitionSlug: string, elementId: string, datesToShow: number): BehaviorSubject<MatchData[] | undefined> {
    if (!this.utilService.isBrowser()) {
      return new BehaviorSubject<MatchData[] | undefined>(undefined);
    }
    if (!this.upcomingMatches.upcomingMatchesDataCache[elementId]) {
      this.upcomingMatches.upcomingMatchesDataCache[elementId] = new BehaviorSubject<MatchData[] | undefined>(undefined);
      this.upcomingMatches.getUpcomingMatches(competitionSlug, datesToShow).subscribe((data) => {
        this.upcomingMatches.upcomingMatchesDataCache[elementId].next(data);
      });
    }
    return this.upcomingMatches.upcomingMatchesDataCache[elementId];
  }

  getChampionshipTableData(championshipId: string, elementId: string): BehaviorSubject<LeagueTable[] | undefined> {
    if (!this.utilService.isBrowser()) {
      return new BehaviorSubject<LeagueTable[] | undefined>(undefined);
    }
    if (!this.championshipTables.championshipTableDataCache[elementId]) {
      this.championshipTables.championshipTableDataCache[elementId] = new BehaviorSubject<LeagueTable[] | undefined>(undefined);
      this.championshipTables.getChampionshipTableData(championshipId).subscribe((data) => {
        this.championshipTables.championshipTableDataCache[elementId].next(data.data.teams);
      });
    }
    return this.championshipTables.championshipTableDataCache[elementId];
  }

  getAudienceWidgetData(): void {
    this.apiService
      .getRealTimeStatistics()
      .pipe(
        tap((res) => {
          this.activeUsersCount = res.data?.activeUsersCount;
          this.changeDetector.markForCheck();
        }),
        takeUntil(this.destroy$)
      )
      .subscribe();
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
    this.destroy$.complete();
  }
}
