import { ChangeDetectionStrategy, Component, inject, OnInit } from '@angular/core';
import { ChildActivationEnd, Data, NavigationEnd, Router, RouterOutlet } from '@angular/router';
import { SchemaOrgService, SchemaOrgWebpageDataTemplate, SeoService, UtilService } from '@trendency/kesma-core';
import { AnalyticsService } from '@trendency/kesma-ui';
import { GoogleTagManagerService } from 'angular-google-tag-manager';
import { Observable } from 'rxjs';
import { buffer, filter, map, tap } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import {
  AdvertService,
  ColumnsRssFeedService,
  defaultMetaInfo,
  IosAppService,
  SCRIPT_CONDITIONAL_REMOVE_DATA_KEY,
  ScrollPositionService,
  UrlService,
} from './shared';
import { DOCUMENT } from '@angular/common';

declare global {
  interface Window {
    callAdvertScripts: () => void;
    adsDoNotServeAds?: boolean;
    __adsConfig?: any;
  }
}

@Component({
  selector: 'app-root',
  template: '<router-outlet></router-outlet>',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterOutlet],
})
export class AppComponent implements OnInit {
  isInitialLoad = true;
  private isFirstNavigation = true;
  private readonly document: Document = inject(DOCUMENT);

  readonly #iosAppService: IosAppService = inject(IosAppService);

  constructor(
    private readonly seoService: SeoService,
    private readonly utilsService: UtilService,
    private readonly schemaService: SchemaOrgService,
    private readonly router: Router,
    private readonly analyticsService: AnalyticsService,
    private readonly gtmService: GoogleTagManagerService,
    private readonly urlService: UrlService,
    private readonly advertService: AdvertService,
    private readonly columnsRssFeedService: ColumnsRssFeedService,
    private readonly scrollPositionService: ScrollPositionService
  ) {}

  ngOnInit(): void {
    this.#iosAppService.runIosAppDetector();
    this.seoService.setMetaData(defaultMetaInfo, { skipSeoMetaCheck: true });

    SchemaOrgWebpageDataTemplate.url = this.seoService.hostUrl;
    SchemaOrgWebpageDataTemplate.name = 'Nemzeti Sport';
    SchemaOrgWebpageDataTemplate.image = `${this.seoService.hostUrl}/assets/images/nemzetisport.png`;
    SchemaOrgWebpageDataTemplate.publisher.name = 'N.S. MÉDIA ÉS VAGYONKEZELŐ Kft.';
    SchemaOrgWebpageDataTemplate.publisher.logo = {
      '@type': 'ImageObject',
      height: '100',
      width: '100',
      url: `${this.seoService.hostUrl}/assets/images/ns-avatar.png`,
    };
    this.schemaService.insertSchema(SchemaOrgWebpageDataTemplate);
    this.setupConditionalSSRDomListener();
    if (this.utilsService.isBrowser()) {
      this.setupAnalyticsTracking();
      this.gtmService.addGtmToDom();
      this.scrollPositionService.setupScrollPositionListener();
    }

    (this.router.events.pipe(filter((event) => event instanceof NavigationEnd)) as Observable<NavigationEnd>).subscribe((event: NavigationEnd): void => {
      if (event.url.includes('cikk-elonezet')) {
        this.advertService.removeRtlElements();
        this.advertService.clearAds();

        return;
      }

      if (this.isInitialLoad) {
        this.isInitialLoad = false;
        this.advertService.loadStrossleScript();
      } else {
        this.advertService.callAdvertScriptOnNavigation();
        this.advertService.destroyInterval$.next();
      }
      this.advertService.removeIosAdElementWithDelay();

      this.urlService.setPreviousUrl(event.url);

      if (event.url === '/') {
        this.document.body.classList.add('home');
      } else {
        this.document.body.classList.remove('home');
      }
    });
    if (this.utilsService.isBrowser()) {
      this.columnsRssFeedService.addRSS();
    }
  }

  private setupAnalyticsTracking(): void {
    // Navigation end used to trigger gemius and gtag
    const navigationEnd$: Observable<NavigationEnd> = (this.router.events.pipe(filter((e) => e instanceof NavigationEnd)) as Observable<NavigationEnd>).pipe(
      tap((event: NavigationEnd) => {
        if (!this.isFirstNavigation) {
          // Needs setTimeout in order to have a correct title.
          setTimeout(() => {
            pp_gemius_hit(environment.gemiusId, 'gprism_title=' + this.document.title, 'page=' + event.urlAfterRedirects);
          });
        }
        // wrapped in a setTimeout because the router event fires before we can grab <title> from the page's <head>.
        setTimeout(() => {
          this.isFirstNavigation = false;
        }, 0);
      })
    );

    // Child activationEnd to get the leaf route data in order to see if we send pageViews there.
    (this.router.events.pipe(filter((e) => e instanceof ChildActivationEnd)) as Observable<ChildActivationEnd>)
      .pipe(
        // ChildActivationEnd triggers for every path activation, we need only the leaf so after navigation end
        // we get all of the childActivationEnd events and we only need the first one.
        buffer(navigationEnd$),
        map(([leafNode]: ChildActivationEnd[]) => (leafNode as ChildActivationEnd)?.snapshot?.firstChild?.data),
        map((data?: Data & { omitGlobalPageView?: boolean }) => data?.omitGlobalPageView)
      )
      .subscribe((shouldNotSendGlobalAnalytics?: boolean) => {
        if (shouldNotSendGlobalAnalytics) {
          return;
        }
        setTimeout(() => {
          this.analyticsService.sendPageView();
        }, 100);
      });
  }

  /**
   * Establishes a listener for the data of the current route, allowing us to ascertain whether the provided route
   * is intended for the mobile app. This enables the selective removal of required DOM elements.
   * This process should be executed both in the browser and during Server-Side Rendering (SSR),
   * as the SSR response must not encompass these specific DOM elements. By employing this approach,
   * the browser avoids loading the script tags slated for removal,
   * as they are omitted from the initial HTML response altogether.
   * @private
   */
  private setupConditionalSSRDomListener(): void {
    (this.router.events.pipe(filter((e) => e instanceof ChildActivationEnd)) as Observable<ChildActivationEnd>)
      .pipe(
        buffer(this.router.events.pipe(filter((e) => e instanceof NavigationEnd)) as Observable<NavigationEnd>),
        map(([leafNode]: ChildActivationEnd[]) => (leafNode as ChildActivationEnd)?.snapshot?.firstChild?.data)
      )
      .subscribe((data?: Data) => {
        const shouldSkipSsrConditionalElements = !!data?.['skipSsrConditionalElements'];
        const isMobileApp = !!data?.['isMobileApp'];

        const skipElements = Array.from(this.document.querySelectorAll(`[${SCRIPT_CONDITIONAL_REMOVE_DATA_KEY}]`));
        skipElements.forEach((element: any) => {
          element.removeAttribute(SCRIPT_CONDITIONAL_REMOVE_DATA_KEY);
          if (shouldSkipSsrConditionalElements) {
            element.remove();
          }
        });

        if (isMobileApp) {
          const manifest = this.document.querySelector('link[rel=manifest]') as HTMLLinkElement;
          if (manifest) {
            manifest.href = 'cleaned-manifest.json';
          }
          const themeColor = this.document.querySelector('meta[name=theme-color]');
          if (themeColor) {
            themeColor.setAttribute('content', '#FFFFFF');
          }
        }
      });
  }
}
