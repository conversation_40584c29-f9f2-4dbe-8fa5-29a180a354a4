import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { VotingComponent } from '@trendency/kesma-ui';
import { SheSimpleButtonComponent } from '../simple-button/simple-button.component';
import { NgIf } from '@angular/common';

@Component({
  selector: 'she-voting',
  templateUrl: './voting.component.html',
  styleUrls: ['./voting.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [SheSimpleButtonComponent, NgIf],
})
export class SheVotingComponent extends VotingComponent implements OnInit {
  @Input() isSidebar: boolean = false;
  canGoBack: boolean = true;
  #desktopWidth?: number;

  constructor(private readonly cdr: ChangeDetectorRef) {
    super();
  }

  get desktopWidth(): number {
    return this.#desktopWidth as number;
  }

  @Input() set desktopWidth(desktopWidth: number) {
    this.#desktopWidth = desktopWidth;
    this.cdr.markForCheck();
  }

  get sum(): number {
    return this.data?.voteCountSum ?? this.data?.answers?.reduce((acc, curr) => acc + (Number(curr?.voteCount) ?? 0), 0) ?? 0;
  }

  override ngOnInit(): void {
    this.canGoBack = !(this.showResults || !this.voteId);
    super.ngOnInit();
  }

  override onVote(): void {
    this.canGoBack = false;
    super.onVote();
  }

  watchResult(): void {
    this.voteId = undefined;
    this.canGoBack = true;
    this.showResults = true;
  }
}
