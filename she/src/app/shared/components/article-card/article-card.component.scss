@use 'shared' as *;

:host {
  display: block;
  margin-bottom: 30px;
}

.article-card {
  &-image-link {
    position: relative;
    display: block;

    &-sponsor {
      position: absolute;
      display: block;
      bottom: 0;
      color: var(--kui-white);
      background: var(--kui-black);
      width: 100%;
      padding: 16px;
      font-weight: 700;
    }

    &-adult {
      position: absolute;
      top: 20px;
      left: 20px;
      width: 72px;
      height: 72px;
      padding: 18px;
      background-color: var(--kui-black);
      display: flex;
      align-items: center;
      justify-content: center;

      i {
        width: 100%;
        height: 100%;
      }

      &.small-img {
        top: 12px;
        left: 12px;
        width: 40px;
        height: 40px;
        padding: 9px;
      }
    }
  }

  &-caption {
    font-family: var(--kui-font-primary);

    &-title {
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

:host {
  &.style-VerticalImgTallTitleNoAvatar,
  &.style-VerticalImgRectangleTitleNoAvatar,
  &.style-VerticalImgRectangleTagTitleNoAvatar,
  &.style-VerticalWideImgTitleAvatar,
  &.style-SmallSidedImgAuthorTitle,
  &.style-SmallSidedImgTagTitle,
  &.style-SmallSidedImgAuthorTagDateTitle,
  &.style-VerticalBigImgTagTitleAuthorAvatar,
  &.style-SideBigTagTitleAuthorImgRed,
  &.style-SideBigTagTitleAuthorImgGray,
  &.style-ExternalRecommendation {
    .article-card {
      &-image {
        object-fit: cover;
      }

      &-caption {
        color: var(--kui-white);
        padding: 12px 0;

        @include media-breakpoint-down(sm) {
          padding: 8px 0;
        }

        &-misc {
          text-transform: uppercase;
          margin-bottom: 12px;
          display: flex;
          align-items: center;
          font-weight: 700;

          @include media-breakpoint-down(sm) {
            margin-bottom: 8px;
          }

          &-author {
            text-decoration-line: underline;
            color: var(--kui-black);

            @include media-breakpoint-down(sm) {
              font-size: 12px;
            }

            &.link {
              cursor: pointer;
            }
          }

          &-tag {
            color: var(--kui-white);

            &-wrapper {
              top: 0;
            }
          }

          &-separator {
            display: block;
            width: 4px;
            height: 4px;
            border-radius: 50%;
            background: var(--kui-white);
            margin: 0 7px;
          }
        }
      }

      &-title {
        color: var(--kui-white);
        font-family: var(--kui-font-primary);
        display: inline-block;
        border-bottom: unset;
        font-size: 28px;
        line-height: 1.2;

        &.with-tag {
          margin-top: 5px;
        }

        @include media-breakpoint-down(sm) {
          font-size: 24px;
        }
      }
    }
  }

  &.style-VerticalImgRectangleTitleNoAvatar,
  &.style-VerticalImgRectangleTagTitleNoAvatar,
  &.style-VerticalWideImgTitleAvatar,
  &.style-VerticalBigImgTagTitleAuthorAvatar,
  &.style-SideBigTagTitleAuthorImgRed,
  &.style-SideBigTagTitleAuthorImgGray,
  &.style-SmallSidedImgTagTitle,
  &.style-SmallSidedImgAuthorTagDateTitle,
  &.style-ExternalRecommendation,
  &.style-SmallSidedImgAuthorTitle {
    .article-card {
      &-caption {
        color: var(--kui-black);

        &-misc {
          &-author {
            border-color: var(--kui-black);
          }

          &-tag {
            color: var(--kui-black);
          }

          &-separator {
            color: var(--kui-black);
          }
        }
      }

      &-title {
        color: var(--kui-black);
        font-size: 24px;
        overflow-wrap: break-word;
        width: 100%;
      }

      &-date {
        font-weight: 700;
        margin-top: auto;
      }
    }
  }

  &.style-VerticalWideImgTitleAvatar,
  &.style-VerticalBigImgTagTitleAuthorAvatar,
  &.style-SideBigTagTitleAuthorImgRed,
  &.style-SideBigTagTitleAuthorImgGray {
    .article-card {
      &-caption {
        color: var(--kui-black);

        &-misc {
          &-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 10px;
            object-fit: cover;
          }
        }
      }

      &-title {
        font-size: 28px;
      }
    }
  }

  &.style-VerticalWideImgTitleAvatar {
    .article-card {
      &-title {
        @include media-breakpoint-down(sm) {
          font-size: 24px;
          line-height: 36px;
        }
      }

      &-image {
        width: 100%;

        @include media-breakpoint-down(sm) {
          font-size: 24px;
        }
      }

      &-caption {
        &-misc {
          margin-bottom: 24px;
        }
      }
    }
  }

  &.style-SmallSidedImgAuthorTitle,
  &.style-SmallSidedImgTagTitle,
  &.style-SmallSidedImgAuthorTagDateTitle {
    .article-card {
      &-figure {
        display: flex;
        width: unset;

        .article-card-link {
          flex: 0 0 auto;
        }
      }

      &-image {
        height: 180px;
        width: 180px;
      }

      &-caption {
        padding: unset;
        padding-left: 16px;
        display: flex;
        flex-direction: column;

        &-misc {
          &-separator {
            flex: 0 0 auto;
          }
        }
      }

      &-title {
        @include media-breakpoint-up(sm) {
          font-size: 28px;
          line-height: 1.2;
        }
      }
    }
  }

  &.style-SmallSidedImgTagTitle,
  &.style-VerticalBigImgTagTitleAuthorAvatar,
  &.style-SideBigTagTitleAuthorImgRed,
  &.style-SideBigTagTitleAuthorImgGray {
    .article-card {
      &-caption {
        &-misc {
          &-separator {
            display: none;
          }
        }
      }
    }
  }

  &.style-SmallSidedImgTagTitle,
  &.style-SmallSidedImgAuthorTitle {
    @include media-breakpoint-down(sm) {
      .article-card {
        &-image {
          min-width: 150px;
          height: 150px;
        }

        &-caption {
          padding-left: 8px;
        }

        &-title {
          font-size: 16px;
          line-height: 24px;
        }

        &-caption-misc {
          margin-bottom: 4px;
        }

        &-caption-misc-tag {
          font-size: 12px;
        }
      }
    }
  }

  &.style-VerticalBigImgTagTitleAuthorAvatar {
    @include media-breakpoint-down(sm) {
      width: 100%;
    }

    .article-card {
      &-image {
        width: 100%;
      }

      &-caption {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding: 32px;

        @include media-breakpoint-down(sm) {
          padding: 20px;
        }

        &-misc {
          margin-bottom: 0;

          &-author {
            font-weight: 700;
            text-transform: uppercase;
          }

          &-vertical {
            margin-top: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--kui-black);

            @include media-breakpoint-down(sm) {
              margin-top: 20px;
            }
          }

          &-tag {
            margin-bottom: 16px;

            @include media-breakpoint-down(sm) {
              font-size: 12px;
              margin-bottom: 12px;
            }
          }
        }
      }

      &-title {
        text-align: center;
        font-size: 32px;
        font-family: var(--kui-font-secondary);
        font-weight: 400;

        @include media-breakpoint-down(sm) {
          font-size: 20px;
          line-height: 32px;
        }

        &.with-tag {
          margin-top: unset;
        }
      }
    }
  }

  &.style-SideBigTagTitleAuthorImgRed,
  &.style-SideBigTagTitleAuthorImgGray {
    .article-card {
      &-figure {
        display: flex;
        flex-direction: row-reverse;

        @include media-breakpoint-down(sm) {
          flex-direction: column;
        }

        &.sidebar {
          flex-direction: column;

          .article-card-image-link,
          .article-card-caption {
            width: 100%;
          }
        }
      }

      &-image {
        width: 100%;
        height: 100%;

        @include media-breakpoint-down(sm) {
          max-width: none;
        }

        &-link {
          width: 50%;

          @include media-breakpoint-down(sm) {
            width: 100%;
          }
        }
      }

      &-caption {
        width: 50%;
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        justify-content: center;
        padding: 80px 50px;
        color: var(--kui-white);
        background-color: var(--kui-maroon);

        @include media-breakpoint-down(sm) {
          width: 100%;
          padding: 20px;
        }

        &-misc {
          @include media-breakpoint-down(sm) {
            margin-bottom: 0;
          }

          &-vertical {
            display: flex;
            align-items: center;
            gap: 8px;
          }

          &-avatar {
            margin: 0;
          }

          &-tag {
            margin-bottom: 32px;
            color: var(--kui-white);

            @include media-breakpoint-down(sm) {
              font-size: 12px;
              line-height: 18px;
              margin-bottom: 12px;
            }
          }

          &-author {
            border-color: var(--kui-white);
            color: var(--kui-white);
            text-transform: uppercase;
            font-weight: 700;
          }
        }

        &-misc-vertical {
          margin-top: 48px;
          color: var(--kui-white);
          @include media-breakpoint-down(sm) {
            margin-top: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }
      }

      &-title {
        text-align: center;
        font-size: 32px;
        color: var(--kui-white);
        font-weight: 400;
        font-family: var(--kui-font-secondary);
        line-height: 48px;

        &.with-tag {
          margin-top: unset;
        }

        @include media-breakpoint-down(sm) {
          font-size: 20px;
          font-weight: 400;
          line-height: 32px;
        }
      }
    }
  }

  &.style-SideBigTagTitleAuthorImgGray {
    .article-card {
      &-caption {
        background-color: var(--kui-black);
      }
    }
  }

  &.style-ExternalRecommendation {
    .article-card {
      &-image {
        width: 100%;
      }

      &-caption {
        padding: 0;

        &-link {
          color: var(--kui-black);

          &-source {
            display: block;
            font-size: 16px;
            margin: 12px 0;
            font-weight: 700;

            @include media-breakpoint-down(sm) {
              margin: 8px 0;
              font-size: 12px;
              line-height: 18px;
            }
          }

          &-title {
            font-size: 24px;

            @include media-breakpoint-down(sm) {
              font-size: 16px;
              line-height: 24px;
            }
          }
        }
      }
    }
  }

  &.style-SmallSidedImgAuthorTagDateTitle {
    .article-card {
      @include media-breakpoint-down(sm) {
        &-caption {
          padding-left: 8px;
        }

        &-caption-misc-tag {
          font-size: 12px;
          line-height: 18px;
        }

        &-caption-misc-author {
          font-weight: 700;
        }

        &-date {
          font-size: 12px;
        }
        &-title {
          font-size: 16px;
          line-height: 24px;

          &.with-tag {
            margin-top: 0px;
          }
        }

        &-image {
          height: 150px;
          min-width: 150px;
        }
      }

      &-caption-misc {
        margin-bottom: 4px;
      }

      &-caption {
        &-misc {
          &-separator {
            background: var(--kui-maroon);
          }
        }
      }
    }
  }

  &.style-SideBigTagTitleBigAvatarAuthorImgRed,
  &.style-SideBigTagTitleBigAvatarAuthorImgQuoteRed {
    .article-card {
      &-figure {
        display: flex;
        @include media-breakpoint-down(sm) {
          flex-direction: column;
        }

        &.sidebar {
          flex-direction: column;

          .article-card-author-side {
            width: 100%;
            padding: 64px 0;
            gap: 4px;
          }

          .article-card-caption {
            width: 100%;
            padding: 20px;
          }
        }
      }

      &-author-rank {
        font-size: 16px;
        font-weight: 400;
        color: var(--kui-claret-200);
      }

      &-author-side {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background-color: var(--kui-maroon);
        width: 50%;
        gap: 16px;

        aspect-ratio: 1/1;

        @include media-breakpoint-down(sm) {
          width: 100%;
          padding: 64px 0;
          gap: 4px;
        }

        &-avatar {
          width: 280px;
          height: 280px;
          border-radius: 50%;
          margin-bottom: 32px;
          object-fit: cover;

          @include media-breakpoint-down(sm) {
            width: 120px;
            height: 120px;
            margin-bottom: 20px;
          }
        }

        &-author {
          font-size: 32px;
          text-transform: uppercase;
          font-weight: 700;
          text-decoration-line: underline;
          color: var(--kui-white);

          @include media-breakpoint-down(sm) {
            font-size: 16px;
          }
        }
      }

      &-caption {
        display: flex;
        width: 50%;
        position: relative;
        justify-content: center;
        align-items: center;

        @include media-breakpoint-down(sm) {
          width: 100%;
          padding: 20px;
        }

        &-overlay {
          background-color: rgba(0, 0, 0, 0.4);
          display: block;
          position: absolute;
          z-index: 1;
          height: 100%;
          width: 100%;
        }

        &-image {
          position: absolute;
          object-fit: cover;
          height: 100%;
          width: 100%;
          z-index: 0;
        }

        &-adult {
          position: absolute;
          top: 20px;
          left: 20px;
          width: 72px;
          height: 72px;
          padding: 18px;
          background-color: var(--kui-black);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 2;

          @include media-breakpoint-down(sm) {
            width: 55px;
            height: 55px;
            padding: 11px;
          }

          i {
            width: 100%;
            height: 100%;
          }
        }
      }

      &-caption-misc-tag {
        color: var(--kui-white);
        text-transform: uppercase;
        font-size: 16px;
        font-weight: 700;
        line-height: 24px;
        display: block;

        @include media-breakpoint-down(sm) {
          font-size: 12px;
          line-height: 18px;
        }
      }

      &-link {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        z-index: 2;
        color: var(--kui-white);
        padding: 0 80px;

        &-info {
          &-title {
            margin: 32px 0;
            text-align: center;
            font-size: 32px;
            font-weight: 400;
            font-family: var(--kui-font-secondary);
            line-height: 48px;

            @include media-breakpoint-down(sm) {
              font-size: 20px;
              margin: 12px 0;
              font-weight: 400;
              line-height: 32px;
            }
          }
        }

        &-lead {
          font-size: 20px;
          font-weight: 400;
          line-height: 30px;

          @include media-breakpoint-down(sm) {
            display: none;
          }
        }
      }
    }
  }

  &.style-SideBigTagTitleBigAvatarAuthorImgQuoteRed {
    .article-card {
      &-figure {
        flex-direction: row-reverse;

        &.sidebar {
          flex-direction: column;

          .article-card-quote-side,
          .article-card-caption {
            width: 100%;
          }
        }
      }

      &-link {
        aspect-ratio: 1 / 1;
      }

      @include media-breakpoint-down(sm) {
        &-figure {
          flex-direction: column-reverse;
        }

        &-link-info-title {
          margin-bottom: 0;
        }
      }

      &-quote-side {
        width: 50%;
        display: flex;
        flex-direction: column;
        background-color: var(--kui-maroon);
        justify-content: space-between;
        align-items: center;
        padding: 32px 0;
        aspect-ratio: 1/1;

        @include media-breakpoint-down(sm) {
          width: 100%;
        }

        &-top-icon {
          margin-right: auto;
          padding-left: 32px;
        }

        &-bottom-icon {
          margin-left: auto;
          padding-right: 32px;
        }

        &-excerpt {
          padding: 0 80px;
          text-align: center;
          color: var(--kui-white);
          font-size: 24px;
          font-style: italic;
          font-weight: 400;
          line-height: 48px;

          @include media-breakpoint-down(sm) {
            font-size: 16px;
            line-height: 24px;
          }
        }
      }

      &-caption {
        @include media-breakpoint-down(sm) {
          padding: 0;
        }
      }

      &-author-side {
        display: flex;
        align-items: center;
        flex-direction: row;
        justify-content: center;
        background-color: unset;
        width: auto;
        aspect-ratio: unset;

        @include media-breakpoint-down(sm) {
          padding-top: 20px;
          padding-bottom: 0;
        }

        &-avatar {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          object-fit: cover;
          margin: auto 8px;
        }

        &-author {
          padding-bottom: 0;
          text-decoration-line: underline;
          border: unset;
          font-size: 16px;
          font-weight: 700;
          line-height: 24px;
          text-transform: uppercase;
          color: var(--kui-white);

          @include media-breakpoint-down(sm) {
            font-size: 12px;
          }
        }
      }
    }

    .she-icon-quote-9-reverse-white,
    .she-icon-quote-6-reverse-white {
      width: 60px;
      height: 49px;
    }
  }

  &.style-VerticalImgRectangleTagTitleNoAvatar {
    .article-card {
      &-title {
        padding-bottom: 4px;
        font-size: 28px;
        line-height: 36px;
        color: var(--kui-white);

        @include media-breakpoint-down(sm) {
          font-size: 24px;
        }
      }

      &-image {
        width: 100%;
      }

      &-caption {
        &-misc {
          flex-wrap: wrap;

          &-author,
          &-tag {
            color: var(--kui-white);
            font-weight: 700;

            @include media-breakpoint-down(sm) {
              font-size: 12px;
              line-height: 18px;
            }
          }

          &-separator {
            background: var(--kui-white);
            margin: 0 8px;
          }
        }
      }
    }
  }

  &.style-VerticalImgRectangleTitleNoAvatar {
    .article-card {
      &-image {
        width: 100%;
      }
    }
  }

  &.style-SmallSidedImgTagTitle,
  &.style-SmallSidedImgAuthorTitle,
  &.style-SmallSidedImgAuthorTagDateTitle {
    .article-card {
      &-title {
        &.in-sidebar {
          font-size: 20px;
          hyphens: auto;

          @include media-breakpoint-down(xs) {
            font-size: 16px;
          }
        }
      }
    }
  }

  &.style-RelatedArticle {
    padding: 12px 0;
    border-bottom: 1px solid var(--kui-thunder-200);
    width: 100%;
    margin: 0;

    .article-card {
      &-title {
        font-family: var(--kui-font-primary);
        color: var(--kui-black);
        font-style: normal;
        line-height: 24px; /* 150% */
        font-weight: 700;
        font-size: 16px;

        @include media-breakpoint-up(sm) {
          font-size: 20px;
          line-height: 32px;
        }
      }

      &-link {
        display: flex;
        align-items: center;

        i.icon {
          vertical-align: text-top;
          margin-left: 8px;
          width: 18px;
          height: 18px;

          @include media-breakpoint-up(sm) {
            width: 24px;
            height: 24px;
          }
        }
      }
    }
  }
}
