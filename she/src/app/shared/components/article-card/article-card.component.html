<article *ngIf="articleCard">
  <ng-container [ngSwitch]="styleID">
    <ng-container
      *ngSwitchCase="ArticleCardType.VerticalImgTallTitleNoAvatar"
      [ngTemplateOutletContext]="{ displayedAspectRatio: { desktop: '1:1' } }"
      [ngTemplateOutlet]="basicCard"
    ></ng-container>
    <ng-container
      *ngSwitchCase="ArticleCardType.VerticalImgRectangleTitleNoAvatar"
      [ngTemplateOutletContext]="{ noTag: true, displayedAspectRatio: { desktop: '1:1' } }"
      [ngTemplateOutlet]="basicCard"
    ></ng-container>
    <ng-container
      *ngSwitchCase="ArticleCardType.VerticalImgRectangleTagTitleNoAvatar"
      [ngTemplateOutletContext]="{ displayedAspectRatio: { desktop: '1:1' } }"
      [ngTemplateOutlet]="basicCard"
    ></ng-container>
    <ng-container
      *ngSwitchCase="ArticleCardType.VerticalWideImgTitleAvatar"
      [ngTemplateOutletContext]="{ noTag: true, hasAvatar: true, displayedAspectRatio: { desktop: '16:9', mobile: '1:1' } }"
      [ngTemplateOutlet]="basicCard"
    ></ng-container>
    <ng-container
      *ngSwitchCase="ArticleCardType.SmallSidedImgAuthorTitle"
      [ngTemplateOutletContext]="{ noTag: true, isSmallImg: true, displayedAspectRatio: { desktop: '1:1' } }"
      [ngTemplateOutlet]="basicCard"
    ></ng-container>
    <ng-container
      *ngSwitchCase="ArticleCardType.SmallSidedImgTagTitle"
      [ngTemplateOutletContext]="{ noAuthor: true, isSmallImg: true, displayedAspectRatio: { desktop: '1:1' } }"
      [ngTemplateOutlet]="basicCard"
    ></ng-container>
    <ng-container
      *ngSwitchCase="ArticleCardType.SmallSidedImgAuthorTagDateTitle"
      [ngTemplateOutletContext]="{ hasDate: true, isSmallImg: true, displayedAspectRatio: { desktop: '1:1' } }"
      [ngTemplateOutlet]="basicCard"
    ></ng-container>
    <ng-container
      *ngSwitchCase="ArticleCardType.VerticalBigImgTagTitleAuthorAvatar"
      [ngTemplateOutletContext]="{ hasAvatar: true, isVertical: true, displayedAspectRatio: { desktop: '16:9', mobile: '1:1' } }"
      [ngTemplateOutlet]="basicCard"
    ></ng-container>
    <ng-container
      *ngSwitchCase="ArticleCardType.SideBigTagTitleAuthorImgRed"
      [ngTemplateOutletContext]="{ hasAvatar: true, isVertical: true, displayedAspectRatio: { desktop: '1:1' } }"
      [ngTemplateOutlet]="basicCard"
    ></ng-container>
    <ng-container
      *ngSwitchCase="ArticleCardType.SideBigTagTitleAuthorImgGray"
      [ngTemplateOutletContext]="{ hasAvatar: true, isVertical: true, displayedAspectRatio: { desktop: '1:1' } }"
      [ngTemplateOutlet]="basicCard"
    ></ng-container>
    <ng-container
      *ngSwitchCase="ArticleCardType.SideBigTagTitleBigAvatarAuthorImgRed"
      [ngTemplateOutletContext]="{ showAuthorRank: true, displayedAspectRatio: { desktop: '1:1', mobile: 'unset' } }"
      [ngTemplateOutlet]="bigSideCard"
    ></ng-container>
    <ng-container
      *ngSwitchCase="ArticleCardType.SideBigTagTitleBigAvatarAuthorImgQuoteRed"
      [ngTemplateOutletContext]="{ isQuote: true, displayedAspectRatio: {} }"
      [ngTemplateOutlet]="bigSideCard"
    ></ng-container>
    <ng-container *ngSwitchCase="ArticleCardType.RelatedArticle" [ngTemplateOutlet]="relatedArticle"></ng-container>

    <ng-template #bigSideCard let-displayedAspectRatio="displayedAspectRatio" let-isQuote="isQuote" let-showAuthorRank="showAuthorRank">
      <figure [ngClass]="{ sidebar: isInSidebar }" class="article-card-figure">
        <div *ngIf="isQuote; else authorSide" class="article-card-quote-side">
          <span class="article-card-quote-side-top-icon"><i class="icon she-icon-quote-9-reverse-white"></i></span>
          <h3 class="article-card-quote-side-excerpt">
            {{ articleCard?.lead }}
          </h3>
          <span class="article-card-quote-side-bottom-icon"><i class="icon she-icon-quote-6-reverse-white"></i></span>
        </div>
        <ng-template #authorSide>
          @if (articleCard?.author?.slug) {
            <a [routerLink]="['/', 'szerzo', articleCard?.author?.slug]" class="article-card-author-side">
              <img
                [data]="articleCard?.avatarImageFocusedImages"
                [displayedAspectRatio]="{ desktop: '1:1' }"
                [displayedUrl]="articleCard?.author?.avatarUrl"
                alt="Szerző avatar"
                class="article-card-author-side-avatar"
                loading="lazy"
                placeholderUrl="./assets/images/she-placeholder.svg"
                withFocusPoint
              />
              <p *ngIf="articleCard?.author?.name" class="article-card-author-side-author">{{ articleCard?.author?.name }}</p>
              <div *ngIf="showAuthorRank && articleCard?.author?.rank as rank" class="article-card-author-rank">{{ rank }}</div>
            </a>
          } @else {
            <span class="article-card-author-side">
              <img
                [data]="articleCard?.avatarImageFocusedImages"
                [displayedAspectRatio]="{ desktop: '1:1' }"
                [displayedUrl]="articleCard?.author?.avatarUrl"
                alt="Szerző avatar"
                class="article-card-author-side-avatar"
                loading="lazy"
                placeholderUrl="./assets/images/she-placeholder.svg"
                withFocusPoint
              />
              <p *ngIf="articleCard?.author?.name" class="article-card-author-side-author">{{ articleCard?.author?.name }}</p>
              <div *ngIf="showAuthorRank && articleCard?.author?.rank as rank" class="article-card-author-rank">{{ rank }}</div>
            </span>
          }
        </ng-template>

        <figcaption class="article-card-caption">
          <div class="article-card-caption-overlay"></div>
          <img
            [alt]="displayedThumbnailAlt || ''"
            [data]="articleCard?.thumbnailFocusedImages"
            [displayedAspectRatio]="displayedAspectRatio"
            [displayedUrl]="displayedThumbnailUrl"
            class="article-card-caption-image"
            loading="lazy"
            withFocusPoint
          />
          <ng-container *ngIf="articleCard?.isAdultsOnly">
            <div class="article-card-caption-adult">
              <i class="icon she-icon-adult"></i>
            </div>
          </ng-container>
          <a [routerLink]="articleLink" class="article-card-link">
            <div class="article-card-link-info">
              <ng-container *ngIf="isTagVisible">
                <ng-container *ngTemplateOutlet="displayTag"></ng-container>
              </ng-container>
            </div>
            <h2 class="article-card-link-info-title">{{ articleCard?.title }}</h2>

            @if (articleCard?.author?.slug) {
              <a *ngIf="isQuote; else lead" [routerLink]="['/', 'szerzo', articleCard?.author?.slug]" class="article-card-author-side">
                <img
                  [data]="articleCard?.avatarImageFocusedImages"
                  [displayedAspectRatio]="{ desktop: '1:1' }"
                  [displayedUrl]="articleCard?.author?.avatarUrl"
                  alt="Szerző avatar"
                  class="article-card-author-side-avatar"
                  loading="lazy"
                  placeholderUrl="./assets/images/she-placeholder.svg"
                  withFocusPoint
                />
                <p *ngIf="articleCard?.author?.name" class="article-card-author-side-author">{{ articleCard?.author?.name }}</p>
              </a>
            } @else {
              <span *ngIf="isQuote; else lead" class="article-card-author-side">
                <img
                  [data]="articleCard?.avatarImageFocusedImages"
                  [displayedAspectRatio]="{ desktop: '1:1' }"
                  [displayedUrl]="articleCard?.author?.avatarUrl"
                  alt="Szerző avatar"
                  class="article-card-author-side-avatar"
                  loading="lazy"
                  placeholderUrl="./assets/images/she-placeholder.svg"
                  withFocusPoint
                />
                <p *ngIf="articleCard?.author?.name" class="article-card-author-side-author">{{ articleCard?.author?.name }}</p>
              </span>
            }

            <ng-template #lead>
              <div class="article-card-link-lead">{{ articleCard?.lead }}</div>
            </ng-template>
          </a>
        </figcaption>
      </figure>
    </ng-template>

    <ng-template
      #basicCard
      let-displayedAspectRatio="displayedAspectRatio"
      let-hasAvatar="hasAvatar"
      let-hasDate="hasDate"
      let-isSmallImg="isSmallImg"
      let-isVertical="isVertical"
      let-noAuthor="noAuthor"
      let-noTag="noTag"
    >
      <figure [ngClass]="{ sidebar: isInSidebar }" class="article-card-figure">
        <a [routerLink]="articleLink" class="article-card-link article-card-image-link">
          <img
            [alt]="displayedThumbnailAlt || ''"
            [data]="articleCard?.thumbnailFocusedImages"
            [displayedAspectRatio]="displayedAspectRatio"
            [displayedUrl]="displayedThumbnailUrl"
            class="article-card-image"
            loading="lazy"
            withFocusPoint
          />
          <ng-container *ngIf="articleCard?.isAdultsOnly">
            <div [class.small-img]="isSmallImg" class="article-card-image-link-adult">
              <i class="icon she-icon-adult"></i>
            </div>
          </ng-container>
          <span *ngIf="articleCard?.sponsorTitle" class="article-card-image-link-sponsor">szponzorált tartalom</span>
        </a>

        <figcaption class="article-card-caption">
          <div class="article-card-caption-misc">
            @if (articleCard?.author?.slug) {
              <a *ngIf="hasAvatar && !isVertical" [routerLink]="['/', 'szerzo', articleCard?.author?.slug]">
                <img
                  [data]="articleCard?.avatarImageFocusedImages"
                  [displayedAspectRatio]="{ desktop: '1:1' }"
                  [displayedUrl]="articleCard?.author?.avatarUrl"
                  alt="Szerző avatar"
                  class="article-card-caption-misc-avatar"
                  loading="lazy"
                  placeholderUrl="./assets/images/she-placeholder.svg"
                  withFocusPoint
                />
              </a>
              <a
                *ngIf="articleCard?.author?.name && !noAuthor && !isVertical"
                [routerLink]="['/szerzo', articleCard?.author?.slug]"
                class="article-card-caption-misc-author link"
              >
                {{ articleCard?.author?.name }}
              </a>
            } @else {
              <img
                *ngIf="hasAvatar && !isVertical"
                [data]="articleCard?.avatarImageFocusedImages"
                [displayedAspectRatio]="{ desktop: '1:1' }"
                [displayedUrl]="articleCard?.author?.avatarUrl"
                alt="Szerző avatar"
                class="article-card-caption-misc-avatar"
                loading="lazy"
                placeholderUrl="./assets/images/she-placeholder.svg"
                withFocusPoint
              />
              <span *ngIf="articleCard?.author?.name && !noAuthor && !isVertical" class="article-card-caption-misc-author">
                {{ articleCard?.author?.name }}
              </span>
            }
            <ng-container *ngIf="isTagVisible && !noTag">
              <ng-container *ngTemplateOutlet="displayTag"></ng-container>
            </ng-container>
          </div>
          <a [routerLink]="articleLink" class="article-card-link">
            <ng-container *ngIf="isInSidebar; else notInSidebar">
              <h2 [class.in-sidebar]="isInSidebar" [ngClass]="{ 'with-tag': isTagVisible && !noTag }" class="article-card-title">
                {{ articleCard?.title | sheArticleTextTrim: 50 }}
              </h2>
            </ng-container>
            <ng-template #notInSidebar>
              <h2 [ngClass]="{ 'with-tag': isTagVisible && !noTag }" class="article-card-title">{{ articleCard?.title }}</h2>
            </ng-template>
          </a>

          @if (articleCard?.author?.slug) {
            <a *ngIf="isVertical" [routerLink]="['/', 'szerzo', articleCard?.author?.slug]" class="article-card-caption-misc-vertical">
              <span *ngIf="hasAvatar">
                <img
                  [data]="articleCard?.avatarImageFocusedImages"
                  [displayedAspectRatio]="{ desktop: '1:1' }"
                  [displayedUrl]="articleCard?.author?.avatarUrl"
                  alt="Szerző avatar"
                  class="article-card-caption-misc-avatar"
                  loading="lazy"
                  placeholderUrl="./assets/images/she-placeholder.svg"
                  withFocusPoint
                />
              </span>
              <p *ngIf="articleCard?.author?.name && !noAuthor" class="article-card-caption-misc-author">{{ articleCard?.author?.name }}</p>
            </a>
          } @else {
            <span *ngIf="isVertical" class="article-card-caption-misc-vertical">
              <span *ngIf="hasAvatar">
                <img
                  [data]="articleCard?.avatarImageFocusedImages"
                  [displayedAspectRatio]="{ desktop: '1:1' }"
                  [displayedUrl]="articleCard?.author?.avatarUrl"
                  alt="Szerző avatar"
                  class="article-card-caption-misc-avatar"
                  loading="lazy"
                  placeholderUrl="./assets/images/she-placeholder.svg"
                  withFocusPoint
                />
              </span>
              <p *ngIf="articleCard?.author?.name && !noAuthor" class="article-card-caption-misc-author">{{ articleCard?.author?.name }}</p>
            </span>
          }

          <p *ngIf="hasDate && articleCard?.publishDate" class="article-card-date">
            {{ articleCardPublishDate | dfnsFormat: 'yyyy.MM.dd.' }}
          </p>
        </figcaption>
      </figure>
    </ng-template>

    <ng-container *ngSwitchCase="ArticleCardType.ExternalRecommendation">
      <figure class="article-card-figure">
        <a [href]="articleCard?.url" class="article-card-link article-card-image-link" referrerpolicy="no-referrer" target="_blank">
          <img [alt]="articleCard?.thumbnail?.alt ? articleCard?.title : ''" [src]="displayedThumbnailUrl" class="article-card-image" loading="lazy" />
        </a>

        <figcaption class="article-card-caption">
          <a [href]="articleCard?.url" class="article-card-caption-link" referrerpolicy="no-referrer" target="_blank">
            <span class="article-card-caption-link-source">{{ articleCard?.category?.name }}</span>
            <h3 class="article-card-caption-link-title">{{ articleCard?.title }}</h3>
          </a>
        </figcaption>
      </figure>
    </ng-container>
  </ng-container>
</article>

<ng-template #displayTag>
  <ng-container *ngIf="displayedTagMetaType === 'tag'">
    <span class="article-card-caption-misc-separator"></span>
    <a *ngIf="articleCard?.tags?.[displayedTagIndex]?.title; let title" [routerLink]="tagLink" class="article-card-caption-misc-tag">{{ title }}</a>
  </ng-container>
</ng-template>

<ng-template #relatedArticle>
  <a [routerLink]="articleLink" class="article-card-link">
    <h2 class="article-card-title">{{ articleCard?.title }}</h2>
    <i *ngIf="articleCard?.isAdultsOnly" class="icon she-icon-adult"></i>
  </a>
</ng-template>
