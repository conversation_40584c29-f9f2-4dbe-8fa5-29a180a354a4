import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, Inject, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { FormatDatePipe, IMetaData, PublishDatePipe, SchemaOrgService, SeoService, StorageService, UtilService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  ALL_BANNER_LIST,
  AnalyticsService,
  Article,
  ArticleAdvertisements,
  ArticleBody,
  ArticleBodyDetails,
  ArticleBodyType,
  ArticleCard,
  ArticleFileLinkDirective,
  ArticleResolverData,
  ArticleRouteParams,
  AutoArticleBodyAdService,
  buildArticleUrl,
  DetailReferenceArticle,
  GalleryData,
  GalleryElementData,
  getStructuredDataForArticle,
  NativtereloComponent,
  PAGE_TYPES,
  previewBackendArticleToArticleCard,
  SecondaryFilterAdvertType,
  TenArticleRecommenderData,
  VoteDataWithAnswer,
  VoteService,
} from '@trendency/kesma-ui';
import { combineLatest, forkJoin, Observable, of, Subject } from 'rxjs';
import { map, switchMap, takeUntil } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import { ArticlePageResolverData } from '../api/article-page.resolver';
import { AsyncPipe, DOCUMENT, NgForOf, NgIf, NgSwitch, NgSwitchCase, NgTemplateOutlet } from '@angular/common';
import {
  ArticleCardComponent,
  ArticleCardType,
  ArticleRecommenderComponent,
  AttentionBoxType,
  defaultMetaInfo,
  GalleryService,
  PersonalizedRecommendationService,
  SheAdultComponent,
  SheAttentionBoxComponent,
  SheQuizComponent,
  SheSliderGalleryComponent,
  SheVotingComponent,
  SheWysiwygBoxComponent,
  SponsorshipComponent,
  TagListComponent,
  TopStoriesComponent,
  TopStoriesType,
} from '../../../shared';
import { ArticleHeaderComponent } from './article-header/article-header.component';
import { ArticleSocialShareComponent } from './article-social-share/article-social-share.component';
import { SidebarComponent } from '../../layout/components/sidebar/sidebar.component';

@Component({
  selector: 'app-article-page',
  templateUrl: 'article-page.component.html',
  styleUrls: ['article-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [AutoArticleBodyAdService, FormatDatePipe],
  imports: [
    ArticleHeaderComponent,
    SponsorshipComponent,
    TagListComponent,
    ArticleSocialShareComponent,
    NativtereloComponent,
    SidebarComponent,
    SheAdultComponent,
    SheAttentionBoxComponent,
    ArticleCardComponent,
    TopStoriesComponent,
    SheQuizComponent,
    SheWysiwygBoxComponent,
    SheVotingComponent,
    SheSliderGalleryComponent,
    ArticleRecommenderComponent,
    NgIf,
    AdvertisementAdoceanComponent,
    PublishDatePipe,
    NgTemplateOutlet,
    NgForOf,
    NgSwitch,
    NgSwitchCase,
    ArticleFileLinkDirective,
    AsyncPipe,
  ],
})
export class ArticlePageComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('dataTrigger', { static: false }) readonly dataTrigger: ElementRef<HTMLDivElement>;
  @ViewChild('externalRecommendationsBlock', { static: false }) readonly externalRecommendationsBlock: ElementRef<HTMLDivElement>;

  article?: Article;
  articleSlug?: string;
  articleUrl: string[] = [];
  mostReadArticles: ArticleCard[];
  isUserAdultChoice?: boolean;
  adverts?: ArticleAdvertisements;
  interrupter?: ArticleAdvertisements;
  mobile_pr_cikkfix?: Advertisement[];
  embedPrAdvert?: SafeHtml;

  externalRecommendations?: ArticleCard[];
  metaData?: IMetaData;
  adPageType = PAGE_TYPES.all_articles_and_sub_pages;
  galleries: Record<string, GalleryData> = {};
  readonly TopStoriesType = TopStoriesType;
  readonly ArticleBodyType = ArticleBodyType;
  readonly AttentionBoxType = AttentionBoxType;
  readonly ArticleCardType = ArticleCardType;
  replaceableAds = SecondaryFilterAdvertType.REPLACEABLE;
  tereloUrl: string = 'https://terelo.mediaworks.hu/nativterelo/nativterelo.html?utmSource=She.hu' + '&traffickingPlatforms=She%20Nat%C3%ADv' + '&domain=SHE';
  googleNewUrl: string = 'https://news.google.com/publications/CAAqJQgKIh9DQklTRVFnTWFnMEtDM05vWlM1c2FXWmxMbWgxS0FBUAE?hl=hu&gl=HU&ceid=HU%3Ahu';
  readonly #unsubscribe$: Subject<boolean> = new Subject();
  #cannonicalUrl?: string;

  readonly voteCache = this.voteService.voteCache;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly seo: SeoService,
    private readonly cdr: ChangeDetectorRef,
    private readonly adStore: AdvertisementAdoceanStoreService,
    private readonly analyticsService: AnalyticsService,
    private readonly formatDate: FormatDatePipe,
    private readonly storage: StorageService,
    private readonly schemaService: SchemaOrgService,
    private readonly voteService: VoteService,
    private readonly galleryService: GalleryService,
    private readonly utilsService: UtilService,
    private readonly autoArticleBodyAd: AutoArticleBodyAdService,
    private readonly personalizedRecommendationService: PersonalizedRecommendationService,
    private readonly sanitizer: DomSanitizer,
    @Inject(DOCUMENT) private readonly document: Document
  ) {}

  get galleriesData(): Record<string, GalleryData> {
    return this.galleries as unknown as Record<string, GalleryData>;
  }

  get isArticlePreview(): boolean {
    return !!this.route.parent?.snapshot.data['isPreview'];
  }

  ngOnInit(): void {
    (this.route.data as Observable<{ data: ArticlePageResolverData }>).pipe(takeUntil(this.#unsubscribe$)).subscribe(
      ({
        data: {
          article: { data: article },
          articleSlug,
          recommendations,
          mostReadArticles,
          url,
        },
      }) => {
        let body = article?.body;

        this.autoArticleBodyAd.init(article?.body);
        body = this.autoArticleBodyAd.autoAd();

        this.article = {
          ...article,
          slug: articleSlug,
          body: this.#prepareArticleBody(body),
          excerpt: article?.lead || article?.excerpt,
        };
        this.mostReadArticles = mostReadArticles || [];

        this.embedPrAdvert = this.sanitizer.bypassSecurityTrustHtml(this.article?.embedPrAdvert ?? '');
        this.externalRecommendations = recommendations?.data?.externalRecommendation;
        this.getPersonalizedRecommendations();
        this.articleSlug = articleSlug;
        this.articleUrl = buildArticleUrl(this.article);
        this.isUserAdultChoice = (this.storage.getSessionStorageData('isAdultChoice', false) ?? false) && this.article?.isAdultsOnly;
        this.#cannonicalUrl = this.article.seo?.seoCanonicalUrl || this.article?.canonicalUrl || `${this.seo.hostUrl}/${url || ''}`;
        this.adStore.setIsAdultPage(this.isUserAdultChoice);
        this.seo.updateCanonicalUrl(this.#cannonicalUrl ?? '', {
          skipSeoMetaCheck: true,
          addHostUrl: false,
        });
        this.loadEmbeddedGalleries();
        this.voteService.initArticleVotes(this.article);
        this.#setMetaData();
        if (this.article) {
          const hasAuthorSlug: Record<string, boolean> = {
            hasAuthorPageSlug: !!this.article?.publicAuthorSlug,
          };
          this.schemaService.removeStructuredData();
          this.schemaService.insertSchema(getStructuredDataForArticle(this.article, this.seo.currentUrl, environment?.siteUrl ?? '', hasAuthorSlug));
        }
        this.cdr.markForCheck();

        const categorySlug = this.article?.primaryColumn?.slug;

        setTimeout(() => {
          this.analyticsService.sendPageView(
            {
              pageCategory: categorySlug,
              customDim2: this.article?.topicLevel1,
              customDim1: this.article?.aniCode,
              title: this.article?.title,
              articleSource: this.article?.articleSource ? this.article.articleSource : 'no source',
              publishDate: this.formatDate.transform(this.article?.publishDate as Date, 'dateTime'),
              lastUpdatedDate: this.formatDate.transform(
                (this.article?.lastUpdated ? this.article.lastUpdated : this.article?.publishDate) as Date,
                'dateTime'
              ),
            },
            'Cikk'
          );
        }, 0);
      }
    );

    (
      combineLatest([
        this.route.data as Observable<{
          data: ArticleResolverData;
        }>,
        this.adStore.isAdult.asObservable(),
      ]) as unknown as Observable<[{ data: ArticleResolverData }, boolean]>
    )
      .pipe(takeUntil(this.#unsubscribe$))
      .pipe(
        map<[{ data: ArticleResolverData }, boolean], boolean | undefined>(
          ([
            {
              data: { article },
            },
          ]) => {
            this.adStore.getAdvertisementMeta(article.data.tags, article.data.isAdultsOnly);

            return article?.data?.withoutAds;
          }
        )
      )
      .pipe(
        switchMap((withoutAds) => {
          this.resetAds();
          withoutAds ? this.adStore.disableAds() : this.adStore.enableAds();
          return withoutAds ? of([]) : this.adStore.advertisemenets$;
        })
      )
      .subscribe((adverts): void => {
        this.adverts = this.adStore.separateAdsByMedium(adverts, this.adPageType, ALL_BANNER_LIST, this.replaceableAds);

        const prAndInterrupter = this.adStore.separateAdsByMedium(adverts);

        this.interrupter = prAndInterrupter;

        this.mobile_pr_cikkfix = [
          prAndInterrupter.mobile.prcikkfix_1,
          prAndInterrupter.mobile.prcikkfix_2,
          prAndInterrupter.mobile.prcikkfix_3,
          prAndInterrupter.mobile.prcikkfix_4,
          prAndInterrupter.mobile.prcikkfix_5,
        ];
        this.adStore.onArticleLoaded();
        this.cdr.detectChanges();
      });
  }

  ngAfterViewInit(): void {
    if (this.utilsService.isBrowser()) {
      setTimeout(() => {
        this.route.fragment.pipe(takeUntil(this.#unsubscribe$)).subscribe((fragment) => {
          if (fragment) {
            this.document.getElementById(fragment)?.scrollIntoView();
          }
        });
      }, 50);
      setTimeout(() => {
        this.route.data.pipe(takeUntil(this.#unsubscribe$)).subscribe(() => {
          if ('IntersectionObserver' in window) {
            this.observeExternalRecommendations();
            this.observeArticleEnd();
          }
        });
      }, 1000);
    }
  }

  ngOnDestroy(): void {
    this.adStore.onArticleDestroy();
    this.#unsubscribe$.next(true);
    this.#unsubscribe$.complete();
  }

  resetAds(): void {
    this.adverts = undefined;
    this.mobile_pr_cikkfix = undefined;
    this.cdr.detectChanges();
  }

  onVotingSubmit($event: string, voteData: VoteDataWithAnswer): void {
    const votingSubmit$ = this.voteService.onVotingSubmit($event, voteData).subscribe({
      complete: () => {
        this.cdr.detectChanges();
        votingSubmit$.unsubscribe();
      },
    });
  }

  onIsUserAdultChoose(isUserAdult: boolean): void {
    this.isUserAdultChoice = isUserAdult;
    this.adStore.setIsAdultPage(isUserAdult);
  }

  getMultiArticleRecommenderArticles(element: TenArticleRecommenderData): ArticleCard[] {
    return (
      element.details
        ?.filter((detailItem) => this.checkDetailItem(detailItem.type) && detailItem.value)
        // We already checked the type and if value exists, so we can safely cast it:
        .map((detailItem) => previewBackendArticleToArticleCard((detailItem as DetailReferenceArticle).value))
        .slice(0, 10) ?? []
    );
  }

  checkDetailItem(type: string): boolean {
    return type === 'Detail.Reference.Article' || type === 'Detail.Reference.ArticleOptional';
  }

  getPersonalizedRecommendations(): void {
    if (this.utilsService.isBrowser()) {
      this.personalizedRecommendationService
        .getPersonalizedRecommendations()
        .pipe(takeUntil(this.#unsubscribe$))
        .subscribe((data: ArticleCard[]): void => {
          this.externalRecommendations = data;
          this.cdr.detectChanges();
        });
    }
  }

  openGalleryDedicatedRouteLayer(gallery: GalleryData, selectedImageIndex: number): void {
    if (!gallery || !this.utilsService.isBrowser()) {
      return;
    }

    const url = location.pathname;
    const galleryUrl = ['/', 'galeria', gallery.slug, ...(selectedImageIndex || selectedImageIndex === 0 ? [selectedImageIndex + 1] : [])];

    this.router.navigate(galleryUrl, { state: { referrerArticle: url } });
  }

  /**
   * When changing slides in the gallery we should send a pageView to Google Analytics and Gemius.
   * We need to explicitly send the href, title and referrers as these pageViews are just "virtual" views, because
   * they are not triggered by a real navigation in the browser.
   * @param gallery gallery that should receive the page views.
   * @param params parameters of the slide change event. For example the index of the image
   */
  handleGallerySlideChange(gallery: GalleryData, params: any): void {
    const { index } = params;
    const galleryUrl = [this.seo.hostUrl, 'galeria', gallery.slug, ...(index || index === 0 ? [index + 1] : [])].join('/');
    const pageViewParams = {
      href: galleryUrl,
      title: gallery.title,
      referrer: this.seo.currentUrl,
    } as any;
    this.analyticsService.sendPageView(pageViewParams, 'Galéria');
    pp_gemius_hit(environment.gemiusId, `page=${galleryUrl}`);
  }

  private loadEmbeddedGalleries(): void {
    const bodyElements = (this.article?.body as any) ?? [];
    const gallerySubs = ((bodyElements ?? []) as GalleryElementData[])
      .filter(({ type }) => type === ArticleBodyType.Gallery)
      .filter((bodyElem) => !!bodyElem.details[0].value)
      .map((bodyElem: GalleryElementData) => this.galleryService.getGalleryDetails(bodyElem.details[0].value.slug));

    forkJoin(gallerySubs)
      .pipe(takeUntil(this.#unsubscribe$))
      .subscribe((galleries) => {
        galleries.forEach((gallery) => {
          const galleryData: GalleryData = {
            ...gallery,
            highlightedImageUrl: gallery.highlightedImage.url,
          } as any as GalleryData;
          this.galleries[gallery.id] = galleryData;
          this.cdr.markForCheck();
        });
      });
  }

  #prepareArticleBody(body: ArticleBody[]): ArticleBody[] {
    let advertIndex = 1;
    return body.map((bodyPart: ArticleBody) => ({
      ...bodyPart,
      details: (bodyPart.details ?? []).map((detail: ArticleBodyDetails) => ({
        ...detail,
        ...this.#prepareArticleBodyDetail(detail, bodyPart.type),
      })),
      ...(bodyPart.type === ArticleBodyType.Advert && {
        adverts: {
          mobile: `mobilinterrupter_${advertIndex}`,
          desktop: `desktopinterrupter_${advertIndex++}`,
        },
      }),
    }));
  }

  #prepareArticleBodyDetail(detail: ArticleBodyDetails, type: ArticleBodyType): ArticleBodyDetails {
    let newDetail: ArticleBodyDetails;
    switch (type) {
      case ArticleBodyType.Article:
        newDetail = {
          ...detail,
          value: previewBackendArticleToArticleCard(detail.value),
        };
        break;
      default:
        newDetail = { ...detail };
    }
    return newDetail;
  }

  #setMetaData(): void {
    const { thumbnail, publicAuthor, publishDate, alternativeTitle, metaThumbnail, secondaryThumbnail } = this.article || {};
    if (!this.article) {
      return;
    }

    const title = this.article.seo?.seoTitle || this.article.title;

    const comboTitle = `${title} - ${defaultMetaInfo?.ogSiteName}`;
    const finalTitle = alternativeTitle ?? comboTitle;
    const finalOgTitle = alternativeTitle && alternativeTitle.length > 0 ? alternativeTitle : `${this.article.title} - ${defaultMetaInfo?.ogSiteName}`;
    this.metaData = {
      ...defaultMetaInfo,
      title: finalTitle,
      description: this.article.seo?.seoDescription || this.article.excerpt || this.article.lead || defaultMetaInfo.description,
      robots: this.article.seo?.seoRobotsMeta || 'index, follow, max-image-preview:large',
      keywords: '',
      ogTitle: finalOgTitle,
      ogImage: secondaryThumbnail || metaThumbnail || thumbnail,
      ogType: 'article',
      articleAuthor: publicAuthor,
      articlePublishedTime: publishDate?.toISOString(),
    };
    this.seo.setMetaData(this.metaData);
  }

  private observeArticleEnd(): void {
    if (!this.dataTrigger?.nativeElement) {
      return;
    }

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(({ isIntersecting }) => {
        if (isIntersecting) {
          this.sendEcommerceEvent();
          observer.unobserve(this.dataTrigger.nativeElement);
        }
      });
    });
    observer.observe(this.dataTrigger.nativeElement);
  }

  private sendEcommerceEvent(): void {
    const routeParams: ArticleRouteParams = this.route.snapshot.params as ArticleRouteParams;
    if (!this.article) {
      return;
    }
    this.analyticsService.sendEcommerceEvent({
      id: `T${this.article.id}`,
      title: this.article.title,
      articleSlug: routeParams.articleSlug ? routeParams.articleSlug : 'cikk-elonezet',
      category: this.article.columnTitle ?? '',
      articleSource: this.article.articleSource ? this.article.articleSource : 'no source',
      publishDate: this.formatDate.transform(this.article.publishDate as Date, 'dateTime'),
      lastUpdatedDate: this.formatDate.transform((this.article.lastUpdated ? this.article.lastUpdated : this.article.publishDate) as Date, 'dateTime'),
    });
  }

  private observeExternalRecommendations(): void {
    if (!this.externalRecommendationsBlock?.nativeElement) {
      return;
    }

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(({ isIntersecting }) => {
        if (isIntersecting && this.externalRecommendations) {
          this.personalizedRecommendationService.sendPersonalizedRecommendationAv(this.externalRecommendations).subscribe();
          observer.unobserve(this.externalRecommendationsBlock.nativeElement);
        }
      });
    });
    observer.observe(this.externalRecommendationsBlock.nativeElement);
  }
}
