<ng-container *ngIf="!isUserAdultChoice && article?.isAdultsOnly; else adultContent">
  <she-adult (isUserAdult)="onIsUserAdultChoose($event)"></she-adult>
</ng-container>

<ng-template #adultContent>
  <section>
    <div class="wrapper">
      <kesma-advertisement-adocean
        *ngIf="adverts?.mobile?.['mobilrectangle_1'] as ad"
        [ad]="ad"
        [style]="{ margin: 'var(--ad-margin)', background: 'var(--ad-bg)' }"
      >
      </kesma-advertisement-adocean>
      <app-article-header [data]="article" class="wrapper-row"></app-article-header>
    </div>

    <div class="wrapper with-aside">
      <div class="left-column">
        <app-sponsorship *ngIf="article?.sponsorship as sponsorship" [sponsorData]="sponsorship" class="content-elements"></app-sponsorship>
        <app-tag-list *ngIf="article?.tags as tags" [data]="tags" class="content-elements"></app-tag-list>

        <div class="content-elements article-meta">
          <div class="article-meta-date">{{ article?.publishDate | publishDate: 'yyyy.MM.dd.' }}</div>
          <app-article-social-share [articleUrl]="articleUrl"></app-article-social-share>
        </div>

        <div class="content-elements article-lead">{{ article?.excerpt }}</div>

        <div *ngIf="embedPrAdvert" [innerHTML]="embedPrAdvert" class="article-embed-pr-advert"></div>

        <kesma-advertisement-adocean
          *ngIf="adverts?.desktop?.['roadblock_1'] as ad"
          [ad]="ad"
          [style]="{ margin: 'var(--ad-margin)', background: 'var(--ad-bg)' }"
        >
        </kesma-advertisement-adocean>

        <kesma-advertisement-adocean
          *ngIf="adverts?.mobile?.['mobilrectangle_2'] as ad"
          [ad]="ad"
          [style]="{ margin: 'var(--ad-margin)', background: 'var(--ad-bg)' }"
        >
        </kesma-advertisement-adocean>
        <ng-container [ngTemplateOutletContext]="{ body: article?.body }" [ngTemplateOutlet]="bodyContent"></ng-container>

        <div class="google-news-wrapper">
          <figure class="google-news">
            <a [href]="googleNewUrl" target="_blank">
              <img alt="Google News" loading="lazy" src="/assets/images/google-news.svg" />
            </a>
          </figure>
          <span class="google-news-text"
            >A legfrissebb hírekért kövess minket a
            <a class="google-news-link" [href]="googleNewUrl" target="_blank"> SHE </a>
            Google News oldalán is!</span
          >
        </div>

        <div #dataTrigger *ngIf="article"></div>

        <she-attention-box [type]="AttentionBoxType.FACEBOOK_GROUP" facebookGroupLink="https://www.facebook.com/groups/shekibeszelo"></she-attention-box>

        <!-- <kesma-advertisement-adocean
          *ngIf="medium_rectangle_3_above_bottom?.mobile as ad"
          [style]="{ margin: 'var(--ad-margin)', background: 'var(--ad-bg)' }"
          [ad]="ad"
        >
        </kesma-advertisement-adocean> -->
        <app-sponsorship *ngIf="article?.sponsorship as sponsorship" [sponsorData]="sponsorship"></app-sponsorship>
        <div class="nativterelo-wrapper">
          <kesma-nativterelo [tereloUrl]="tereloUrl"></kesma-nativterelo>
        </div>

        <div #externalRecommendationsBlock *ngIf="!article?.sponsorship" class="external-recommendations">
          <ng-container *ngFor="let article of externalRecommendations; let i = index">
            <she-article-card [data]="article" [styleID]="ArticleCardType.ExternalRecommendation"></she-article-card>
            <div *ngIf="i === 5" class="full-row">
              <kesma-advertisement-adocean
                *ngIf="adverts?.mobile?.mobilrectangle_ottboxextra as ad"
                [ad]="ad"
                [style]="{ margin: 'var(--ad-margin)', background: 'var(--ad-bg)' }"
              ></kesma-advertisement-adocean>
              <kesma-advertisement-adocean
                *ngIf="adverts?.desktop?.roadblock_ottboxextra as ad"
                [ad]="ad"
                [style]="{ margin: 'var(--ad-margin)', background: 'var(--ad-bg)' }"
              ></kesma-advertisement-adocean>
            </div>
          </ng-container>
        </div>

        <kesma-advertisement-adocean
          *ngIf="adverts?.desktop?.['roadblock_2'] as ad"
          [ad]="ad"
          [style]="{ margin: 'var(--ad-margin)', background: 'var(--ad-bg)' }"
        >
        </kesma-advertisement-adocean>
      </div>
      <aside>
        <app-sidebar [adPageType]="adPageType" [articleId]="article?.id" [articleSlug]="articleSlug"></app-sidebar>
      </aside>
    </div>

    <div class="wrapper">
      <ng-container *ngIf="article?.sponsorship">
        <she-top-stories *ngIf="mostReadArticles?.length" [data]="mostReadArticles" [styleID]="TopStoriesType.Dark"></she-top-stories>
      </ng-container>

      <!-- <kesma-advertisement-adocean
        *ngIf="medium_rectangle_4_bottom?.mobile as ad"
        [style]="{ margin: 'var(--ad-margin)', background: 'var(--ad-bg)' }"
        [ad]="ad"
      >
      </kesma-advertisement-adocean> -->
    </div>

    <div *ngIf="mobile_pr_cikkfix?.length" [style]="{ display: 'flex', width: '100%', 'justify-content': 'center', 'flex-direction': 'column' }">
      <ng-container *ngFor="let pr_cik_fix of mobile_pr_cikkfix">
        <kesma-advertisement-adocean *ngIf="pr_cik_fix as ad" [ad]="ad" [style]="{ 'margin-bottom': '20px' }"> </kesma-advertisement-adocean>
      </ng-container>
    </div>
  </section>
</ng-template>

<ng-template #bodyContent let-body="body">
  <ng-container *ngFor="let element of body">
    <ng-container [ngSwitch]="element.type">
      <div *ngSwitchCase="ArticleBodyType.Wysywyg" class="content-elements">
        <ng-container *ngFor="let wysiwygDetail of element?.details">
          <she-wysiwyg-box [html]="wysiwygDetail?.value || ''" trArticleFileLink></she-wysiwyg-box>
        </ng-container>
      </div>

      <div *ngSwitchCase="ArticleBodyType.Quiz" class="content-elements">
        <she-quiz [data]="element?.details[0]?.value"></she-quiz>
      </div>

      <div *ngSwitchCase="ArticleBodyType.NewsletterSignUp" class="content-elements">
        <she-attention-box [type]="AttentionBoxType.NEWSLETTER"></she-attention-box>
      </div>

      <div *ngSwitchCase="ArticleBodyType.Voting" class="content-elements">
        @if (voteCache[element?.details?.[0]?.value?.id ?? ''] | async; as voteData) {
          <she-voting (vote)="onVotingSubmit($event, voteData)" [data]="voteData.data" [voteId]="voteData.votedId" />
        }
      </div>

      <div *ngSwitchCase="ArticleBodyType.Gallery" class="content-elements">
        <she-slider-gallery
          *ngIf="galleriesData[element?.details[0]?.value?.id] as galleryData"
          [id]="galleryData.id"
          [data]="galleryData"
          [canOpenLayer]="false"
          [isInsideAdultArticleBody]="article?.isAdultsOnly"
          (imageClicked)="openGalleryDedicatedRouteLayer(galleryData, $event)"
          (slideChanged)="handleGallerySlideChange(galleryData, $event)"
        ></she-slider-gallery>
      </div>

      <ng-container *ngSwitchCase="ArticleBodyType.TenArticleRecommender">
        <she-article-recommender *ngIf="getMultiArticleRecommenderArticles(element) as data" [data]="data"></she-article-recommender>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.Advert">
        <ng-container *ngIf="!isArticlePreview; else adPlaceholder">
          <kesma-advertisement-adocean
            *ngIf="interrupter?.mobile?.[element.adverts.mobile] as ad"
            [ad]="ad"
            [style]="{ margin: 'var(--ad-margin)', background: 'var(--ad-bg)' }"
          >
          </kesma-advertisement-adocean>
          <kesma-advertisement-adocean
            *ngIf="interrupter?.desktop?.[element.adverts.desktop] as ad"
            [ad]="ad"
            [style]="{ margin: 'var(--ad-margin)', background: 'var(--ad-bg)' }"
          >
          </kesma-advertisement-adocean>
        </ng-container>

        <ng-template #adPlaceholder>
          <div class="ad-placeholder">
            <p class="ad-placeholder-title">HIRDETÉS</p>
            <p>Az aktív cikkben itt egy hirdetés fog megjelenni.</p>
          </div>
        </ng-template>
      </ng-container>
    </ng-container>
  </ng-container>
</ng-template>
