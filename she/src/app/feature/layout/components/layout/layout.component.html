<kesma-layout
  [breakingNews]="breakingNews"
  [configuration]="configuration"
  [layoutType]="layoutType ?? LayoutPageTypes.HOME"
  [structure]="structure"
  [adPageType]="adPageType"
  [blockTitleRef]="blockTitles"
  [contentComponentsRef]="contentComponents"
  [contentComponentWrapperRef]="contentComponentsWrapper"
  [contentComponentInnerWrapperRef]="contentComponentsInnerWrapper"
  [blockTitleWrapperRef]="blockTitleWrapper"
  [editorFrameSize]="editorFrameSize"
></kesma-layout>

<ng-template #blockTitles let-layoutType="layoutType" let-layoutElement="layoutElement">
  <she-block-title [data]="layoutElement.blockTitle" [headingLevel]="layoutType !== 'Sidebar' ? 3 : 4"></she-block-title>
</ng-template>

<ng-template #contentComponents let-layoutElement="layoutElement" let-index="index" let-desktopWidth="desktopWidth">
  <ng-container *ngIf="layoutElement?.config || layoutElement?.configurable === false">
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.NewsletterBlock">
      <she-attention-box></she-attention-box>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.SOCIAL_MEDIA">
      <she-social-box></she-social-box>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Article">
      <ng-container *ngIf="layoutElement.extractorData?.[index] as data">
        <she-article-card
          *ngIf="data?.slug"
          [styleID]="layoutElement.styleId"
          [isInSidebar]="layoutType === LayoutPageTypes.SIDEBAR"
          [desktopWidth]="desktopWidth"
          [data]="data"
        >
        </she-article-card>
      </ng-container>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Vote">
      @if (layoutElement.extractorData; as extractorData) {
        @if ((voteCache[extractorData?.data?.id] | async) || extractorData; as voteData) {
          <she-voting
            *ngIf="voteData.data"
            [data]="voteData.data"
            [voteId]="voteData.votedId"
            (vote)="onVotingSubmit($event, voteData)"
            [desktopWidth]="desktopWidth"
            [isSidebar]="layoutType === LayoutPageTypes.SIDEBAR"
          />
        }
      }
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.koponyeg">
      <kesma-koponyeg [data]="KoponyegDefinitions" [type]="KoponyegType"></kesma-koponyeg>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.BrandingBoxEx">
      <app-she-branding-box-ex [desktopWidth]="desktopWidth"></app-she-branding-box-ex>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.TOP_STORIES">
      <she-top-stories *ngIf="topStories$ | async as data" [data]="data" [styleID]="layoutElement.styleId"></she-top-stories>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.HtmlEmbed">
      <kesma-html-embed *ngIf="layoutElement?.config?.htmlContent as data" [data]="data"></kesma-html-embed>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.IngatlanbazarSearch">
      <kesma-real-estate-bazaar-search-block
        [showBudapestLocations]="layoutElement.config.showBudapestLocations"
        [showCountyLocations]="layoutElement.config.showBudapestLocations"
        [showOtherLocations]="layoutElement.config.showOtherLocations"
        [showNewBuildButton]="layoutElement.config.showNewBuildButton"
        [showAdvertiseButton]="layoutElement.config.showAdvertiseButton"
        [defaultLocation]="layoutElement.config.defaultLocation"
        [defaultType]="layoutElement.config.defaultType"
        [utmSource]="layoutElement.config.utmSource"
      >
      </kesma-real-estate-bazaar-search-block>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Ad">
      <kesma-advertisement-adocean
        *ngIf="layoutElement.ad"
        [style]="{
          paddingTop: 'var(--ad-padding-top)',
          paddingBottom: 'var(--ad-padding-bottom)',
          background: !layoutElement.ad.bannerName.includes('prcikkfix_') ? 'var(--ad-bg)' : '',
        }"
        [ad]="layoutElement.ad"
        [isHidden]="layoutElement.contentType !== LayoutElementContentType.Ad && !layoutElement.ad"
      ></kesma-advertisement-adocean>
    </ng-container>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.IngatlanbazarConfigurable">
    <kesma-real-estate-bazaar-block
      (initEvent)="handleRealEstateInitEvent()"
      [showHeader]="layoutElement.showHeader"
      [itemsToShow]="layoutElement.itemsToShow"
      [data]="realEstateData"
    >
    </kesma-real-estate-bazaar-block>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.INGATLANBAZAR">
    <kesma-real-estate-bazaar-block (initEvent)="handleRealEstateInitEvent()" [showHeader]="true" [itemsToShow]="1" [data]="realEstateData">
    </kesma-real-estate-bazaar-block>
  </ng-container>
</ng-template>
