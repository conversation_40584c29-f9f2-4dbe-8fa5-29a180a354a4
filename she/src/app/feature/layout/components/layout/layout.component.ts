import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, TemplateRef, ViewChild } from '@angular/core';
import {
  AdvertisementAdoceanComponent,
  BackendArticleSearchResult,
  BlockWrapperTemplateData,
  BreakingNews,
  HtmlEmbedComponent,
  KoponyegComponent,
  KoponyegDefinitions,
  KoponyegType,
  LayoutComponent as KesmaLayoutComponent,
  LayoutContentItemWrapperTemplateData,
  LayoutContentParams,
  LayoutElementContentConfiguration,
  LayoutElementContentType,
  LayoutElementRow,
  LayoutPageType,
  mapBackendArticleDataToArticleCard,
  mapRealEstateApiDataToRealEstateData,
  RealEstateBazaarApiData,
  RealEstateBazaarBackendResponse,
  RealEstateBazaarBlockComponent,
  RealEstateBazaarData,
  RealEstateBazaarSearchBlockComponent,
  VoteDataWithAnswer,
  VoteService,
} from '@trendency/kesma-ui';
import { HttpClient } from '@angular/common/http';
import { map, share } from 'rxjs/operators';
import { Observable } from 'rxjs';
import { BrandingBoxService } from '../../services/branding-box.service';
import {
  ApiService,
  ArticleCardComponent,
  SheAttentionBoxComponent,
  SheBlockTitleComponent,
  SheBrandingBoxExComponent,
  SheLifeBrandingBoxArticle,
  SheVotingComponent,
  SocialBoxComponent,
  TopStoriesComponent,
} from '../../../../shared';
import { AsyncPipe, NgIf } from '@angular/common';

@Component({
  selector: 'app-layout',
  templateUrl: './layout.component.html',
  styleUrls: ['./layout.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    SheBrandingBoxExComponent,
    SheBlockTitleComponent,
    SheAttentionBoxComponent,
    SocialBoxComponent,
    ArticleCardComponent,
    SheVotingComponent,
    TopStoriesComponent,
    KesmaLayoutComponent,
    NgIf,
    AsyncPipe,
    HtmlEmbedComponent,
    RealEstateBazaarSearchBlockComponent,
    AdvertisementAdoceanComponent,
    RealEstateBazaarBlockComponent,
    KoponyegComponent,
  ],
})
export class LayoutComponent {
  @Input() adPageType = '';
  @Input() structure: LayoutElementRow[];
  @Input() configuration: LayoutElementContentConfiguration[];
  @Input() layoutType?: LayoutPageType;
  @Input() breakingNews: BreakingNews[] = [];
  @Input() contentComponentsWrapper: TemplateRef<LayoutContentItemWrapperTemplateData>;
  @Input() contentComponentsInnerWrapper: TemplateRef<LayoutContentItemWrapperTemplateData>;
  @Input() blockTitleWrapper: TemplateRef<BlockWrapperTemplateData>;
  @Input() editorFrameSize?: 'desktop' | 'mobile';

  @ViewChild('contentComponents', {
    read: TemplateRef,
    static: false,
  })
  contentComponents: TemplateRef<LayoutContentParams>;

  readonly LayoutPageTypes = LayoutPageType;
  readonly LayoutElementContentType = LayoutElementContentType;

  readonly KoponyegType: KoponyegType = KoponyegType.Light;
  readonly KoponyegDefinitions: KoponyegDefinitions = {
    width: 300,
    height: 100,
  };

  public realEstateData: RealEstateBazaarData[] = [];
  public topStories$ = this.apiService.getArticles(3, true).pipe(
    map(({ data }) => data.map((article: BackendArticleSearchResult) => mapBackendArticleDataToArticleCard(article))),
    share()
  );
  private realEstateDataLoading = false;

  readonly voteCache = this.voteService.voteCache;

  constructor(
    private readonly apiService: ApiService,
    private readonly changeDetector: ChangeDetectorRef,
    public readonly voteService: VoteService,
    public readonly httpClient: HttpClient,
    private readonly brandingBoxService: BrandingBoxService
  ) {}

  handleRealEstateInitEvent(): void {
    if (!this.realEstateDataLoading && this.realEstateData.length < 1) {
      this.getRealEstateData();
    }
  }

  getRealEstateData(): void {
    this.realEstateDataLoading = true;
    const realEstates: Array<RealEstateBazaarData> = [];
    // eslint-disable-next-line max-len
    this.httpClient
      .get(
        'https://www.ingatlanbazar.hu/api/property-search' +
          '?property_location=6,1000000004,1000000005,1000000006,1000000007&amp;;property_newbuildonly=on&amp;property__2=3_2'
      )
      .subscribe((data: RealEstateBazaarBackendResponse) => {
        data?.hits?.forEach((realEstate: RealEstateBazaarApiData) => {
          realEstates.push(mapRealEstateApiDataToRealEstateData(realEstate));
        });
        this.realEstateData = realEstates;
        this.realEstateDataLoading = false;
        this.changeDetector.detectChanges();
      });
  }

  onVotingSubmit($event: string, voteData: VoteDataWithAnswer): void {
    const votingSubmit$ = this.voteService.onVotingSubmit($event, voteData).subscribe({
      complete: () => {
        this.changeDetector.detectChanges();
        votingSubmit$.unsubscribe();
      },
    });
  }

  getBrandingBoxExArticles(): Observable<SheLifeBrandingBoxArticle[]> | undefined {
    return this.brandingBoxService.getLifeBrandingBoxData();
  }
}
