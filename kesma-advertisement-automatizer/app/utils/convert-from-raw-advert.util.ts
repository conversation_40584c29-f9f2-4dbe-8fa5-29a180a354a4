import {
  Advertisement,
  AdvertisementBannerName,
} from "../definitions/adverts.definitions";

export type RawAdsJson = {
  "Zona name": string;
  "Master ID"?: string;
  "Slave ID": string;
};

export enum AdMedium {
  Desktop = "desktop",
  Mobile = "mobile",
}

export enum PageType {
  AllArticlesAndSubPages = "all_articles_and_sub_pages",
  ColumnSportAllArticlesAndSubPages = "column_sport_all_articles_and_sub_pages",
  OtherPages = "other_pages",
  ArticlePages = "article_pages",
  RecipePages = "recipe_pages",
  SearchPage = "search_page",
  MainPage = "main_page",
  Grief = "grief",
  Gallery = "gallery",
  Aprohirdetes = "aprohirdetes",
  Allas = "allas",
  Allasok = "allasok",
  TeljesSite = "teljes-site",
}

export function filterAdsByPortal(
  allAdverts: RawAdsJson[],
  portal: string
): RawAdsJson[] {
  const portalLower = portal.toLowerCase();
  return allAdverts
    .filter((item) => item["Zona name"].toLowerCase().includes(portalLower))
    .map((item) => ({
      ...item,
      "Zona name": item["Zona name"].toLowerCase(),
    }));
}

/**
 * Validates if a banner name is valid for processing
 * @param name The banner name to validate
 * @returns true if the banner name is valid, false otherwise
 */
function isValidBannerName(name: string): boolean {
  // Check if the banner name has at least 2 characters
  if (!name || name.length < 2) {
    return false;
  }

  // Split the name to check its parts
  const parts = name.split("_");

  // A valid banner name should have at least 4 parts: PORTAL_MEDIUM_PAGETYPE_BANNER
  if (parts.length < 4) {
    return false;
  }

  return true;
}

export function transformAds(allAds: RawAdsJson[]): Advertisement[] {
  // Filter out ads with invalid banner names
  const validAds = allAds.filter((ad) => isValidBannerName(ad["Zona name"]));

  const desktopAds = validAds
    .filter(
      (ad) => ad["Zona name"].split("_")[1] === "desktop" && ad["Slave ID"]
    )
    .map((ad) => mapAd(ad, AdMedium.Desktop));

  const mobileAds = validAds
    .filter((ad) => ad["Zona name"].split("_")[1] === "mobil" && ad["Slave ID"])
    .map((ad) => mapAd(ad, AdMedium.Mobile));

  const result = [...desktopAds, ...mobileAds];
  return result;
}

function mapAd(ad: RawAdsJson, medium: AdMedium): Advertisement {
  const name = ad["Zona name"];
  const hasAdultContent = name.includes("18");
  const processedName = hasAdultContent ? name.replace("18_", "") : name;

  return {
    zonaId: ad["Slave ID"],
    medium,
    pageType: determinePageType(processedName),
    bannerName: determineBannerName(name),
    isAdultAd: hasAdultContent,
    masterId: ad["Master ID"] ?? "",
  };
}

function determinePageType(name: string): string {
  const isOrigoSport =
    name.toLowerCase().includes("origo_") && name.includes("_sport_");
  const categoryName = isOrigoSport
    ? `sport_${name.split("_")[3]}`
    : name.split("_")[2];

  switch (categoryName) {
    case "aloldal":
    case "aloldalak":
    case "cikkajanlo":
    case "hirek":
    case "altalanos":
      return PageType.AllArticlesAndSubPages;
    case "sport_altalanos":
    case "sport_aloldal":
    case "sport_cikkajanlo":
      return PageType.ColumnSportAllArticlesAndSubPages;
    case "egyeb":
      return PageType.OtherPages;
    case "cikkoldal":
      return PageType.ArticlePages;
    case "receptek":
      return PageType.RecipePages;
    case "kereses":
      return PageType.SearchPage;
    case "cimlap":
      return PageType.MainPage;
    case "gyasz":
      return PageType.Grief;
    case "galeria":
      return PageType.Gallery;
    case "aprohirdetes":
      return PageType.Aprohirdetes;
    case "allas":
      return PageType.Allas;
    case "allasok":
      return PageType.Allasok;
    case "teljes-site":
      return PageType.TeljesSite;
    default:
      return `column_${categoryName.toLowerCase()}`;
  }
}

function determineBannerName(name: string): AdvertisementBannerName {
  const isOrigoSport =
    name.toLowerCase().includes("origo_") && name.includes("_sport_");

  const splitParts = name.split("_");

  // For Origo Sport, we need to remove the first element
  if (isOrigoSport) {
    splitParts.shift(); // Remove the first element
  }

  // Clean the parts by replacing certain strings with empty strings
  const cleanedParts = splitParts.map((part) =>
    part
      ?.replace("altalanos", "")
      ?.replace("aloldal", "")
      ?.replace("cimlap", "")
  );

  const [, , , bannerType, bannerNumber, sponsorship, sponsorshipNum] =
    cleanedParts;

  const bannerName = constructCustomBannerName(
    bannerType,
    bannerNumber,
    sponsorship,
    sponsorshipNum
  ) as AdvertisementBannerName;

  return name.toLowerCase().includes("origo_")
    ? transformOrigoBannerName(bannerName)
    : bannerName;
}

function constructCustomBannerName(
  bannerType: string,
  bannerNumber?: string,
  sponsorship?: string,
  sponsorshipNum?: string
): string {
  const parts = [bannerType, bannerNumber, sponsorship, sponsorshipNum].filter(
    (part) => part && part.length > 0
  );

  return parts.join("_");
}

function transformOrigoBannerName(bannerName: string): AdvertisementBannerName {
  if (bannerName.includes("rovat_")) {
    const [prefix, suffix] = bannerName.split("rovat_");
    return prefix + (suffix || "");
  }

  if (bannerName.includes("cikkajanlo_csak")) {
    return "googlenativ" + bannerName.split("cikkajanlo_csak-google")[1];
  }

  if (bannerName.includes("csak-google")) {
    return "googlenativ" + bannerName.split("csak-google")[1];
  }

  return bannerName;
}
