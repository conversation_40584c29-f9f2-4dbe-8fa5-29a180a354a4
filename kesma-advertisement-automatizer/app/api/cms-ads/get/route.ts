// app/api/cms-ads/get/route.ts
import { getProtocolByEnv } from "@/app/utils/protocol-by-environment.util";
import { NextRequest } from "next/server";

export async function POST(req: NextRequest) {
  const { token, portal, url, environment } = await req.json();
  if (!token || !portal || !url) {
    return new Response(
      JSON.stringify({
        error: "Missing required parameters",
        success: false
      }),
      { status: 400 }
    );
  }

  try {
    // First request: page_limit=0&offset_limit=0&rowCount_limit=10000
    const apiUrl1 = `${getProtocolByEnv(
      environment
    )}://${url}/api/hu/hu/portal/commercials?page_limit=0&offset_limit=0&rowCount_limit=10000`;

    // Second request: page_limit=1&offset_limit=1000&rowCount_limit=10000
    const apiUrl2 = `${getProtocolByEnv(
      environment
    )}://${url}/api/hu/hu/portal/commercials?page_limit=1&offset_limit=1000&rowCount_limit=10000`;

    const requestOptions = {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "X-Auth-Token": token,
        Portal: portal,
      },
    };

    // Make both requests concurrently
    const [res1, res2] = await Promise.all([
      fetch(apiUrl1, requestOptions),
      fetch(apiUrl2, requestOptions)
    ]);

    // Check if both requests were successful
    if (!res1.ok) {
      return new Response(
        JSON.stringify({
          error: `Error fetching backend ads (first request): ${res1.status} ${res1.statusText}`,
          success: false
        }),
        { status: res1.status }
      );
    }

    if (!res2.ok) {
      return new Response(
        JSON.stringify({
          error: `Error fetching backend ads (second request): ${res2.status} ${res2.statusText}`,
          success: false
        }),
        { status: res2.status }
      );
    }

    // Parse both responses
    const [json1, json2] = await Promise.all([
      res1.json(),
      res2.json()
    ]);

    // Validate response formats
    if (!json1.data || !Array.isArray(json1.data)) {
      return new Response(
        JSON.stringify({
          error: "Invalid response format from backend (first request)",
          success: false
        }),
        { status: 500 }
      );
    }

    if (!json2.data || !Array.isArray(json2.data)) {
      return new Response(
        JSON.stringify({
          error: "Invalid response format from backend (second request)",
          success: false
        }),
        { status: 500 }
      );
    }

    // Concatenate the results from both requests
    const ads = [...json1.data, ...json2.data];

    return new Response(JSON.stringify({ success: true, ads }), {
      status: 200,
    });
  } catch (error) {
    return new Response(
      JSON.stringify({
        error: (error as Error)?.message || "Request failed",
        success: false
      }),
      {
        status: 500,
      }
    );
  }
}
