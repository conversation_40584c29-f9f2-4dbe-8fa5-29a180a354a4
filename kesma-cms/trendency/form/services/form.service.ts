import { Injectable } from '@angular/core';
import { UntypedFormGroup } from '@angular/forms';

@Injectable()
export class FormService {
  constructor() {}

  // <PERSON><PERSON><PERSON><PERSON> email formátum ellenőrzésére
  public static EMAIL_REGEXP: RegExp = new RegExp(
    [
      '^(([^<>()\\[\\]\\\\.,;:\\s@"]+(\\.[^<>()\\[\\]\\\\.,;:\\s@"]+)*)|(".+"))@',
      '((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\])|(([a-zA-Z\\-0-9]+\\.)+',
      '[a-zA-Z]{2,}))$',
    ].join('')
  );

  public static URL_REGEXP: RegExp = new RegExp(
    '^((http|https):\\/\\/)' + // protocol
      '((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|' + // domain name
      '((\\d{1,3}\\.){3}\\d{1,3}))' + // OR ip (v4) address
      '(\\:\\d+)?(\\/[-a-z\\d%_.~+=-@!:,#]*)*' + // port and path
      '(\\?[;&a-z\\d%_.~+=-]*)?' + // query string
      '(\\#[-a-z\\d_]*)?$',
    'i'
  ); // fragment locator

  // Visszaadja, hogy egy FormControl invalid-e
  isFormControlInvalid(form: UntypedFormGroup, formControlName: string, formGroupName?: string): boolean {
    if (formGroupName) {
      return (
        form.get(formGroupName)['controls'][formControlName] &&
        form.get(formGroupName)['controls'][formControlName].dirty &&
        form.get(formGroupName)['controls'][formControlName].invalid
      );
    }
    return form.controls[formControlName] && form.controls[formControlName].dirty && form.controls[formControlName].invalid;
  }

  // A FormGroup minden FormControl-ját szerkesztettnek jelöli és futtat rajtuk egy validációt
  public markFormGroupTouched(formGroup: UntypedFormGroup) {
    (<any>Object)
      .keys(formGroup.controls)
      .map((key) => formGroup.controls[key])
      .forEach((control) => {
        control.markAsTouched();
        control.markAsDirty();
        control.updateValueAndValidity();

        if (control.controls) {
          this.markFormGroupTouched(control);
        }
      });
  }
}
