{"Basic": {"TitleWysiwyg": {"TitleWysiwyg": "Cím"}, "Wysiwyg": {"Wysiwyg": "Bekezdés"}}, "workflow": {"done": "Elkészült", "edited": "Szerkesztett", "read": "<PERSON><PERSON><PERSON><PERSON>", "frackedUp": "<PERSON><PERSON><PERSON><PERSON>", "reloaded": "Visszatöltött", "readyToWrite": "<PERSON><PERSON><PERSON>", "readyToEdit": "Szerkesztés kész", "accepted": "J<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "proofreading": "Korrektúra", "published": "Publikált", "frontPage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "waitingForPublish": "Publikálásra vár"}, "CMS": {"quiz-categories": "Kv<PERSON>z ka<PERSON>gó<PERSON>", "adOceanId": "Adocean azonosító", "star_dictionary_images": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gastro_experience_category": "<PERSON><PERSON><PERSON>gó<PERSON>", "gastro_experience": "Gasz<PERSON>", "gastro_experience_occasion": "Gasztro élmény alkalmak", "orders": "<PERSON><PERSON><PERSON><PERSON>", "studyAmount_1": "Ár 1 főre", "studyAmount_2": "Ár 2 főre", "studyAmount_3": "Ár 3 főre", "studyAmount_4": "Ár 4 főre", "primaryCategory": "Főkategória", "otherCategories": "<PERSON><PERSON><PERSON><PERSON>", "answerCount": "Válaszok száma", "selection-item": "Válogatás elem", "deletedAt": "<PERSON><PERSON><PERSON><PERSON>", "deletedBy": "T<PERSON>rölt felhasználó neve", "stationDeviceExternalId": "Eszköz azonosító", "highlighted_items": "Szalagba kiemelt elemek", "highlighted-items": "Szalagba kiemelt elemek", "content_block_images": "Képes Blokk címek", "header_sport_olympics": "Olimpia menü", "header_sport_olympics_origo": "Sport Olimpia menü", "photographer": "Fotós", "header_sport_eb": "EB menü", "header_sport_eb_origo": "Sport EB menü", "submitter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "coverimage_approve": "Borítókép jóváhagyása", "coverimage_reject": "Borítók<PERSON><PERSON>", "avatar_delete": "Avatar kép törlése", "badge": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "accept": "Jóváhagyás/átemelés", "coverImageApprove": "Borítókép jóváhagyása", "coverImageReject": "Borítók<PERSON><PERSON>", "accept-success": "Sikeres jóváhagyás/átemelés!", "cover-image-approved": "Sikeres borítókép jóváhagyás!", "cover-image-rejected": "Sikeres borítókép elutasítás!", "laps": "Körök", "fullArticleIsOnlyInFourthDepthAvailable": "A teljes cikk tartalmi típus csak a 4. mélységben választható ki!", "setUndoStatus": "Visszautalva stá<PERSON>z beállítása", "contactPerson": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maintainedAt": "Utolsó karbantartás ideje", "deviceWindDetection": "<PERSON><PERSON><PERSON><PERSON>", "deviceRainDetection": "Csapadék", "deviceIndoor": "Belső", "deviceOutdoor": "<PERSON><PERSON><PERSON><PERSON>", "olympics_participant": "Olimpiai résztvevő", "status": "<PERSON><PERSON><PERSON><PERSON>", "redirection_loop_error": "Átirányítási hiba!", "from_path_is_used_as_static_page_slug": "Ehhez a hivatkozáshoz már tartozik egy statikus oldal!", "from_path_is_already_in_use": "Ehhez a hivatkozáshoz már tartozik egy rövidített hivatkozás!", "shortUrl": {"creator": "Létrehozó", "fromPath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toPath": "<PERSON><PERSON><PERSON>"}, "custom_auto_mail": "Automailek", "external_partner": "<PERSON><PERSON>", "forecast_white_chance_map": "<PERSON><PERSON><PERSON><PERSON> ü<PERSON>ély<PERSON> (<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Szilveszter)", "forecast_station": "<PERSON><PERSON><PERSON>ások", "did-you-know": "Változó tartalmú branding box", "did_you_know": "Változó tartalmú branding box", "bestPractice": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bestPractices": "Turpisságok", "bestpractice": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bestPracticeOwner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "allergen": "<PERSON><PERSON><PERSON><PERSON>", "allergens": "Allergének", "maestro": "Maestro", "maestros": "Maestrok", "isMaestroAuthor": "Mindmegette maestro", "maestroTip": "Maestro tipp", "favoriteFoods": "Kedvenc ételek", "fullName": "Teljes név", "rank": "Titulus", "introduction": "Bemutatkozó", "tip": "<PERSON><PERSON><PERSON>", "facebookProfileUrl": "Facebook profil url", "instagramProfileUrl": "Instagram profil url", "tiktokProfileUrl": "Tiktok profil url", "xChannelUrl": "Twitter csatorna url", "photo": "Profilkép", "favoriteRecipes": "Ked<PERSON><PERSON> receptek", "weeklyMenus": "<PERSON><PERSON>", "weeklyMenu": "<PERSON><PERSON>", "socialCoverImage": "Közösségi média borítókép", "weeklyMenuItems": "<PERSON><PERSON>", "id": "Azonosító", "day": "Nap", "eloetel": "Előétel", "foetel": "Főétel", "desszert": "<PERSON><PERSON><PERSON>", "vega": "Vegetá<PERSON><PERSON><PERSON>", "portal-user-badge": "Jelvén<PERSON>k", "badgeTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "badgeType": "Kiosztási mód", "month": "Hónap", "year": "<PERSON><PERSON>", "selection": "Válogatás", "selections": "Válogatások", "ingredients": "Hozzávalók", "ingredientStat": "Használati szám", "energy": "Kalória", "addedSugar": "Ho<PERSON><PERSON><PERSON><PERSON> cukor", "protein": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gold": "<PERSON><PERSON>", "silver": "<PERSON><PERSON><PERSON><PERSON>", "bronze": "Bronz", "manualGold": "<PERSON><PERSON><PERSON><PERSON>", "manualSilver": "<PERSON><PERSON><PERSON><PERSON>", "manualBronze": "<PERSON><PERSON><PERSON><PERSON>", "multi_vote": "Multi szavazás", "olympicsParticipant": "Olimpiai résztvevő ország", "olympics_medal": "Olimpiai érmek", "olympics-medal": "Olimpiai érmek", "fat": "Zsír", "calcium": "Kálcium", "sfa": "SFA", "carbohydrate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "whenBuy": "<PERSON><PERSON> vedd?", "whereHold": "<PERSON><PERSON><PERSON> tárold?", "difficulty": "Nehézség", "recipeCategory": "Recept kategória", "recipeCategories": "Recept <PERSON><PERSON><PERSON><PERSON><PERSON>", "isRequired": "Kötelező", "recipeIngredients": "{{recipeName}} hozzávalók", "quantity": "Mennyiség", "unit": "Mértékegység", "recipes": "Re<PERSON>ptek", "required": "Kötelező", "recipeDescription": "Megjegyzés", "reporter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alternatives": "Alternatívák", "coverVideoUrl": "<PERSON><PERSON><PERSON><PERSON><PERSON> videó url-je", "madeForPeople": "<PERSON><PERSON><PERSON> f<PERSON>re <PERSON>", "prepareTime": "Előkészítési idő", "cookTime": "Főzési idő", "totalTime": "<PERSON><PERSON><PERSON>", "otherTimes": "<PERSON><PERSON><PERSON><PERSON>", "cost": "Árkategória", "howToVideoUrl": "Ka<PERSON><PERSON>ol<PERSON><PERSON>ó videó url-je", "allowComments": "Hozzászólás engedélyezése", "coverImage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "coverImageTemp": "Jóváhagyásra v<PERSON><PERSON><PERSON> borítókép", "coverImagesStatus": "Borítókép jóváhagyva", "secondaryCoverImage": "Másodlagos borítókép", "secondaryCoverImageTemp": "Jóváhagyásra váró másodlagos borítókép", "sentByUser": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isHighlightInTape": "Kiemelés a szalagba", "highlightInTapeFromDate": "Kiemelés a szalagba kezdete", "highlightInTapeToDate": "Kiemelés a szalagba vége", "allergics": "Allergének", "allergic": "Allergének", "mmeWarranty": "MME Garancia", "isNotifiable": "Kér-e értesítést?", "allowedRating": "<PERSON><PERSON><PERSON><PERSON><PERSON>ol<PERSON>", "containsVideo": "Videós recept", "Cím felett": "<PERSON><PERSON><PERSON>", "Cím alatt": "<PERSON><PERSON><PERSON>", "competition-team": "Bajnoksághoz rendelt csapatok", "competition-team-player": "Csapathoz rendelt j<PERSON>", "competitions": "Bajnokságok", "competition": "Bajnokság", "manager": "Manager", "round": "<PERSON><PERSON><PERSON>", "playoffGroup": "Playoff csoport", "competitionTeam": "Bajnoksághoz rendelt csapat", "competitionAssignment": "Bajnokság - csapat - játékos összerendelés", "tabellaStatus": "<PERSON><PERSON><PERSON> tabella", "logo": "Logo", "tvStations": "TV adók", "tvStation": "TV adó", "schedule": "Mérkőzés", "schedules": "Mérkőzések", "scheduleTime": "Mérkőzés idő", "scheduleEditor": "Mérkőzés összeállítás", "visitors": "Nézőszám", "homeScore": "<PERSON><PERSON> ered<PERSON>", "awayScore": "Vend<PERSON>g er<PERSON>", "information": "Információ", "scheduleDate": "Kezdés <PERSON>", "homeLineupForm": "<PERSON><PERSON>", "awayLineupForm": "Vend<PERSON><PERSON>", "scheduleStatus": "<PERSON><PERSON><PERSON>", "phase": "Szakasz/csoport", "phases": "Szakaszok", "homeTeam": "<PERSON><PERSON> csapat", "awayTeam": "Vendég csapat", "referees": "Játékvezetők", "assistants": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "newsFeed": "Hírfolyam", "windTitle": "Szélsebesség", "iconTitle": "Id<PERSON><PERSON><PERSON><PERSON><PERSON>", "portal_user": "Olvasók", "subscription_email_template": "Email <PERSON>k", "subscription_addressee": "Export cí<PERSON>tt", "redirection": "Átirányítás", "weekly_menu_item": "<PERSON><PERSON> elem", "weekly_menu": "<PERSON><PERSON>", "missingDays": "<PERSON><PERSON><PERSON><PERSON><PERSON> nap(ok)", "weeklyMenuInterval": "<PERSON><PERSON>", "banned_word": "<PERSON><PERSON><PERSON> s<PERSON>k", "pr_tag": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "snow_depth": "<PERSON><PERSON>", "prediction_hourly": "12 órás előrejelzés", "prediction_daily": "Napi <PERSON>", "prediction_text": "Szöveges előrejelzés", "player": "<PERSON><PERSON><PERSON><PERSON>", "players": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "athletes": "Sportolók", "olympicsAthletes": "Olimpiai sportolók", "athleteName": "Sportoló neve", "isActiveOrInactive": "Aktív / Inaktív", "competitionRace": "Versenyszám", "publicName": "Megjelenítendő név", "placeOfBirth": "Születési hely", "height": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "weight": "Súly", "jersey": "Mezszám", "athlete": "Sportoló", "position": "Pozíció", "cmsFirstOrder": "Kerüljön a lista elejére", "team": "Csapat", "teams": "Csapatok", "shortName": "Rövidített név", "officialPage": "<PERSON><PERSON><PERSON><PERSON> webold<PERSON>", "instagram": "Instagram oldal", "twitter": "Twitter oldal", "facebook": "Facebook oldal", "teamLogo": "Logó", "season": "Szezon", "seasons": "Szezonok", "seasonStart": "<PERSON><PERSON><PERSON> kez<PERSON>e", "seasonEnd": "Szezon vége", "sportType": "Sportág", "formAvailable": "Fe<PERSON><PERSON><PERSON>ás rögzíté<PERSON>", "staff": "Staff", "staffs": "Staff", "profilePicture": "Profilkép", "nationality": "Állampolgá<PERSON>", "birthDate": "Születési d<PERSON>", "staffPosition": "Foglalkozás / Beosztás", "staffPositions": "Foglalkozások / Beosztások", "playerPositions": "Pozíciók / Posztok", "playerPosition": "Pozíció / Poszt", "icon": "<PERSON><PERSON>", "scheduleEvent": "<PERSON><PERSON><PERSON><PERSON>", "scheduleEvents": "Segédletek", "playerRelatedEvent": "Játékost vezérlő esemény", "sport-module": "Sport", "liveSport": "Sportág", "liveSports": "Sportágak", "creator": "Szerző", "latitude": "Szélességi fok", "longitude": "Hosszúsági fok", "capacity": "Kapacitás", "address": "Cím", "publicTitle": "Megjelenítendő név", "titleAsName": "Név", "facilities": "Stadionok", "facility": "Stadion", "gift": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subRecurringParentSubscriptionId": "Ismétlődő előfizetés azonosító", "invoice_address": "Számlázási cím", "invoice_city": "Számlázási helység / város", "invoice_name": "Számlázási név", "invoice_zip": "Számlázási irányítószám", "phone_number": "Telefonszám", "shipping_address": "Szállítási cím", "shipping_city": "Szállítási helység / város", "shipping_name": "Szállítási név", "shipping_zip": "Szállítási irányítószám", "tax_number": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recurringTokenCount": "Ismétlődő előfizetés token számláló", "observedAt": "Feltöltő által megadott dátum", "backToWaiting": "Vissza a várakozóba", "rejection": "Elutasítás", "approve": "Elfogadás", "userMuted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> n<PERSON>í<PERSON>", "userLocked": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "userDeleted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>", "temperature": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "perceptions": "Észlelések", "perception": "É<PERSON><PERSON><PERSON><PERSON>", "imageTitle": "<PERSON><PERSON><PERSON>", "city": "<PERSON><PERSON><PERSON>", "glossaryAutoLink": "Szószedet/tudástár/sportlexikon automatikus linkelése", "detections": {"perceptions": "Észlelések", "banned-words": "<PERSON><PERSON><PERSON> s<PERSON>k", "forbidden-word": "<PERSON><PERSON><PERSON> s<PERSON>"}, "map-type": {"title": "Térkép neve", "isActive": "Aktív", "european_satellite_image": "Európai műholdkép", "air_pollution": "Légszennyezettség", "expected_snow_depth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "humidity": "P<PERSON><PERSON><PERSON><PERSON><PERSON>", "pollen_map": "Pollentérkép", "radar_image": "Radarkép", "water_temperature": "Vízhőmérséklet", "white_christmas_chance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "white_new_year_eve_chance": "<PERSON><PERSON><PERSON><PERSON>", "white_santa_chance": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "maximum_wind_gust": "<PERSON><PERSON><PERSON>", "last_hour_precipitation_sum": "Utolsó ó<PERSON> csapadékösszeg", "hazard_map": "Veszélytérkép", "hungarian_satellite_image": "Magyarorsz<PERSON><PERSON> műholdkép", "wind_strength": "Szélsebesség", "temperature": "Hőmérséklet térkép", "precipitation_since_midnight": "<PERSON><PERSON><PERSON><PERSON><PERSON> csapadékösszeg"}, "map": "Térkép", "negligible": "Elhanyagolható", "weak": "<PERSON><PERSON><PERSON>", "moderate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "strong": "<PERSON><PERSON><PERSON><PERSON>", "very_strong": "<PERSON>gy<PERSON> er<PERSON>", "extremely_strong": "<PERSON><PERSON><PERSON><PERSON>", "uvRadiation": "UV Sugárzás", "sunshineHours": "Napsütéses órák száma", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mute": "Némítás", "unmute": "Némítás feloldása", "dateRequired": "Dátum megadása kötelező", "orvosmet": "Or<PERSON>met", "maps": "Térképek", "materialType": "<PERSON><PERSON><PERSON><PERSON>", "popup-notifier": "Pop-up <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "notifier": "Pop-up <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ingredient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "write": "<PERSON><PERSON><PERSON>", "export-addressee": "Export cí<PERSON>tt", "isDeletable": "Törölhető", "fileName": "Fájlnév", "createExport": "Új export létrehozása", "exports": "Exportok", "subscriptions_export": "Előfizetés export", "showData": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "campaign_price": "<PERSON><PERSON><PERSON><PERSON>", "normal_price": "<PERSON><PERSON><PERSON><PERSON>", "recurringTokenCount ": "Meg<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>", "campaignRenewNextPriceType": "Megú<PERSON><PERSON><PERSON> előfizetés ártip<PERSON>", "campaignType": "Kamp<PERSON><PERSON> t<PERSON>", "prodIsDigital": "Termé<PERSON> t<PERSON>", "prodPrice": "Term<PERSON><PERSON>", "param": "Paraméter", "subActive": "Aktív", "subCreatedAt": "Létrehozás <PERSON>", "subRecurring": "Meg<PERSON><PERSON><PERSON><PERSON>", "transactionCreated": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "userEmail": "Felhasználó e-mail címe", "userFirstName": "Keresztnév", "userLastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>v", "subscriptions_addressee": "Export címzettek", "subscription_product": "Termékek", "subscription": "Előfizetések", "marketing_campaign": "Kampányok", "periodInDay": "<PERSON><PERSON>ódus (nap)", "digital": "Típus (Digitális/Nyo<PERSON>ott)", "recurring": "Meg<PERSON><PERSON><PERSON><PERSON>", "can-recur": "Meg tud még <PERSON>?", "campaignName": "Kampány neve", "product": "Termékek", "campaign": "Kampány", "dateEnd": "Kampány vége", "dateStart": "Kampány k<PERSON>e", "couponCode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "marketingCampaign": "Kampányok", "calendar": "<PERSON><PERSON><PERSON><PERSON>", "INIT": "Előkészített", "INPAYMENT": "<PERSON><PERSON><PERSON><PERSON>", "PAID": "Fizetve", "REFUND": "Visszafizetve", "CARD_REGISTRATION_FINISHED": "Kártya regisztráció véglegesítve", "FAILED": "Sikertelen", "BANK_CARD_REGISTRATION": "<PERSON><PERSON><PERSON><PERSON>", "WAITING_FOR_IPN": "Várakozás az IPN-re", "CANCELED": "Törölve", "CALENDAR": {"create": "<PERSON><PERSON> <PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "types": {"popup": "Csak popup", "content_link": "Tartalomra mutató link", "advent": "Advent"}}, "popupImageUrl": "<PERSON><PERSON> k<PERSON>p", "publishStartDate": "Publikálás k<PERSON>det<PERSON> d<PERSON>", "publishEndDate": "Publikálás vég dátuma", "subscriptions": "Előfizetések", "updatedAt": "Utolsó módosítás", "protected_article": "<PERSON><PERSON><PERSON><PERSON> cikk", "update_activated_contributor": "Elszámolási szerző", "background": "Background", "roadblock_ottboxextra": "Ottbox roadblock", "mobilrectangle_ottboxextra": "Ottbox mobile rectangle", "mobilrectangle_1": "Mobile rectangle #1", "mobilrectangle_2": "Mobile rectangle #2", "mobilrectangle_3": "Mobile rectangle #3", "mobilrectangle_4": "Mobile rectangle #4", "mobilrectangle_5": "Mobile rectangle #5", "mobilrectangle_6": "Mobile rectangle #6", "mobilrectangle_7": "Mobile rectangle #7", "mobilrectangle_8": "Mobile rectangle #8", "mobilrectangle_9": "Mobile rectangle #9", "mobilrectangle_10": "Mobile rectangle #10", "mobilrectangle_11": "Mobile rectangle #11", "szovegszponz_1": "Szöveg szponz. #1", "szovegszponz_2": "Szöveg szponz. #2", "szponzorcsik": "Szponzorcsík", "technikai_1": "Technikai #1", "technikai_2": "Technikai #2", "technikai_3": "Technikai #3", "googlenativ_1": "Google natív #1", "googlenativ_2": "Google natív #2", "googlenativ_3": "Google natív #3", "ecomm_1": "Ecomm #1", "ecomm_2": "Ecomm #2", "ecomm_3": "Ecomm #3", "ecomm_4": "Ecomm #4", "ecomm_5": "Ecomm #5", "ecomm_6": "Ecomm #6", "hozzavalok_1": "Hozzávalók #1", "hozzavalok_2": "Hozzávalók #2", "hozzavalok_3": "Hozzávalók #3", "hozzavalok_4": "Hozzávalók #4", "hozzavalok_5": "Hozzávalók #5", "szponzoracio": "Sz<PERSON><PERSON>or<PERSON><PERSON><PERSON>", "pr_box": "PR", "box_5": "Box #5", "leaderboard_3": "Leaderboard #3", "leaderboard_4": "Leaderboard #4", "leaderboard_5": "Leaderboard #5", "leaderboard_6": "Leaderboard #6", "leaderboard_7": "Leaderboard #7", "leaderboard_8": "Leaderboard #8", "leaderboard_9": "Leaderboard #9", "leaderboard_10": "Leaderboard #10", "leaderboard_11": "Leaderboard #11", "leaderboard_12": "Leaderboard #12", "leaderboard_13": "Leaderboard #13", "leaderboard_14": "Leaderboard #14", "leaderboard_15": "Leaderboard #15", "box_6": "Box #6", "box_7": "Box #7", "box_8": "Box #8", "box_9": "Box #9", "box_10": "Box #10", "roadblock_7": "Roadblock #7", "roadblock_8": "Roadblock #8", "roadblock_9": "Roadblock #9", "roadblock_10": "Roadblock #10", "roadblock_6": "Roadblock #6", "roadblock_6_extra": "Roadblock #6 extra", "roadblock_5": "Roadblock #5", "mobiltop": "Mobiltop", "mobilbottom": "<PERSON><PERSON><PERSON><PERSON>", "nap-ajanlata_keret": "<PERSON><PERSON> a<PERSON><PERSON><PERSON> keret", "nap-ajanlata_1": "<PERSON><PERSON> #1", "nap-ajanlata_2": "<PERSON><PERSON> #2", "nap-ajanlata_3": "<PERSON><PERSON> #3", "nap-ajanlata_4": "<PERSON><PERSON> #4", "nap-ajanlata_5": "<PERSON><PERSON> #5", "nap-ajanlata_6": "<PERSON><PERSON> #6", "nap-ajanlata_7": "<PERSON><PERSON> #7", "nap-ajanlata_8": "<PERSON><PERSON> #8", "nap-ajanlat_keret": "<PERSON><PERSON> a<PERSON><PERSON><PERSON> keret", "nap-ajanlat_1": "<PERSON><PERSON> #1", "nap-ajanlat_2": "<PERSON><PERSON> #2", "nap-ajanlat_3": "<PERSON><PERSON> #3", "nap-ajanlat_4": "<PERSON><PERSON> #4", "nap-ajanlat_5": "<PERSON><PERSON> #5", "nap-ajanlat_6": "<PERSON><PERSON> #6", "nap-ajanlat_7": "<PERSON><PERSON> #7", "nap-ajanlat_8": "<PERSON><PERSON> #8", "desktopinterrupter_1": "Interrupter #1", "desktopinterrupter_2": "Interrupter #2", "desktopinterrupter_3": "Interrupter #3", "desktopinterrupter_4": "Interrupter #4", "desktopinterrupter_5": "Interrupter #5", "prcikkfix_1": "PR cikk fix #1", "prcikkfix_2": "PR cikk fix #2", "prcikkfix_3": "PR cikk fix #3", "prcikkfix_4": "PR cikk fix #4", "prcikkfix_5": "PR cikk fix #5", "mobilinterrupter_1": "Interrupter #1", "mobilinterrupter_2": "Interrupter #2", "mobilinterrupter_3": "Interrupter #3", "mobilinterrupter_4": "Interrupter #4", "mobilinterrupter_5": "Interrupter #5", "externalContributor": "Elszámolási szerző", "public-authors": "Nyilvános szerzők", "public_author": "Nyilvános szerző", "isShowInStatistics": "Megjelenik a statisztikában", "contributorFullName": "Szerző neve", "contributorNickName": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>", "nickName": "Be<PERSON>név", "userFullName": "Felhaszná<PERSON><PERSON> neve", "merge": "Összevonás", "add_others": "Továbbiak hozzáadása", "user_assignment": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "contributors": "Elszámolási szerzők", "isActual": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isPublic": "<PERSON><PERSON><PERSON>", "(in)active": "(In)aktiválás", "Article": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>", "daily_sports_program": "Napi sport műsor", "mobil_app": "Mobil alkalmazás értesítései", "recommendedTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>í<PERSON>", "Body": {"AnchorBlock": "Mini menü", "Audio": "Audio fájl", "Collection": {"ArticleCollection": "Tematikus cikk gyűjtő", "AttractionCollection": "Attrakció gyűjtő", "EventCollection": "Esemény gyűjtő", "TrippCollection": "Utazás gyűjtő"}, "ContentHero": "<PERSON><PERSON><PERSON><PERSON>", "DestinationMap": "Desztinációk térkép", "DuringYourVisit": "During your visit", "Embed": "Beágyazott tartalom", "EventBox": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Gallery": "Galéria", "Group": {"Carousel": {"Row": "Carousel"}, "CategoryBox": {"Row": "Kategória do<PERSON>z"}, "Discovery": {"Row": "Discover Now"}, "Gallery": {"ImageBox": "<PERSON><PERSON><PERSON>"}, "ItineraryTip": {"Row": "Utazások doboz"}, "SeasonHighlight": {"Row": "Évszakok"}, "ThingsToDo": {"Row": "Things To Do"}, "TripDay": {"Day": "Túra nap"}, "VideoNews": {"Row": "<PERSON><PERSON><PERSON> ta<PERSON>"}, "YouMayAlsoLikeSelect": {"Row": "You May Also Like"}}, "Hero": "<PERSON><PERSON><PERSON><PERSON>", "HighlightedNewsBox": "<PERSON><PERSON><PERSON><PERSON> hí<PERSON> blokk", "InstagramFeed": "Instagram feed", "Lead": "Bevezető", "LeadMap": "Lead + <PERSON><PERSON><PERSON><PERSON><PERSON>", "LiveStream": "Stream", "MainNewsBox": "<PERSON><PERSON><PERSON> blo<PERSON>k", "MainPageHero": "Főoldal fejléc", "MoveAround": "Move Around", "PlanYourTrip": "Plan Your Trip", "PracticalInfo": "Practical Info", "ShareBar": "Megosztás sáv", "SimpleImage": "<PERSON><PERSON><PERSON>", "TakeLookAround": "Take A Look Around", "Text": "Szöveg", "TitleText": "Cím + Szöveg", "TripInfoHeader": "<PERSON><PERSON><PERSON> fej<PERSON> infó", "Vod": "<PERSON><PERSON><PERSON>"}, "CMS": {"label": {"file": "<PERSON><PERSON><PERSON>"}}, "original": "Eredeti", "digitalCopy": "<PERSON><PERSON><PERSON><PERSON>", "normalCopy": "Másolat", "Category description": "Kategó<PERSON>", "Collection": "Gyűjtő oldalak", "Column": "<PERSON><PERSON><PERSON>", "ColumnSidebar": "<PERSON><PERSON><PERSON> k<PERSON> terü<PERSON>", "Custom url": "Egyedi URL", "CustomBuiltPage": "<PERSON><PERSON><PERSON><PERSON> oldal", "banned-word": "Cenzúrázan<PERSON><PERSON> sz<PERSON>", "comment": "Hozzászólás", "commenting": {"action-success": "A művelet végrehajtása sikeres volt.", "comment": "Hozzászólás", "commenter": "Hozzászóló", "isLeader": "<PERSON><PERSON><PERSON><PERSON>", "leader": "Vezérkomment", "regular": "<PERSON><PERSON><PERSON><PERSON>", "createdAt": "Beküldve", "likeCount": "Like", "dislikeCount": "Dislike", "banned-words": "Cenzúr<PERSON><PERSON><PERSON><PERSON>", "bannedWords": {"word": "Cenzúrázan<PERSON><PERSON> sz<PERSON>"}}, "comments": "Hozzászólások", "comment-reports": "<PERSON><PERSON><PERSON> ho<PERSON>ás<PERSON>", "COMMENT_TO_DELETE": "Hozzászólás törlése", "COMMENT_TO_SHOW": "Hozzászólás láthatóvá tétele", "COMMENT_TO_HIDE": "Hozzászólás elrejté<PERSON>", "COMMENT_TO_LEADER": "Vezérkommetnek jelölés", "COMMENT_TO_FOLLOWER": "Vezérkomment jelölés törlése", "COMMENT_TO_RESTORE": "Hozzászólás visszaállítása", "Destination": "Desztinációk", "HomePage": "<PERSON><PERSON><PERSON><PERSON>", "robots_content": "Robots Meta", "seo-meta-tool": "SEO <PERSON>", "seo_meta_tool": "SEO <PERSON>", "SEOMetaTool": {"seoCanonicalUrl": "Canonical URL", "seoDescription": "SEO leírás", "seoRobotsMeta": "Robots Meta", "seoTitle": "SEO cím", "isActive": "Aktív", "seoMetaToolTitle": "SEO Meta URL-ek", "robotsTitle": "Robots <PERSON><PERSON>", "robotsValue": "Robots <PERSON><PERSON>", "createdAt": "Létrehozva", "urls": "URL-ek", "url": "URL", "robots": "Robots Meta", "title": "SEO Meta"}, "Layouts": {"HomePage": "<PERSON><PERSON><PERSON><PERSON>", "Column": "rovat c<PERSON>", "Opinion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Sidebar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ColumnSidebar": "rovat k<PERSON><PERSON><PERSON> ter<PERSON>", "CustomBuiltPage": "egy<PERSON>i oldal", "base-layouts-list": "Alap sablonok", "column-layouts-edit": "Rovat elrendezés szerkesztése", "column-layouts-list": "<PERSON><PERSON><PERSON>", "columnsidebar-layouts-edit": "Rovat közös területek szerkesztése", "columnsidebar-layouts-list": "Rovat közö<PERSON> területek", "configure-template": "Elrendezés konfigurálása", "custombuiltpage-layouts-edit": "Egyedi oldal szerkesztése", "custombuiltpage-layouts-list": "<PERSON><PERSON><PERSON><PERSON>", "edit-template": "Sablon szerkesztése", "explicit-article-lists": "<PERSON><PERSON><PERSON><PERSON>", "explicit-article-list": {"articles": "Cikkek", "article-list": "Cikk lista", "article-search": "Keresés cikkre", "edit-article-outlet": "<PERSON><PERSON><PERSON><PERSON> cikk <PERSON> (Pozíció: #{{index}}, Lista: {{listName}})", "edit-opinion-outlet": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON>y <PERSON> (Pozíció: #{{index}}, Lista: {{listName}})", "outlet-position": "Megjelenítési pozíció", "list-title": "Lista megnevezése", "list-type": "Lista típusa", "list-type-article": "Cikk lista", "list-type-opinion": "V<PERSON>lem<PERSON><PERSON> lista", "list-add": "Lista hozzáadása", "lists": "Listák", "insert": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobile-order": "<PERSON><PERSON> sorrend", "move-article": "Cikk mozgatása", "no-article-selected": "nincs cikk kiválasztva", "opinions": "Vélemények", "opinion-list": "V<PERSON>lem<PERSON><PERSON> lista", "overwrite": "Felülírás", "position": "pozíció", "select-article": "Cikk kiválasztása", "unknown-list": "Ismeretlen lista", "messages": {"cannot_change_list_type": "Tartalommal rendelkező lista típusa nem változtatható meg. A lista típusának megváltoztatásához az 'Elrendezés módosítása' f<PERSON><PERSON><PERSON><PERSON> hely<PERSON>ze át másik listára az egyes elemeket.", "confirm_article_delete": "Biztosan töröljük a kiválasztott cikket?", "invalid_move_between_different_type_lists": "Sikertelen mozgatás: Különböző típusú listák között nem mozgathatók elemek.", "cannot_delete_list_with_items": "Cikkekkel rendelkező lista nem törölhető."}}, "homepage-layouts-edit": "Főoldali elrendezés szerkesztése", "homepage-layouts-list": "<PERSON><PERSON><PERSON><PERSON>", "layout-content-type-block-separator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "layout-content-type-write-to-us": "<PERSON><PERSON><PERSON>", "layout-content-type-author": "Szerző", "layout-content-type-star-births": "Ezen a napon született sztárok", "layout-content-type-public-authors": "Nyilvános szerzők", "layout-content-type-media-panel": "Kiemelt box", "layout-content-type-recipe": "Recept k<PERSON><PERSON>", "layout-content-type-weekly-newspaper-box": "Hetilap box", "layout-content-type-column-box": "Rovat box", "layout-content-type-ad": "<PERSON><PERSON><PERSON><PERSON>", "layout-content-type-agrokep": "Agro kép", "layout-content-type-agrokep-list": "Agro kép lista", "layout-content-type-allaskereso": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "layout-content-type-article": "Cikk", "layout-content-type-article-slider": "Lapozható cikk", "layout-content-type-astrology": "Asztrológia", "layout-content-type-astronet-cikkek": "Astronet cikkek", "layout-content-type-astronet-horoszkop": "Horoszkóp", "layout-content-type-astronet-joslas": "Jóslás", "layout-content-type-best-recommender": "Metropol Best ajánló", "layout-content-type-category-stepper": "Rovat blokk", "layout-content-type-hello-budapest": "Hello Budapest", "layout-content-type-blog": "Blog", "layout-content-type-branding-box": "Branding box", "layout-content-type-branding-box-ex": "Branding Box külső forrásból", "layout-content-type-branding-box-article": "Branding box cikk lista", "layout-content-type-breaking": "Breaking blokk", "layout-content-type-brown-box": "<PERSON>na c<PERSON> do<PERSON>", "layout-content-type-data-bank": "Adatbank", "layout-content-type-upcoming-matches": "Közelgő mérkőzések", "layout-content-type-championship-table": "Bajnokság tabella", "layout-content-type-kulturnemzet": "Kultúrnemzet", "layout-content-type-event-calendar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "layout-content-type-final-countdown": "Visszaszámláló", "layout-content-type-culture-nation": "Kúltúrnemzet", "layout-content-type-dossier": "<PERSON><PERSON><PERSON><PERSON>", "layout-content-type-dossier-list": "<PERSON><PERSON><PERSON><PERSON> lista", "layout-content-type-fresh-block": "<PERSON><PERSON>", "layout-content-type-fresh-news": "24 óra", "layout-content-type-column-block": "Rovat blokk", "layout-content-type-latest-and-most-read-articles": "Legfrissebb és a legolvasottabb cikkek", "layout-content-type-configurable-sponsored-box": "Képes szövegblokk", "layout-content-type-sub-columns": "Alrovatok", "layout-content-type-astronet-branding-box": "Astronet Branding Box", "layout-content-type-astronet-columns": "Astronet Horoszkóp rovatok", "layout-content-type-latest-news": "<PERSON><PERSON>", "layout-content-type-spotlight": "Spotlight", "layout-content-type-gallery": "Galéria", "layout-content-type-html-embed": "Beágyazott tartalom", "layout-content-type-image": "<PERSON><PERSON><PERSON> blo<PERSON>k", "layout-content-type-ingatlanbazar": "Ingatlanbazár", "layout-content-type-ingatlanbazar-configurable": "Ingatlanbazár (konfigurálható)", "layout-content-type-ingatlanbazar-search": "Ingatlanba<PERSON><PERSON><PERSON>", "layout-content-type-kompost-block": "KOMPOST blokk", "layout-content-type-koponyeg": "Köpönyeg", "layout-content-type-link-list": "Link lista", "layout-content-type-sport-radio-player": "Sport Rádió le<PERSON>zó", "layout-content-type-visitor-counter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "layout-content-type-newsletter-block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "layout-content-type-opinion-newsletter-box": "<PERSON><PERSON><PERSON>", "layout-content-type-newsletter-block-gong": "<PERSON>", "layout-content-type-newspaper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "layout-content-type-did-you-know": "Változó tartalmú branding box", "layout-content-type-note": "Jegyzet", "layout-content-type-opinion-list": "V<PERSON>lem<PERSON><PERSON> lista", "layout-content-type-opinion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "layout-content-type-opinion-block": "Vélemény blokk", "layout-content-type-podcast-block": "Podcast blokk", "layout-content-type-podcast-list": "Podcast lista", "layout-content-type-pr-block": "PR blokk", "layout-content-type-program": "Program", "layout-content-type-quiz": "Kvíz", "layout-content-type-shortNews": "<PERSON><PERSON><PERSON>", "layout-content-type-fast-news": "<PERSON><PERSON><PERSON> h<PERSON>", "layout-content-type-minute-to-minute": "Per<PERSON>r<PERSON>l percre", "layout-content-type-social-media": "Social media box", "layout-content-type-stock": "Tőzsde", "layout-content-type-szakikereso": "Szakikereső", "layout-content-type-sponsored-vote": "Szponzorált sza<PERSON>zás", "layout-content-type-tabs": "Tabos blokk", "layout-content-type-tag-block": "Címke s<PERSON>ag", "layout-content-type-trending-tags-block": "Trending címkék blokk", "layout-content-type-video": "<PERSON><PERSON><PERSON>", "layout-content-type-video-block": "<PERSON><PERSON>ó blokk", "layout-content-type-visegrad-post": "Visegrád Post", "layout-content-type-vote": "Szavazás", "layout-content-type-sponsored-quiz": "Szponzorált kvíz", "layout-content-type-waze": "Waze", "layout-content-type-weekly-menu": "<PERSON><PERSON>", "layout-content-type-wysiwyg": "Szöveges tartalom", "layout-content-type-yessfactor-block": "YESSSFACTOR blokk", "layout-content-type-ripost7-block": "Ripost7 blokk", "layout-content-type-broadcast-recommender": "<PERSON><PERSON>", "layout-content-type-song-top-list": "<PERSON>e toplista", "layout-content-type-articles-with-video-content": "Videós cikk blokk", "layout-content-type-articles-with-podcast-content": "Podcastos cikk blokk", "layout-content-type-podcast-article-list": "Podcastos cikk lista", "layout-content-type-pdf-box": "Pdf box", "layout-content-type-dossier-repeater": "Dossziék", "layout-content-type-manual-article": "<PERSON><PERSON><PERSON><PERSON> cikk", "layout-content-type-manual-opinion": "<PERSON><PERSON><PERSON><PERSON> vélem<PERSON>y", "layout-content-type-rss-box": "RSS doboz", "layout-content-type-map-recommendations": "Térképaján<PERSON>ó", "layout-content-type-multi-vote": "Multi szavazás", "layout-content-type-tenyek-box": "Tények ajánló doboz", "layout-content-type-hero": "Hero", "layout-content-type-medical-meteorology": "Orvosi meteorológia", "layout-content-type-twelve-days-forecast": "12 napos előrejelzés", "layout-content-type-detections": "Észlelések", "layout-content-type-image-map-list": "Képlist<PERSON> térképek", "layout-content-type-drawn-map-list": "Rajzolt térképek", "layout-content-type-news-feed": "Hírfolyam", "layout-content-type-lead-editors": "Vezető szerkesztők", "layout-content-type-teams": "Csapatok", "layout-content-type-eb-teams": "Foci EB 2024 - Csapatok", "layout-content-type-trip-box": "Forduló box", "layout-content-type-turpi-box": "Turpi doboz", "layout-content-type-sorozatveto": "Sorozatvető", "layout-content-type-most-viewed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "layout-content-type-top-stories": "Top stories", "layout-content-type-gp-news-box": "GP <PERSON><PERSON><PERSON>", "layout-content-type-more-articles": "További c<PERSON>ink link", "layout-content-type-sport-block": "Sport blokk", "layout-content-type-tag-select": "Címke választó", "layout-content-type-recipe-category-select": "Recept kategória választó", "layout-content-type-text-box": "Szöveg", "layout-content-type-knowledge-box": "VG tudástár", "layout-content-type-turpi-card": "<PERSON><PERSON><PERSON> k<PERSON>ya", "layout-content-type-ingredient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "layout-content-type-recipe-swiper": "<PERSON><PERSON><PERSON><PERSON><PERSON> recept", "layout-content-type-related-articles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> c<PERSON>", "layout-content-type-live-bar": "<PERSON><PERSON><PERSON>vet<PERSON>", "layout-content-type-guarantee-box": "<PERSON><PERSON><PERSON>", "layout-content-type-offer-box": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "layout-content-type-maestro-box": "Maestro doboz", "layout-content-type-article-block": "Cikk blokk", "layout-content-type-eb-article-block": "Foci EB 2024 - <PERSON><PERSON><PERSON><PERSON>", "layout-content-type-app-download": "Alkalmazás letöltés box", "layout-content-type-sponsored-article-box": "Szponzorált cikk doboz", "layout-content-type-highlighted-selection": "Kiemelt Válogatás", "layout-content-type-selection": "Válogatás", "layout-content-type-daily-menu": "<PERSON><PERSON>", "layout-content-type-telekom-vivicitta": "Telekom Vivicittá", "layout-content-type-conference": "Konferencia", "layout-content-type-daily-program": "Foci EB 2024 - Napi program", "layout-content-type-eb-news": "Foci EB 2024 - <PERSON><PERSON><PERSON>", "layout-content-type-countdown-box": "Visszaszámláló do<PERSON>z", "layout-content-type-elections-box": "Választások 2024 doboz", "layout-content-type-eb-countdown-block-title": "Foci EB 2024 - Visszaszámláló blokk cím", "layout-content-type-eb-single-elimination": "Foci EB 2024 - <PERSON><PERSON><PERSON><PERSON> k<PERSON>", "layout-content-type-olimpia-countdown-block-title": "Olimpia - Visszaszámláló blokk cím", "layout-content-type-olimpia-news": "Olimpia - Hírek", "layout-content-type-olimpia-hungarian-competitions": "Olimpia - <PERSON><PERSON><PERSON>", "layout-content-type-olimpia-hungarian-team": "Olimpia - <PERSON>gy<PERSON>", "layout-content-type-olimpia-results-block": "Olimpia - <PERSON><PERSON><PERSON><PERSON><PERSON>", "layout-content-type-olimpia-articles-with-podcast-content": "Olimpia - Podcast", "layout-content-type-olimpia-large-navigator": "Olimpia - Terelő", "layout-content-type-bayer-blog": "<PERSON><PERSON>", "layout-content-type-gallery-article-list": "Galériás cikk lista", "layout-content-type-services-box": "Szolgáltatások", "layout-content-type-podcast-app-recommender": "Podcast alkalmazás terelő", "layout-content-type-gastro-experience-occasion": "Élmény alkalom", "layout-content-type-experience-gift": "Élményt ajánd<PERSON>ba", "layout-content-type-gastro-occasion-recommender": "Nagy képes élmény ajánló", "layout-content-type-gastro-experience-recommendation": "<PERSON><PERSON><PERSON><PERSON>", "layout-content-type-gastro-thematic-recommender": "<PERSON><PERSON><PERSON><PERSON> te<PERSON>", "layout-content-type-gastro-experience-occasion-swiper": "Lapozható élmény alkalom", "layout-content-type-top-ranking-glossary": "Toplista", "layout-content-type-job-listings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "layout-content-type-where-the-ball-will-be": "Ahol a labda lesz", "layout-content-type-top-ten-tags": "TOP 10 címke", "layout-content-type-topic-suggestion": "<PERSON><PERSON><PERSON>", "layout-content-type-secret-days-calendar": "Kalendárium", "layout-content-type-top-commented-articles": "Legtöbbet kommentelt cikkek", "layout-drag-content-tooltip": "Az egérgombot lenyomva tartva mozgatható ez az elem.", "layout-page-type-column": "<PERSON><PERSON><PERSON>", "layout-page-type-columnsidebar": "<PERSON><PERSON><PERSON> k<PERSON> terü<PERSON>", "layout-page-type-custom-built-page": "<PERSON><PERSON><PERSON><PERSON>", "layout-page-type-custombuiltpage": "<PERSON><PERSON><PERSON><PERSON> oldal", "layout-page-type-homepage": "<PERSON><PERSON><PERSON><PERSON>", "layout-page-type-opinion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "layout-page-type-sidebar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "modify-template": "Elrendezés módosítása", "opinion-layouts-edit": "Vélemény elrendezés szerkesztése", "opinion-layouts-list": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "select-template": "Sablon kiválasztása", "sidebar-layouts-edit": "Közös terület szerkesztése", "sidebar-layouts-list": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "Meta": {"Author": "Szerző", "DestinationGallery": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DistanceFromBudapest": "Távolság <PERSON>ől", "Excerpt": "<PERSON><PERSON><PERSON>", "FacebookThumbnail": "Facebook Thumbnail", "IsForAdult": "Felnőtt tartalom", "Lead": "<PERSON><PERSON>", "Location": "Location", "MapLead": "Discover now leírás", "MetaDescription": "<PERSON><PERSON>", "MetaImage": "<PERSON><PERSON>", "MetaTitle": "<PERSON><PERSON> c<PERSON>m", "OnlyRss": "RSS", "Relation": {"Article": "Ka<PERSON><PERSON>ol<PERSON><PERSON><PERSON> tarta<PERSON>", "ColumnPage": "Ka<PERSON><PERSON>ol<PERSON><PERSON><PERSON> tarta<PERSON>", "Event": "Cimkék", "VideoPage": "Ka<PERSON><PERSON>ol<PERSON><PERSON><PERSON> tarta<PERSON>"}, "Seo": "SEO", "ShowRecommendedContentByColumn": "Lapv<PERSON><PERSON>", "ShowRecommendedContentGlobal": "<PERSON><PERSON><PERSON>l se maradj le!", "ShowRecommendedContentNewest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Slug": "URL", "Status": {"Article": "<PERSON><PERSON><PERSON><PERSON>", "CategoryDescription": "<PERSON><PERSON><PERSON><PERSON>", "Collection": "<PERSON><PERSON><PERSON><PERSON>", "ColumnPage": "<PERSON><PERSON><PERSON><PERSON>", "Destination": "<PERSON><PERSON><PERSON><PERSON>", "Event": "<PERSON><PERSON><PERSON><PERSON>", "HomePage": "<PERSON><PERSON><PERSON><PERSON>", "StaticPage": "<PERSON><PERSON><PERSON><PERSON>", "Trip": "<PERSON><PERSON><PERSON><PERSON>", "VideoPage": "<PERSON><PERSON><PERSON><PERSON>"}, "Thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "TimeHide": "<PERSON><PERSON><PERSON>", "Title": "Cím", "VisibleDate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "author": "Szerző"}, "Opinion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PrintorOnline": "Print / Online", "SeoScore": {"Bonus": "B<PERSON>usz pont", "CloseModal": "Bezárás", "FailedChecks": "Sikertelen ellenőrzések", "PassedChecks": "<PERSON><PERSON><PERSON> el<PERSON>", "SeoCheck": "SEO <PERSON>", "SeoScore": "SEO <PERSON>", "KeywordAlreadyInUse": "Ez a kulcsszó már felvitelre került. Kérem javítsa mielőtt új kulcsszót venne fel.", "MaximumKeywordCountReached": "<PERSON><PERSON> fel több kulcsó.", "MaximumOneKeywordCountReached": "A fő kulcsszó nem tartalmazhat több elemet.", "SeoScoreResult": "Ellen<PERSON><PERSON><PERSON>", "SeoScoreResultLabels": {"High": "Magas SEO érték", "Low": "Gyenge SEO érték", "Medium": "Közepes SEO érték"}, "Required": "Kötelez<PERSON> szabály", "point": "pont"}, "materials": {"own_material": "<PERSON><PERSON><PERSON><PERSON>", "other_material": "<PERSON><PERSON><PERSON><PERSON>", "news_service_material": "Hírügyeleti <PERSON>"}, "gastro-experience": "Élmén<PERSON>k", "gastro-experience-categories": "<PERSON><PERSON><PERSON><PERSON>", "gastro-experience-occasion": "Élmény alkalmak", "gastro-purchases": "Vásárlások", "gastro": {"experience": {"title": "<PERSON><PERSON><PERSON><PERSON>", "isActive": "Aktív", "occasionCount": "Alkalmak száma", "createdAt": "Létrehozva", "updatedAt": "Módosítva"}, "experienceCategory": {"title": "<PERSON><PERSON><PERSON><PERSON>", "statistics": "Statisztika", "isActive": "Aktív"}, "experience-occasion": {"title": "<PERSON><PERSON><PERSON><PERSON> neve", "date": "Alkalom napja és időpontja", "comingSoon": "<PERSON><PERSON><PERSON>", "host": "Házigazda", "seats": "Foglalt férőhelyek / Összes férőhely száma", "isActive": "Aktív", "createdAt": "Rög<PERSON><PERSON><PERSON><PERSON>", "updatedAt": "Utolsó módosítás dá<PERSON>"}, "occasion": {"title": "Alkalom"}, "purposeOfPurchase": "Vásárlás c<PERSON>", "studentsNumber": "Hány fő részére", "SimplePlay": "SimplePay fizetés", "SzamlazzHu": "Számla kiküldése / szamlazz.hu"}, "WeatherPredictions": {"prediction_hourly": "24 órás előrejelzés", "prediction_daily": "12 napos előrejelzés", "prediction_text": "Szöveges előrejelzés", "Icons": {"felhos": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "borult": "<PERSON><PERSON><PERSON>", "derult": "<PERSON><PERSON><PERSON>", "eso": "Eső", "felhoszakadas": "Felhőszakadás", "havazas": "Havazás", "havas_eso": "<PERSON><PERSON><PERSON>", "havaseso": "<PERSON><PERSON><PERSON>", "hofuvas": "Hófúvás", "hoszallingozas": "Hószállingózás", "hozapor": "Hózápor", "jegeso": "Jégeső", "kanikula": "<PERSON><PERSON><PERSON><PERSON>", "kod": "<PERSON><PERSON><PERSON>", "kodszitalas": "Ködszitálás", "onoseso": "Ónoseső", "onos_eso": "Ónoseső", "szeles": "<PERSON><PERSON><PERSON><PERSON>", "szitalas": "Szitálás", "valtozoan_felhos": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zapor": "Zápor", "zivatar": "Zivatar"}}, "Sidebar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Static page": "Statikus cikkek", "Trip": "<PERSON><PERSON><PERSON><PERSON>", "accepted": "J<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "accepted3": "J<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acceptedBuiltAt": "Elfogadva d<PERSON>", "acceptedLastBuiltResult": "Elfogadva státusz", "actions": "Műveletek", "activate": "Aktiválás", "activateRecipe": "Publikálás", "activateVersion": "V<PERSON><PERSON>ó <PERSON>iv<PERSON>", "add": "Hozzáadás", "add-recommendations": "Lap végi a<PERSON>ó oldalak hozzáadása", "addReview": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "add-to-header-bar": "Hozzáadás fejléchez", "addanswers": "<PERSON><PERSON><PERSON>z <PERSON>", "addblocks": "blokk hozzáadása", "addprogramDates": "<PERSON><PERSON><PERSON><PERSON> ho<PERSON>", "addquestions": "Kérdések hozzáadása", "addratings": "Értékelések hozzáadása", "admin": "Admin", "advertisement": "<PERSON><PERSON><PERSON><PERSON>", "advertisements": "<PERSON>rdetések", "allArticle": "Mindenhol ez a cikk legyen", "allArticleAlternateTitle": "Mindenhol ez az egyedi cím legyen", "allDossier": "Mindenhol ez a dosszié legyen", "allVotings": "Mindenhol ez a szavazás legyen", "all_users": "<PERSON><PERSON> f<PERSON>", "allowedLanguages": "Nyelvek", "alternateTitle": "<PERSON>gyed<PERSON> cím", "alternative_view": "Alternatív nézet", "always-newest": "Mindig a legfrissebb megjelenítése", "only-guarantee-recipe": "Csak garanciális receptek", "only-recipes-with-image": "Elsődleges képpel rendelkező recept", "only-recipes-with-secondary-image": "Másodlagos k<PERSON> rendelkező recept", "amend": "Módosítás", "android_users": "Android felhasználók", "answer": "<PERSON><PERSON><PERSON><PERSON>", "answers": "Válaszok", "app_facebook_post": "Facebook posztolás", "approve-add-contributor": "Szerző hozzáadása", "approve-page": "oldal", "approve_articles": "Cikkek elfogadása", "article": "Cikk", "article-network-slot": "<PERSON><PERSON><PERSON> hely", "articleNetworkSlots": "<PERSON><PERSON><PERSON>", "articleVersions": {"versionList": {"title": "Változatok"}}, "article_and_set_print_status": "Vágólapra és tördel", "articles": "Cikkek", "author": "Szerző", "authors": "Szerzők", "back": "<PERSON><PERSON><PERSON>", "bama": "<PERSON><PERSON>", "bannerName": "Banner név", "baon": "Baon", "base-layouts": "Sablonok", "beol": "Beol", "block-has-line": "Vízszintes elválasztó a szekció után", "block-has-title": "Címmel rendelkező szekció", "block-has-horizontal-element-separator": "Vízszintes elválasztás az elemek között", "block-has-vertical-element-separator": "Függőleges elválasztás az elemek között", "desktop-header": "Desktop header", "desktop-footer": "Desktop footer", "mobil-header": "<PERSON><PERSON> header", "mobil-footer": "<PERSON><PERSON> footer", "blocks": "Játék blockok", "boon": "<PERSON><PERSON>", "bottom-recommendations": "Lap végi <PERSON>", "boxes": "Doboz", "branding-box": "Branding box", "branding-boxes": "Branding boxok", "brandingbox": "Brandingbox", "buttonLabel": "<PERSON><PERSON> felirat", "buttonUrl": "Gomb url", "can_add_html": "HTML hozzáadása", "cancel": "<PERSON><PERSON><PERSON><PERSON>", "cancel-margin": "<PERSON><PERSON><PERSON><PERSON><PERSON> ma<PERSON> k<PERSON>", "cannotBeDeleted": "A komponens nem törölhető!", "categories": "Rova<PERSON>", "categories-real": "Kate<PERSON><PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON>", "category-page": "Kategó<PERSON>", "category-pages": "<PERSON><PERSON><PERSON>", "central": "Központi", "centralPositions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "central_sending": "Központi küldés", "changeLog": {"mode": {"activate": "Aktiválás", "createdByCopy": "Másolás", "delete": "Törlés", "formSave": "Változtatás", "imported": "Importálás", "inactivate": "Inaktiválás", "other": "<PERSON><PERSON><PERSON><PERSON>", "publicate": "Publikálás", "publish": "Publikálás", "restore": "Visszaállítás", "verToAccepted": "Verzióváltás (elfogadva)", "verToDeclined": "Verzióváltás (elutasítva)", "verToWaiting": "Verzióváltás (várakoztatva)"}}, "change_author": "Szerző megváltoztatása", "characterCount": "Karakterszám", "characters": "Karakterek", "check": "Validálás", "city7": "City7", "closed": "<PERSON><PERSON><PERSON><PERSON>", "cms-users": "CMS felhasználók", "portal-users": "Olvasók", "collection-pages": "Gyűjtő oldalak", "color": "Szín", "column": "<PERSON><PERSON><PERSON>", "columns": "Rova<PERSON>", "column-layouts": "<PERSON><PERSON><PERSON>", "columnTitle": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON> t<PERSON>", "columnsidebar-layouts": "Rovat közö<PERSON> területek", "commercial": "<PERSON><PERSON><PERSON><PERSON>", "commercials": "<PERSON>rdetések", "condition": "<PERSON><PERSON><PERSON>", "config": "Be<PERSON>llít<PERSON>", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "confirm_action": "Művelet végrehajtása?", "confirm_delete_layout_element_lead": "Ezzel az összes belső elem is törlésre kerül!", "confirm_delete_layout_element_title": "Biztos törölni akarja az elemet?", "confirm_modify_layout_element_title": "Biztos módosítani akarja az elemet?", "contact": "Tartalom", "content": "Tartalom", "contentNotSet": "<PERSON><PERSON><PERSON> cikk", "contentOverride": "Tartalom f<PERSON>ü<PERSON>í<PERSON>", "contentOverrideDescription": "Válassza ki hogy mely mezők értékei legyenek felülírva a most kiválasztott tartalmi oldal értékeivel!", "contentPage": "<PERSON><PERSON><PERSON><PERSON> oldal", "contentType": "<PERSON><PERSON><PERSON><PERSON> típus", "foundationTagTitle": "Alapkő tartalom címke", "content_form": {"basic_label": {"audio": "Audio fájl", "color": "Szín", "content": "Tartalom", "contentpage": "<PERSON><PERSON><PERSON><PERSON> oldal", "contenttype": "<PERSON><PERSON><PERSON><PERSON> típus", "date": "<PERSON><PERSON><PERSON>", "description": "Le<PERSON><PERSON><PERSON>", "duration": "Időtar<PERSON>", "event": "<PERSON><PERSON><PERSON><PERSON>", "eventdatefrom": "Kezdő dátum", "eventdatetill": "<PERSON><PERSON><PERSON><PERSON>", "excerpt": "Bevezető", "fontsize": "<PERSON><PERSON><PERSON>", "functionalicons": "Funkcionális <PERSON>ok", "functionaltitle": "Funkció cí<PERSON>", "howlongittakes": "Mennyi ideig tart?", "imagetitle": "<PERSON><PERSON><PERSON> feli<PERSON>", "isforadult": "Felnőtt tartalom", "lead": "Bevezető", "leftrightposition": "<PERSON><PERSON><PERSON>", "manycolumns": "Rova<PERSON>", "onecolumn": "<PERSON><PERSON><PERSON>", "onlyrss": "RSS átadás", "practicalinfoicon": "Praktikus info ikon", "publishdate": "Publikálási d<PERSON>", "rightorigin": "Oldalsáv", "show": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "streamurl": "Stream URL", "tags": "Címkék", "tagtext": "Címkék", "text": "Szöveg", "timehide": "<PERSON><PERSON><PERSON>", "title": "Cím", "startMatch": "<PERSON><PERSON><PERSON> eleji ill<PERSON>", "endMatch": "Szó végi ill<PERSON>", "triplength": "Hány napos az utazás?", "value": "<PERSON><PERSON><PERSON><PERSON>", "viacar": "<PERSON><PERSON><PERSON><PERSON>", "viatransport": "Tömegközlekedéssel", "vod": "<PERSON><PERSON><PERSON>"}, "address": {"searchGoogleMaps": "Keresés Google Mapsen"}, "openingHours": {"day": "Nap", "open-time": "Nyitvatart<PERSON>", "actions": "Műveletek", "addDay": "<PERSON><PERSON>", "addTime": "<PERSON><PERSON><PERSON>", "days": {"monday": "Hétfő", "tuesday": "<PERSON><PERSON>", "wednesday": "Szerda", "thursday": "Csütörtök", "friday": "Péntek", "saturday": "Szombat", "sunday": "Vas<PERSON>rna<PERSON>"}, "openInSelectedTimes": "Nyitva a megadott időben", "openAllDay": "Egész nap nyitva", "closed": "<PERSON><PERSON><PERSON>", "date-already-exists": "A megadott dátummal már van megadva nyitvatartási nap. <PERSON><PERSON>rem v<PERSON>zon másik dátumot."}, "otherTimes": {"timeName": "<PERSON><PERSON><PERSON>", "time": "<PERSON><PERSON><PERSON> (percben)", "actions": "Műveletek", "addTime": "<PERSON><PERSON><PERSON>", "timeNameAlreadyExists": "A megadott idő megnevez<PERSON>sel már van egyéb idő!"}}, "automails": "Automailek", "partnerKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mailTo": "Automail partner", "partners": "<PERSON><PERSON>", "partnerName": "Partner neve", "isEnabledExtra": "Extra", "isEnabledFrontalSystem": "Fronthatás", "isEnabledNow": "<PERSON><PERSON><PERSON><PERSON>", "isEnabledPrediction": "Előrejelzés", "isEnabledTextPrediction": "Szöveges előrejelzés", "isEnabledTv": "TV", "automail": "Automatikus levelek", "contents": "Tartalom", "contributor": "Elszámolási szerző", "contributor-percent-warning": "Elszámolási szerzők aránya több, mint 100%, ezért az eredeti érték került visszaállításra.", "copied": "Vágólapra másolva", "copiedFrom": "M<PERSON>ol<PERSON>", "copy": "Másolás", "copyAndFormatted": "Vágólapra és tördel", "copyAsOnline": "Másolás mint Online", "copyAsPrint": "Másolás mint Print", "copyOrOriginal": "Másolat / Eredeti", "copyToClipboard": "Vágólapra másolás", "create": "Hozzáadás", "createContent": "Új tartalom létrehozása", "create_by_params": "Létrehoz<PERSON> parammal", "createdAt": "Létrehozás <PERSON>", "competition_team": "Bajnokság csapat", "competition_team_player": "Bajnokság csapat j<PERSON>", "current": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currentLayoutDeleteError": "Aktuális {{ layoutType }} törlésére nincs le<PERSON>őség", "custom": "<PERSON><PERSON><PERSON><PERSON>", "custom-built-layouts": "<PERSON><PERSON><PERSON><PERSON>", "custom-built-page": "<PERSON><PERSON><PERSON><PERSON> oldal", "custom-built-pages": "<PERSON><PERSON><PERSON><PERSON>", "customUrl": "Egyéni URL", "custom_built_page": "<PERSON><PERSON><PERSON><PERSON> oldal", "dashboard": "Vezérlőpult", "data": "Ada<PERSON>", "datastudio": "Datastudio", "date": "<PERSON><PERSON><PERSON>", "dateFrom": "Ettől", "dateOfLastSearch": "Utolsó keresés időpontja", "dateRangeError": "Hibás intervallum!", "statisticsDateRangeError": "Hib<PERSON> intervallum, a kettő dátum között maximum 90 nap lehet!", "commentsDateRangeError": "<PERSON><PERSON><PERSON> intervallum, a kettő dátum között maximum 3 hónap lehet!", "dateUntil": "<PERSON><PERSON><PERSON>", "delete": "Törlés", "deleteComponent": "Komponens törlése", "deleteComponentConfirm": "Biztos törlöd ezt a komponenst?", "delete_selected": "Kijelöltek törlése", "deleted": "Törölve", "successfulDeletion": "Sikeresen törölve", "delmagyar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Le<PERSON><PERSON><PERSON>", "deselectTitle": "Kiválasztás elvetése", "desktop-view": "<PERSON><PERSON><PERSON><PERSON>", "destinationBalaton": "Balaton", "destinationBudapest": "Budapest", "destinationDebrecen": "Debrecen", "destinationEger": "<PERSON><PERSON>", "destinationGyor": "Győr", "destinationGyula": "<PERSON><PERSON><PERSON>", "destinationPecs": "Pécs", "destinationSopron": "Sopron", "destinationSzeged": "Szeged", "destinationTag": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "destinationTags": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "destinationTokaj": "Tokaj", "destinations": "Desztinációk", "deutsch": "Német", "discount": "<PERSON><PERSON><PERSON><PERSON>", "document-groups": "Dokumentum kategóriák", "documents": "Dokumentumok", "dont-miss": "<PERSON><PERSON><PERSON><PERSON> se maradj le", "dossier": "<PERSON><PERSON><PERSON><PERSON>", "dossier-network-slot": "<PERSON><PERSON><PERSON><PERSON>", "dossierNetworkSlots": "<PERSON><PERSON><PERSON><PERSON>", "dossiers": "Dossziék", "download": "Letöltés", "downloads": "Letöltések", "draft": "<PERSON><PERSON><PERSON><PERSON>", "draftBuiltAt": "Előnézet d<PERSON>", "draftLastBuiltResult": "Előnézet státusz", "duol": "Duol", "duration": "Időtar<PERSON>", "dynamic_form": "Din<PERSON><PERSON> formok (fejlesztés alatt)", "edit": "Szerkesztés", "editComponent": "Komponens szerkesztése", "editDieselPrice": "Diesel ár s<PERSON>", "editImageData": "<PERSON><PERSON><PERSON><PERSON> módosítása", "editImageDataInstructions": "Adja meg a kép adatait", "editPetrolPrice": "Benzin 95 ár s<PERSON>", "edited": "Szerkesztett", "edited1": "Szerkesztett", "editor": "Editor", "editorComment": "Szerkesztői megjegyzés", "email": "E-mail cím", "emailRequired": "Kérjük adja meg az email címet!", "endDate": "<PERSON><PERSON><PERSON><PERSON>", "english": "Angol", "entity-log": "Entity log", "entity_change": "Változás logok", "erdon": "<PERSON><PERSON>", "error": "Hiba", "eventDateTill": "<PERSON><PERSON><PERSON><PERSON>", "eventDateTillMustBeGreater": "Záró d<PERSON>nak később kell lennie mint a kezdődátumnak!", "events": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "excerpt": "<PERSON><PERSON><PERSON>", "execute": "Végrehajtás", "existingFile": "Je<PERSON>legi fájl", "expired": "<PERSON><PERSON><PERSON><PERSON>", "exportToclipboard": "Vágólapra másolás", "exportXmlsx": "Exportálás XLSX formátumban", "external_list": "Külsős branding box lista", "failed": "Sikertelen", "fee": "<PERSON><PERSON><PERSON>", "feol": "Feol", "field": "Mező", "field_required": "A mező kitöltése kötelező!", "fields_required": "A mezők kitöltése kötelező!", "file": "<PERSON><PERSON><PERSON>", "filter-not-opinion": "Vélemény tartalom kih<PERSON>", "filter-not-podcast": "Podcast tartalom kihagyása", "filter-not-sponsorship": "Szponzorált tartalom kihagyása", "firstName": "Keresztnév", "flekk": "Flekk", "following-errors": "A művelet végrehajtása során az alábbi hibák keletkeztek:", "footer": "Footer", "footer_szegedma": "Footer", "footer_gastro": "Mindmegette Élmények footer", "footer_0": "1. <PERSON><PERSON><PERSON>", "footer_1": "2. <PERSON><PERSON><PERSON>", "footer_2": "3. <PERSON><PERSON><PERSON>", "footer_0_origo": "Origo footer", "footer_1_origo": "Origo Sport footer", "forceUnlock": "<PERSON><PERSON><PERSON><PERSON> felold<PERSON>", "force_unlock": "<PERSON><PERSON><PERSON><PERSON> felold<PERSON>", "form": "Űrlap", "frackedUp": "<PERSON><PERSON><PERSON><PERSON>", "fracked_up4": "<PERSON><PERSON><PERSON><PERSON>", "francais": "Francia", "functionalIcon": "Funkcionális ikon", "functionalIcons": "Funkcionális <PERSON>ok", "futurePublicDateWarning": "Kedves Kolléga! Amennyiben a cikked jövőbeli dátumra időzíted, az érintett dátumig az nem lesz elérhető az olvasók számára! Ne felejtsd el visszadátumozni a cikket mielőtt publikálod!", "gallery": "Galéria", "galleryItems": "<PERSON><PERSON><PERSON><PERSON>", "game": "<PERSON><PERSON><PERSON><PERSON>", "gameBlockCount": "Blokkok száma", "games": "Játékok", "getdatas": "<PERSON><PERSON><PERSON><PERSON>", "global_delete": "Global törlés", "global_list": "Global lista", "global_restore": "Glob<PERSON><PERSON>llí<PERSON>", "googleDataStudio": "Google Data Studio", "group": "Kategória", "haon": "<PERSON><PERSON>", "has-lenia": "Elválasztó lénia", "hasAccepted": "Elfogadva", "hasDraft": "Előnézet", "hashtag": "Hashtag", "header": "Header", "header_gastro": "Mindmegette Élmények header", "header_0": "Főmenü bal oldal", "header_1": "Főmenü jobb oldal", "header_0_szegedma": "Főmenü", "header_1_szegedma": "Hamburger menü", "header_0_origo": "Origo felső sáv", "header_1_origo": "Origo Hamburger menü", "header_3_origo": "Sport felső sáv", "header_4_origo": "Sport hambuger/témák", "header_5_origo": "Sport hambuger/ered<PERSON>nyek", "headerbar": "headerbar", "heol": "<PERSON><PERSON>", "hide-mobile": "Elem elrejtése mobil eszközökön", "has-background": "<PERSON><PERSON><PERSON><PERSON><PERSON> rendelk<PERSON>", "hide-article-type": "<PERSON><PERSON><PERSON><PERSON> tí<PERSON>", "hasRightMargin": "<PERSON><PERSON> marg<PERSON><PERSON> rendelk<PERSON>", "hasLeftMargin": "<PERSON>l <PERSON><PERSON> margó<PERSON> rendelkezik", "marginBorderColor": "<PERSON><PERSON><PERSON>", "highlight": "<PERSON><PERSON><PERSON><PERSON>", "highlighted": "<PERSON><PERSON><PERSON><PERSON>", "home-layouts": "<PERSON><PERSON><PERSON><PERSON>", "home_page": "<PERSON><PERSON><PERSON><PERSON>", "howLongItTakes": "Mennyi ideig tart?", "ios_users": "iOS felhasználók", "image": "<PERSON><PERSON><PERSON>", "imageBoxes": "Képek", "imageDescription": "Le<PERSON><PERSON><PERSON>", "imageEditorInstructions": "Válassz képet a Galériából, majd válaszd ki a megfelelő vágást", "imageName": "<PERSON><PERSON><PERSON> neve", "imageSizeTooLarge": "A kép mérete túl nagy!", "imageUploadDescription": "Kattints vagy húzd ide a képeket a feltöltéshez", "images": "Képek", "import": "Import", "import-approve": "Elfogadásra váró c<PERSON>", "import-approve-seach": "<PERSON><PERSON>", "import-print-xml": "Print Visszatöltés", "import-render-added-article": "Hozzárendelt cikk", "import-render-cms-articles": "A CMS-ben lé<PERSON> cikkek ", "import-render-column": "<PERSON><PERSON><PERSON>", "import-render-contributor": "Elsz. szerző", "import-render-delete-confirmation": "Biztos s<PERSON>etnéd törölni?", "import-render-email": "E-mail", "import-render-no-article": "Nem tal<PERSON>lt<PERSON> cikke(ke)t az xml-ben. <PERSON>, hogy hibás az xml formátum!", "import-render-no-selection": "nincs k<PERSON>las<PERSON>t<PERSON>", "import-render-other-xml-items": "Egyéb XML-b<PERSON><PERSON> jöv<PERSON> elemek", "import-render-pick-author": "Válassz szerzőt", "import-render-public-author": "Nyilvános szerző", "import-render-xml-article": "XML cikkek", "import-settings": "Import be<PERSON>ll<PERSON>", "import-settings-allow-mutation": "Mutáció/többszörös feltöltés engedélyezése", "import-settings-default_group": "Alapértelmezett rovat", "import-settings-kpi": "KPI?", "import-settings-mutation": "Mutációk", "import-settings-mutation-label": "Csak engedélyezett mutáció esetén kell kitölteni.\r\nAz alaplapot nem kell hozzáadni\nEgy sorba egy mutáció kerüljön", "import-xml": "Visszatöltés", "import-xml-title": "Print cikkek visszatöltése XML-ből", "important_article": "Fontos", "in_progress": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inactivate": "Inaktiválás", "inactivateRecipe": "Visszavétel", "inactive": "Inaktív", "institution": "Intézmény", "institutions": "Intézmények", "institution-categories": "Intézmény kategóriák", "institution-category": "Intézmény kategóriák", "interstitial": "Interstitial", "invalidCredentials": "Hibás felhasználónév vagy jelszó!", "isAccepted": "Elfogadva", "isActive": "Aktív", "isCorrect": "<PERSON><PERSON><PERSON>", "isGreen": "<PERSON><PERSON><PERSON>", "isHidden": "<PERSON><PERSON><PERSON><PERSON>", "isOnline": "Online", "isPinnedToHomePage": "Főoldalra kitűzés", "isPrint": "Print", "isSentInNewsletters": "<PERSON><PERSON><PERSON><PERSON>", "is_on_header_bar": "Megjelenik fejlécben", "item": "Elem", "items": "Elem", "journal_issue": "Hetilap", "journal-issue": "Hetilap", "journal_issues": "<PERSON><PERSON>ap<PERSON>", "journal-issues": "<PERSON><PERSON>ap<PERSON>", "kemma": "Kemma", "key": "<PERSON><PERSON><PERSON>", "keyword": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keywordCount": "Keresőszó beküldéseinek a száma", "kisalfold": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kpi": "KPI", "kpi-all-article-slot": "Összes KPI cikk hely", "kpi-article-slot": "KPI cikkhely", "kpi-article-slot-1": "KPI 1. cikk hely", "kpi-article-slot-2": "KPI 2. cikk hely", "kpi-article-slot-3": "KPI 3. cikk hely", "kpi-article-slot-4": "KPI 4. cikk hely", "kpi-article-slots": "KPI cikk helyek", "kpiStatus": "KPI Státusz", "label": {"allowedLanguages": "Nyelvek", "color": "Szín", "contentPage": "<PERSON><PERSON><PERSON><PERSON> oldal", "contentType": "<PERSON><PERSON><PERSON><PERSON> típus", "customUrl": "Egyéni URL", "description": "Le<PERSON><PERSON><PERSON>", "destinationTag": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "E-mail cím", "externalDate": "External date", "externalId": "Azonosító", "facebookUrl": "Facebook Url", "firstName": "Keresztnév", "footerContactLead": "Lábléc szöveg", "footerContactTitle": "Lábléc cím", "group": "Kategória", "instagramUrl": "Instagram Url", "isActive": "Aktív", "isHidden": "<PERSON><PERSON><PERSON><PERSON>", "isPublic": "<PERSON><PERSON><PERSON>", "key": "<PERSON><PERSON><PERSON>", "label": "Név", "language": "Nyelv", "lastName": "Vezetéknév", "latitude": "Szélességi fok", "lcid": "Azonosító", "locale": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "longitude": "Hosszúsági fok", "newPassword": "<PERSON><PERSON>", "publishDate": "Publikálás <PERSON>", "relatedKey": "<PERSON><PERSON><PERSON><PERSON> oldal", "relatedType": "<PERSON><PERSON><PERSON><PERSON> típus", "roles": "Jogosultságok", "slug": "URL", "targetBlank": "Megnyitás új ablakban", "thematicIcon": "<PERSON><PERSON><PERSON><PERSON>", "title": "Cím", "twitterUrl": "Twitter Url", "value": "<PERSON><PERSON><PERSON><PERSON>"}, "labelDeleted": "T<PERSON><PERSON><PERSON>ltek", "labelList": "Lista", "language": "Nyelv", "languages": "Nyelvesítés", "lastName": "Vezetéknév", "lastUpdated": "Utolsó módosítás", "last10MinutesPageViews": "Meg<PERSON>kintések (10 perc)", "last20MinutesPageViews": "Megtekintések (20 perc)", "last60MinutesPageViews": "Meg<PERSON>kintések (60 perc)", "lat": "Latitude", "layer": "Layer", "layout": "Címlap szerkesztő", "layout-created": "Elrendezés létrehozva.", "layout-title": "Címlapszerkesztő", "layout_editor": "Címlap szerkesztő", "layout_log": "Címlap szerkesztő logolás", "layout_sidebar-layouts": "Címlap szerkesztő: közös területek", "layouts": "Layout", "lead": "Le<PERSON><PERSON><PERSON>", "lead_article": "Vezető cikk", "leaderboard_1": "Leaderboard #1", "leaderboard_2": "Leaderboard #2", "mobilrectangle_footer": "Mobile rectangle footer", "leaderboard_footer": "Leaderboard footer", "leading-article": "Vezércikk", "left": "<PERSON>l", "link": "Link url", "list": "Listázás", "live_sport": "Sportágak", "lng": "Longitude", "locale": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "location": "<PERSON><PERSON><PERSON><PERSON>", "locations": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "log": "Logolás", "login": "Bejelentkezés", "logout": "Kijelentkezés", "long_name_bama": "Dunánt<PERSON><PERSON> bama.hu", "long_name_baon": "<PERSON><PERSON><PERSON> – baon.hu", "long_name_beol": "Békés Megyei Hírlap – beol.hu", "long_name_boon": "Észak-Magyarország – boon.hu", "long_name_delmagyar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "long_name_duol": "Dunaújv<PERSON><PERSON><PERSON> duol.hu", "long_name_erdon": "<PERSON><PERSON>", "long_name_feol": "<PERSON><PERSON><PERSON><PERSON> feol.hu", "long_name_haon": "Hajdú-<PERSON><PERSON> haon.hu", "long_name_heol": "<PERSON><PERSON> heol.hu", "long_name_kemma": "24 Óra – kemma.hu", "long_name_kisalfold": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "long_name_nool": "Nógr<PERSON><PERSON> nool.hu", "long_name_sonline": "Somogyi <PERSON> sonline.hu", "long_name_szoljon": "<PERSON>j <PERSON> – szoljon.hu", "long_name_szon": "Kelet-Magyarország – szon.hu", "long_name_teol": "Tolnai Népújság – teol.hu", "long_name_vaol": "<PERSON><PERSON> – vaol.hu", "long_name_veol": "Veszprémi <PERSON>ó – veol.hu", "long_name_zaol": "Zalai <PERSON> zaol.hu", "main-page": "Főoldal szerkesztő", "manage": "Kezelés", "mapIcon": "Térkép ikon", "mapIconSubtitle": "Térkép ikon alcím", "mapIconSubtitle0": "Térkép alcím 1", "mapIconSubtitle1": "Térkép alcím 2", "mapIconSubtitle2": "Térkép alcím 3", "mapTitle": "Térké<PERSON> c<PERSON>", "marketplace": "Apróhirdetés", "marketplace-ads": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marketplace-category": "Apróhirdetés kategória", "marketplace-item": "Apróhirdetés cikkek", "marketplace-items": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "media": "Média", "mediaStore": {"gallery": {"validation": {"coverImageRequired": "Borítókép kiválasztása kötelező", "dateRequired": "Dátum megadása kötelező", "imagesRequired": "Legalább egy kép kiválasztása kötelező", "slugRequired": "Slug megadása kötelező", "titleRequired": "Cím megadása kötelező"}}}, "files": "Fájlok", "mediaStoreFiles": "PDF fájlok", "mediaStoreGalleries": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mediaStoreImages": "Képek", "medium": "Medium", "box_1": "Box #1", "box_2": "Box #2", "box_3": "Box #3", "box_4": "Box #4", "menu": "Menüszerkesztő", "mergeTags": {"addTag": "Új címke kijelölése", "mainTag": "Gyűjtőcímke", "nTag": "{{value}}. c<PERSON><PERSON><PERSON>", "searchTag": "Címke k<PERSON>", "selectMainTag": "Gyűjtőcímke kijelölése", "selectTagsToMerge": "Összevonandó címkék kijelölése", "submitBtn": "Összevonás indítása", "success": "A címkék összevonása sikeres!", "title": "Címkék összevonása", "warning": "A kiválasztott gyűjtőcímke az összevonandó címkék közt szerepel."}, "metaDatas": "Alapadatok", "metaTabChange_unsaved": "Az oldalon nem mentett változások vannak! Biztos elnavigál?", "mineOnly": "Csak saját k<PERSON>pek", "minute2minute": "Per<PERSON>r<PERSON>l percre", "minuteToMinute": "Per<PERSON>r<PERSON>l percre", "minuteToMinuteBlocks": "Bejegyzések", "minuteToMinuteBlocksSaveSuccessful": "Bejegyzések sikeresen mentve!", "minuteToMinuteUnauthorised": "Nem redelkezik jogosultsággal a percről percre modul szerkesztésére!", "mobile-view": "<PERSON><PERSON>", "modify": "Szerkesztés", "most-recent": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "move": "Mozgatás", "move-down-header": "Le léptet", "move-up-header": "<PERSON><PERSON> l<PERSON>", "mustStartWith": "Az oldal csak az alábbi tartalom típusokkal indulhat:<ul><li>Fejléckép</li><li>Lead + Térkép</li><li>Cím + Szöveg</li><li>Carousel</li></ul>", "mustStartWithMain": "Az oldal csak az alábbi tartalom típusokkal indulhat:<ul><li>Főoldali fej<PERSON></li><li>Fejléckép</li><li>Lead + Térkép</li><li>Cím + Szöveg</li><li>Carousel</li></ul>", "name": "Név", "network": "Network", "networkDistribution": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "network_slot": "Network slot", "new-article": "<PERSON>j cikk", "new-branding-box": "Új branding box", "newCrop": "Új vágás", "newPassword": "<PERSON><PERSON>", "newTab": "Megnyitás új ablakban", "new_menu_item": "<PERSON><PERSON> menü elem hozz<PERSON>ad<PERSON>", "new_recommendation_item": "<PERSON><PERSON>", "news-reception": "Hírátvétel", "no": "Nem", "no-title": "(nincs cím)", "noAvailableCrop1": "<PERSON><PERSON>g nincs", "noAvailableCrop2": "méretű vágás ehhez a képhez", "noContentSelected": "<PERSON><PERSON>leg még nem választottál tartalmat az oldalhoz.", "no_menu_tree_element": "<PERSON><PERSON><PERSON>", "no_recommendations": "<PERSON><PERSON><PERSON>", "nonpublic": "Publikálás v<PERSON>", "nool": "Nool", "normal_article": "<PERSON><PERSON><PERSON><PERSON>", "not": "Nem", "not_sent_yet": "Nem volt még kiküldve", "ok": "Rendben", "online": "Online", "onlineArticles": "Online cikkek", "onlineStreaming": "Online streaming", "openInNewTab": "Megnyitás új <PERSON>", "opinion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "opinion-layouts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "opinionHeaderControl": "Vélemény címmel rendelkező tartalom", "osszes": "Összes", "others": "Másoké", "own": "<PERSON><PERSON><PERSON><PERSON>", "page": "<PERSON><PERSON>", "pageCreateSuccessful": "Új oldal si<PERSON>esen létrehozva!", "pageSaveSuccessful": "Oldal sikeresen mentve!", "pageType": "<PERSON>al típus", "pageViews": "Oldalmegtekintések", "parent": "Szülő kategória", "password": "Je<PERSON><PERSON><PERSON>", "passwordrequired": "Kérjük adja meg a j<PERSON>!", "pdf-upload-btn": "PDF kiválasztása", "petrol95Price": "Benzin 95 ár", "petrolDieselPrice": "<PERSON> ár", "petrolPrices": "Üzemanyag <PERSON>", "player_position": "Játékos pozíció", "podcast": "Podcast", "podcasts": "Podcastok", "portal": "Portal", "portal-config": "Portal beállítások", "portalName": "<PERSON><PERSON><PERSON> oldal", "post": "Post", "preview": "Előnézet", "price": "<PERSON><PERSON>", "print": "Print", "printArticles": "Print cikkek", "printPageNumber": "PO", "printPublishedDate": "PN", "printPublishedDateLong": "<PERSON><PERSON><PERSON><PERSON>", "printStatus": "<PERSON><PERSON><PERSON><PERSON>", "print_export": "Print export", "print_status": "Munkafolyamatok", "print_xml": "Print xml", "priority": "Fontosság", "processing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "profile": "Profil", "program": "Program", "program-location": "Program helyszín", "program-locations": "<PERSON> he<PERSON><PERSON>", "program-recommendations": "Program ajánló", "program-type": "Program típus", "program-types": "Program típusok", "programDates": "Program időpontok", "programs": "Programok", "public": "Publikálás", "publicAuthor": "Nyilvános szerzők", "publicDate": "Publikálás <PERSON>", "publicState": "<PERSON><PERSON><PERSON>", "publicStateLong": "Publikálási <PERSON>", "public_authors": "Publikus szerzők", "publicate": "Publikálás", "publication": "<PERSON><PERSON><PERSON><PERSON>", "publications": "Kiadván<PERSON>k", "publish": "Publikálás", "publish-success": "Aktiv<PERSON><PERSON><PERSON>", "publishDate": "Publikálás <PERSON>", "published": "Publikált", "publishedAndModified": "Publikált (módosított)", "publishedAt": "Publikálás <PERSON>", "publishedExtendDate": "<PERSON><PERSON><PERSON><PERSON> f<PERSON>", "publishing": "Publikálás", "question": "<PERSON><PERSON><PERSON><PERSON>", "questionCount": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "questions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "quiz": "Kvíz", "quizzes": "Kvízek", "ratingFrom": "Értékt<PERSON>l", "ratingTo": "Értékig", "ratings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "read": "<PERSON><PERSON><PERSON><PERSON>", "read2": "<PERSON><PERSON><PERSON><PERSON>", "ready": "Elkészült", "ready0": "Elkészült", "recipe": "Recept", "recommendation": "Programajánló", "recommendationItemCount": "Lap végi ajánlók száma", "recommendations": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recommended": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "region": "<PERSON><PERSON><PERSON><PERSON>", "regions": "Régiók", "relatedDefinitionType": "<PERSON><PERSON><PERSON>", "reloaded": "Visszatöltött", "reloaded5": "Visszatöltött", "remove": "Törlés", "remove-to-header-bar": "Eltávolítás fejlécből", "render": "Rendelel<PERSON>", "resetEdit": "Módosítások visszavonása", "restore": "Visszaállítás", "successfulRestore": "<PERSON><PERSON><PERSON> v<PERSON>zaállítás", "quickUserAssigment": "G<PERSON>rs f<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "userMerge": "Szerzők összevonása", "results": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "right": "<PERSON><PERSON>", "right_side": "Head<PERSON>", "szponzorcsik_also": "Szponzorcsík alsó", "szponzorcsik_felso": "Szponzorcsík felső", "roadblock_1": "Roadblock #1", "roadblock_2": "Roadblock #2", "roadblock_3": "Roadblock #3", "roadblock_4": "Roadblock #4", "role-group": "Szerepkör csoport", "role-groups": "Szerepkörök", "role_group": "Szerepkör csoport", "rolegroup": "Szerepkör", "roles": "Jogosultságok", "row-custom": "<PERSON><PERSON><PERSON><PERSON> sor", "rowCount": "<PERSON><PERSON>", "running": "<PERSON><PERSON><PERSON>", "save": "Men<PERSON>s", "saveInProgress": "Men<PERSON><PERSON>", "saveComponent": "Komponens mentése", "saveMetaDatas": "Alapadatok mentése", "saved": "Sikeresen mentve", "schedule_event": "Ütemezett események", "schedule_live": "Ütemezett élő közvetítés", "search": "Keresés", "addBySearch": "<PERSON><PERSON><PERSON><PERSON>", "addBySearchTooltip": "Keresés összes találatát ({{count}} elem) hozzárendeli az aktuális válogatáshoz", "addBySearchTooltipDisabled": "Keresés szükséges", "search-static": "Statikus keresés", "search_static": "Statikus keresés", "secondary_header": "<PERSON><PERSON>", "secret_days_calendar": "Kalendárium", "select": "Kiválaszt", "select-a-portal": "Válassz portált!", "selectAll": "Összes kiválasztása", "selectCrop": "Vágás kiválasztása", "selectImage": "Kép k<PERSON>lasztás", "sendIn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sendInMinuteToMinute": "Bejegyzés beküldése", "sendInNewsletters": "Hírlevélbe küldve", "newsletterDate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> f<PERSON> d<PERSON>", "marketingLetterDate": "Marketing levél feliratkozás d<PERSON>a", "isMuted": "Némítva", "schedule_team_player": "Mérközés csapat j<PERSON>", "send_in_news": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> h<PERSON>", "send_in_newsletter": "Hírlevélbe k<PERSON>", "sending": "<PERSON><PERSON><PERSON><PERSON>", "sending status": "<PERSON><PERSON><PERSON><PERSON> (sending status)", "sending_status": "<PERSON><PERSON><PERSON><PERSON>", "sent": "Kiküldve", "setting": "Be<PERSON>llít<PERSON>", "setting_key": "<PERSON><PERSON><PERSON>", "settings": "Beállítások", "short_url": "Rövidített URL", "show": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "show-marketplace-category-valid-items": "Kategória érvényes cikkeinek megtekintése", "show_in_rss": "RSS-<PERSON> m<PERSON>", "sidebar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sidebar-layouts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sidebar_column": "Rovat közö<PERSON> területek", "site_list": "portálok listája", "sitesAndArticles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> he<PERSON> el<PERSON>", "sitesAndDossier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> he<PERSON> el<PERSON>", "sitesAndVotings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> he<PERSON> el<PERSON>", "slug": "URL", "sonline": "Sonline", "sort": "Rendez<PERSON>", "sort_on_header_bar": "<PERSON><PERSON><PERSON><PERSON> sorrend", "sponsorship": "Sz<PERSON><PERSON>or<PERSON><PERSON><PERSON>", "sponsorshipStatus": "Szponzoráció aktív", "sponsorships": "Szponzorációk", "didYouKnow": "Változó tartalmú branding box", "sport": "Sportág", "sports": "Sportágak", "startDate": "Kezdő dátum", "stat": "Statisztika", "staff_position": "Staff beosztások", "static-page": "Statikus oldal", "static-pages": "Statikus oldalak", "static_page": "Statikus oldal", "stations": "<PERSON><PERSON><PERSON>ások", "statistics": "Statisztikák", "subtitle": "Alcím", "subtitle0": "Alcím 1", "subtitle1": "Alcím 2", "subtitle2": "Alcím 3", "success": "<PERSON><PERSON><PERSON>", "szoljon": "Szoljon", "szon": "Szon", "tag": "<PERSON><PERSON><PERSON><PERSON>", "tags": "Címkék", "tags-on-header": "Fejlécben megjelenő címkék", "tagsOnHeaderBar": "Fejlécben megjelenő címkék", "targetAudienceTags": "Célcsoport címkék", "team-players": "Csapathoz rendelt j<PERSON>", "template": "Sablon", "templates-base": "Alap sablonok", "templates-column": "<PERSON><PERSON><PERSON>", "templates-home": "Home layoutok", "templates-opinion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "teol": "Te<PERSON>", "text": "Szöveg", "thematicIcon": "<PERSON><PERSON><PERSON><PERSON>", "timeHide": "<PERSON><PERSON><PERSON>", "timed": "Időzített", "timedAndModified": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (módosított)", "title": "Cím", "to_activate": "Aktiválás", "to_active": "Aktiválás", "to_clipboard": "Vágólapra", "to_inactivate": "Inaktiválás", "to_inactive": "Inaktiválás", "totalResults": "Összes találat", "translation": "Nyelvesítés", "translations": "Fordít<PERSON>ok", "tripLength": "Hány napos az utazás?", "tripLengthShort": "<PERSON><PERSON><PERSON>", "trips": "<PERSON><PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "types": "Típusok", "tv_station": "TV adók", "unhighlight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unknownError": "Ismeretlen hiba történt!", "unpublish-success": "Inaktiv<PERSON><PERSON><PERSON>", "unsavedChangesConfirmation": "Biztos el kívánja hagyni az oldalt mentés nélkül?", "update": "Szerkesztés", "updateContent": "Tartalom szerkesztése", "upload": "Feltöltés", "upload-confirm-btn": "OK", "uploadFromVideo": "Kép kivágása videóból", "url": "Url", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "user-permissions": "Felhasználói jogok", "user_content": "Felhas<PERSON><PERSON><PERSON><PERSON> be<PERSON>üldött tartalom", "user_permission": "Felhasználói jogok", "user_permission_wrong_portal": "<PERSON><PERSON><PERSON> joga az aktív portálhoz, <PERSON><PERSON> nem szerkeszthetők a felhasználói jogok.", "users": "Felhasználók", "users-statistics": "Statisztikák", "columns_performance": "Rovatok teljesítménye", "authors_performance": "Szerzők teljesítménye", "default_statistics": "Általános statiszika", "users_statistics": "Statisztikák", "author_performance_all_materials": "Összesített anyagok", "author_performance_news_agency_material": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "author_performance_own_material": "<PERSON><PERSON><PERSON><PERSON>", "validFromDate": "Kezdet", "validToDate": "Befejezés", "value": "<PERSON><PERSON><PERSON><PERSON>", "vaol": "Vaol", "veol": "Veol", "viaCar": "<PERSON><PERSON><PERSON><PERSON>", "viaTransport": "Tömegközlekedéssel", "videaUrl": "Video url", "video": "<PERSON><PERSON><PERSON>", "video-articles": "<PERSON><PERSON><PERSON>", "video-content-turned-on": "Videós tartalom bekapcsolva", "videos": "Videók", "view": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "views": "Megtekintések", "vod": "<PERSON><PERSON><PERSON>", "vote": "Szava<PERSON><PERSON>", "voteCount": "Szavazatok száma", "voting": "Szavazás", "multi-vote": "Multi-Szavazás", "voting-network-slot": "<PERSON><PERSON><PERSON><PERSON><PERSON> hely", "votingNetworkSlots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "extraordinary-notification": "Rendkív<PERSON><PERSON>", "extraordinary_notification": "Rendkív<PERSON><PERSON>", "notificationTitle": "Értesítés címe", "notificationMessage": "Értesítés szövege", "bannerMessage": "Breaking sáv szövege", "waiting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "weightedViews": "Súlyozott megtekintések", "workflow": "Munkafolyamat", "xml-upload-btn": "XML kiválasztása", "yes": "Igen", "zaol": "Zaol", "zonaId": "Zona ID", "zoneId": "Zona ID", "pageViewsAvg": "Átlagos oldalmegtekintések", "numberOfArticles": "Cikkek száma", "customPageViews": "Egyedi f<PERSON> / Látogató", "customPageViewsAvg": "Átlagos egyedi felhasználók", "numberOfHoursWorked": "Ledolgozott órák száma", "authorPerformance": "Szerző teljesítménye", "workedHoursAndOnlinePerPrintArticlesDefaultText": "Ledolgozott órák / összes cikk (online és print is)", "gallery-content-turned-on": "Galériás tartalom bekapcsolva", "isInactiveAuthorError": "Inaktív elszámolási szerző nem rendelhető hozzá, kérem aktíválja. ({{ author }})", "pr-tags": "Pr címkék", "pr-tag": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "user-statistics": {"DATE_RANGE_ERROR_TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DATE_RANGE_ERROR_MESSAGE": "A kiválasztható időszak maximum 40 napot ölelhet fel."}, "prodName": "Termék neve", "transactionStatus": "<PERSON><PERSON><PERSON><PERSON>", "subStartDate": "Előfizetés kezdete", "subEndDate": "Előfizetés vége", "prodtype": "Termé<PERSON> t<PERSON>", "printed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subscriptionEmailTemplates": "E-mail templatek", "subscription-email-templates": "E-mail templatek", "event": "<PERSON><PERSON><PERSON><PERSON>", "subject": "<PERSON><PERSON><PERSON>", "reviews": "Sorozatvető", "restartPayment": "Beragadt előfizetés újraindítása", "glossary": "Szószedet", "sportlexikon": "Sportlexikon", "editWeightedPageViewsMultiplier": "Súlyozás", "extraordinary_article": "Rendkívüli cikk", "all_users_for_origo_app": "Minden felhasználó Origo App", "all_users_for_origo_sport_app": "Minden felhasználó Origo Sport App", "mobil_app_default": "Origo App", "mobil_app_sport": "Origo App Sport", "mobil_app_default_origo": "Origo App", "mobil_app_sport_origo": "Origo App Sport", "sport_schedule_live": "<PERSON><PERSON><PERSON> mérkőzés", "advertisement_edit": "<PERSON>rde<PERSON>s szerkeszté<PERSON>", "contentBlockImages": "Képes blokk cím", "featuredImage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "experience": "<PERSON><PERSON><PERSON><PERSON> neve", "startOfExperienceEvent": "Alkalom napja és időpontja", "isComingSoon": "<PERSON><PERSON><PERSON>", "place": "Alkalom helyszíne", "maxNumberOfSeats": "Férőhelyek száma", "hostAsPublicAuthor": "Házigazda", "star_dictionary_image": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "star_dictionary_gallery": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "star_dictionary_occupation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "star_dictionary_birthplace": "Születési <PERSON>ek", "star-dictionary-module": "Szt<PERSON><PERSON> lexikon", "star_dictionary_award": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "star_dictionary_star": "Sztárok", "occupation": "Foglalkozás", "award": "<PERSON><PERSON><PERSON>", "displayedName": "Megjelenítési név", "star": "<PERSON>z<PERSON><PERSON><PERSON>", "top-ranking-glossary": "Toplista", "top_ranking_glossary": "Toplista", "word-of-glossary": "Toplista elem", "promotion": "Promóció", "topRankingGlossary": {"word": "Toplista elem", "url": "URL", "order": "<PERSON><PERSON><PERSON>"}, "relatedRecipe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> receptek", "promotions": "Promóciók", "breadcrumbTitle": "Bread<PERSON><PERSON>b cím", "secretDaysCalendar": "Kalendárium", "secretDaysCalendars": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "numberOfDays": "Napok száma", "sponsorshipForFooterTitle": "Fő szponzor láblécbe", "sponsorshipForHeaderTitle": "Fő szponzor fejlécbe", "daysOrder": "<PERSON><PERSON><PERSON>"}, "isShowOnlyLoggedUsers": "Szavazás eredménye csak bejelentkezett olvasók számára megtekinthető", "isAdOceanEnabled": "AdOcean-b<PERSON>l szponzorált kvíz jobb has<PERSON>", "notificationTitle": "Értesítés címe", "notificationMessage": "Értesítés szövege", "bannerMessage": "Breaking sáv szövege", "birthName": "Születési név", "artistName": "Művésznév", "isHungarian": "<PERSON><PERSON><PERSON>", "occupations": "Foglalkozások", "awards": "<PERSON><PERSON><PERSON>", "socialMediaUrlFacebook": "Facebook oldal", "socialMediaUrlTiktok": "TikTok oldal", "socialMediaUrlInstagram": "Instagram oldal", "socialMediaUrlTwitter": "Twitter oldal", "birthplace": "Születési hely", "profileImage": "Profilkép", "gallery": "Galéria", "displayContextName": "Megjelenített név", "displayContextNameInitial": "Kezdőbetű", "horoscope": "Horoszkóp", "height": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "originalHairColor": "<PERSON><PERSON><PERSON>", "maritalStatus": "<PERSON><PERSON><PERSON><PERSON>", "partner": "Partner", "children": "<PERSON><PERSON><PERSON><PERSON>", "pet": "<PERSON><PERSON><PERSON>", "day": "Nap", "eloetel": "Előétel", "foetel": "Főétel", "desszert": "<PERSON><PERSON><PERSON>", "vega": "Vegetá<PERSON><PERSON><PERSON>", "didYouKnowDescription": "Tudta-e felirat", "didYouKnowTextColor": "Szöveg betűszín", "didYouKnowBackgroundColor": "Doboz h<PERSON>", "didYouKnowBackgroundImage": "Tudta-e háttérkép", "descriptionTextColor": "Leírás szövegszín", "descriptionBackgroundColor": "<PERSON><PERSON><PERSON><PERSON>", "footerImage": "<PERSON><PERSON><PERSON><PERSON><PERSON>rk<PERSON>", "footerUrl": "Lábléc url", "air_pollution": "Légszennyezettség", "temperatureScale": "Hőmérsékleti <PERSON>", "word": "<PERSON><PERSON>", "minuteToMinute": "Per<PERSON>r<PERSON>l percre", "acceptTerms": "Feltételek elfogadásának d<PERSON>a", "portalUserDetails": {"invoiceName": "Számlázási név", "invoiceZip": "Számlázási irányítószám", "invoiceCity": "Számlázási helység / város", "invoiceAddress": "Számlázási cím", "shippingName": "Szállítási név", "shippingZip": "Szállítási irányítószám", "shippingCity": "Szállítási helység / város", "shippingAddress": "Szállítási cím", "taxNumber": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "phoneNumber": "Telefonszám"}, "periodTitle": "<PERSON><PERSON><PERSON><PERSON>", "key": "<PERSON><PERSON><PERSON>", "gift": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "invoiceName": "Számlázási név", "invoiceZip": "Számlázási irányítószám", "invoiceCity": "Számlázási helység / város", "invoiceAddress": "Számlázási cím", "shippingName": "Szállítási név", "shippingZip": "Szállítási irányítószám", "shippingCity": "Szállítási helység / város", "shippingAddress": "Szállítási cím", "taxNumber": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "portalUserId": "<PERSON><PERSON><PERSON>", "productId": "Termék", "paymentMethod": "<PERSON><PERSON><PERSON><PERSON> mód", "subscriptionDetail": {"invoiceName": "Számlázási név", "invoiceZip": "Számlázási irányítószám", "invoiceCity": "Számlázási helység / város", "invoiceAddress": "Számlázási cím", "shippingName": "Szállítási név", "shippingZip": "Szállítási irányítószám", "shippingCity": "Szállítási helység / város", "shippingAddress": "Szállítási cím", "taxNumber": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "phoneNumber": "Telefonszám"}, "ArticleListWithTitle": {"ArticleListWithTitle": "Cikk lista címmel"}, "Rating": {"Rating": "<PERSON><PERSON><PERSON><PERSON>"}, "ContentGroup": {"RossmannQuiz": "<PERSON><PERSON> Kv<PERSON>", "Quiz": "Kvíz"}, "ContentPage": {"Article": "Cikk <PERSON>ó", "TwoArticle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (2db)"}, "StockMarketCurrency": {"StockMarketCurrency": "Értéktőzsde"}, "NewsletterSignUp": {"NewsletterSignUp": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "NewsletterGongSignup": {"NewsletterGongSignup": "<PERSON>"}, "Recommendation": {"ExternalRecommendation": "Külső cikk ajánló"}, "Datastudio": "Datastudio", "ERROR": {"required": "A mező kitöltése kötelező!", "maxlength": "Túl hosszú szöveg!"}, "Eadvert": {"Eadvert": "<PERSON><PERSON><PERSON><PERSON>"}, "Pagination": {"Pagination": "Oldaltö<PERSON><PERSON>"}, "Paywall": {"Paywall": "Előfizetéses tartalom határoló", "PaywallInserted": "Előfizetéses tartalom határoló automatikusan beszúrva a cikktörzsbe."}, "Game.GameBlock": "Nyereményj<PERSON><PERSON>", "Media": {"File": {"MediaFile": "File"}, "Gallery": {"Gallery": "Galéria"}, "Image": {"StringifiedImage": "<PERSON><PERSON><PERSON>"}, "Video": {"Video": "<PERSON><PERSON><PERSON>"}}, "TenArticleRcm": {"TenArticle": "Cikkaj<PERSON><PERSON>ó (1-10 cikk)"}, "TenArticleRcmVg": {"TenArticle": "Cikkaj<PERSON><PERSON>ó (1-10 cikk)"}, "TenOpinionArticleRcm": {"TenOpinionArticle": "Vélemény <PERSON>ó (1-10 vélemény)"}, "ROUTES": {"example": "pelda"}, "Subsequent": {"Dossier": {"Dossier": "<PERSON><PERSON><PERSON><PERSON>"}, "DossierNewsFeed": {"DossierNewsFeed": "Hírfolyam"}}, "Voting": {"Voting": "Szavazás"}, "MultiVote": {"MultiVote": "Multi-szavazás"}, "adOceanId": "Adocean azonosító", "isProtected": "<PERSON><PERSON><PERSON><PERSON>", "protectedEditor": "Védett cikk szerkesztői", "isFoundationContent": "Alapkő tartalom", "address": "Cím", "answer": "<PERSON><PERSON><PERSON><PERSON>", "answers": "Válaszok", "article": "Cikk", "avatar": "<PERSON><PERSON><PERSON><PERSON>", "avatars": "<PERSON><PERSON><PERSON><PERSON>", "bronze": "Bronz", "highlightedTag": "<PERSON><PERSON><PERSON><PERSON> cimke", "gender": "Nem", "results": "Eredmenyek", "competitionRaces": "Versenyszámok", "coach": "Edző", "club": "Klub", "videoUrl": "Videó URL", "backgroundColor": "Háttérsz<PERSON>", "bannerName": "Banner név", "blazonImage": "<PERSON><PERSON><PERSON>", "branding-box": "branding box", "can_change_article_author": "Modosithatja a cikk szerzőjét.", "can_edit_show_in_rss": "RSS checkboxot a Cikken modosithatja.", "column": "<PERSON><PERSON><PERSON>", "columnTypes": "<PERSON><PERSON><PERSON> t<PERSON>", "columns": "Rova<PERSON>", "content_type": {"label": {"hidden_menu": "<PERSON><PERSON><PERSON><PERSON> elem", "competition": "Bajnokság", "ads_page": "Apróhirdetések <PERSON>", "institution_collection": "0-24", "article": "Cikk", "article_collection": "<PERSON><PERSON><PERSON>", "journal_issue": "Hetilap", "journal_issueCollection": "Hetilap gyűjtő", "authors": "Szerzők", "branding_box": "Branding box", "column": "<PERSON><PERSON><PERSON>", "custom_built_page": "<PERSON><PERSON><PERSON><PERSON> oldal", "custom_url": "Egyéni URL", "dossier": "<PERSON><PERSON><PERSON><PERSON>", "dossier_collection": "Do<PERSON><PERSON><PERSON>", "dropdown": "<PERSON><PERSON> menü elem", "gallery": "Galéria", "gallery_collection": "Galéria gyűjtő", "grief_page": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "home_page": "Főoldal", "notebook_article": "Jegyzet", "full_article": "<PERSON><PERSON>s cikk", "tag_collection": "Címkék", "sponsorship": "Sz<PERSON><PERSON>or<PERSON><PERSON><PERSON>", "opinion_article": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "opinion_page": "Vélemény gyűjtő", "podcast": "Podcast", "podcast_collection": "Podcast gyűjtő", "program_recommendation": "Programajánló", "program_recommendation_collection": "Programajánló gyűjtő", "region": "<PERSON><PERSON><PERSON><PERSON>", "search_page": "<PERSON><PERSON><PERSON>al", "static_page": "Statikus oldal", "tunderszepek_page": "Tündérszépek főoldal", "video": "<PERSON><PERSON><PERSON>", "video_collection": "Videó g<PERSON>", "weather_page": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "competition_team": "Bajnokság csapat", "recipe": "Recept", "full_recipe": "<PERSON><PERSON><PERSON> recept", "twenty_four_hours": "24 óra", "nso_tv": "Nso tv", "push_notification_topic": "Push üzenet téma"}}, "contributors": "Elszámolási szerzők", "publicAuthors": "Nyilvános szerzők", "externalContributors": "Elszámolási szerzők", "externalPublicAuthors": "Nyilvános szerző", "coverImage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "customUrl": "Egyéni URL", "dataPrimary": {"excerpt": "<PERSON><PERSON><PERSON> lead", "title": "Cím"}, "delete_articles": "Törölhet cikket.", "delete_others_articles": "Mások cikkét törölheti.", "delete_published_articles": "Publikált cikkeet tud törölni.", "description": "Le<PERSON><PERSON><PERSON>", "dossier": "<PERSON><PERSON><PERSON><PERSON>", "edit_articles": "modosithat cikket.", "edit_others_articles": "Másik ciket tudja modositani.", "edit_published_articles": "Modosithat publikált cikket.", "isReviewable": "Sorozatvető", "editedVersion": {"dataPrimary": {"alternativeTitle": "Alternatív cím", "excerpt": "<PERSON><PERSON><PERSON> lead", "onlineExcerpt": "Online rövid lead", "preTitle": "Előcím", "preTitleColor": "Előcím <PERSON>", "subTitle": "Alcím", "title": "Cím", "breadcrumbTitle": "Bread<PERSON><PERSON>b cím"}, "dataSecondary": {"modifiedUrl": "Módosított url", "isGuaranteeType": "<PERSON><PERSON><PERSON><PERSON>", "magazineCover": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "podcastGuestAvatar": "Vendég a<PERSON>", "podcastGuestName": "Vendég neve", "podcastGuestNameTitle": "Vend<PERSON>g titulusa", "avCodes": "AV kódok", "avatar": "Szerző avatar kép", "ctLinkCodes": "CT link kódok", "editorComment": "Szerkesztői megjegyzés", "flash": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>", "hideThumbnailFromBody": "Cikkben kiemelt kép kikapcsolása", "lead": "Lead", "leadVideo": "Lead videó", "leadVideoPosition": "Lead vide<PERSON> pozici<PERSON>", "online": "Online", "podcastType": "Podcast tartalom", "interviewType": "<PERSON><PERSON><PERSON>", "print": "Print", "printLead": "Print lead", "printSubTitle": "Print felcím", "printTitle": "Print főcím", "publicAuthor": "Nyilvános szerző", "publicAuthorEmail": "Nyilvános szerző emailcím", "publicAuthorLocalization": "Nyilvános szerző lokalizáció", "readingLength": "Olvasási idő (perc)", "recommendedArticlesAvCodes": "Cikkajánló AV kódok", "recommendedOnlineTitle": "Javasolt online cím", "recommendedTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>í<PERSON>", "secondaryThumbnail": "Másodlagos kiemelt kép", "secondaryThumbnail43": "Másodlagos kiemelt kép (4:3)", "sendInNewsletters": "Hírlevelekbe küldés", "seoTitle": "SEO cím", "seoDescription": "SEO leírás", "seoMainKeyword": "<PERSON><PERSON> kulcsszó", "seoOtherKeywords": "Kulcsszavak", "seoLongTailKeywords": "Long-tail kulcsszavak", "seoCanonicalUrl": "Canonical URL (Minden esetben csak abszolút URL adható meg.)", "seoResearchSource": "SEO kutatás forrás", "seoCategorySelector": "SEO kategória", "seoRobotsMeta": "Robots meta", "settlement": "Település", "showInRss": "RSS-be mehet", "thumbnail": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "thumbnail169": "<PERSON><PERSON><PERSON><PERSON> (16:9)", "videoType": "<PERSON><PERSON><PERSON> tarta<PERSON>", "hasGallery": "Galériás tarta<PERSON>", "visibleDate": "Megjelení<PERSON><PERSON> d<PERSON>", "withoutAds": "Hirdetésmentes cikk", "isExceptionAdvertEnabled": "Roadblock 1 hirdetés kivételes megjelenítés", "facebookTitle": "Facebook og:title", "seoImage": "Facebook og:image"}}, "isMoreInfoLinkEnabled": "<PERSON>zer<PERSON>ne többet megtudni erről link megjelenjen?", "moreInfoLink": "Szeretne többet megtudni erről URL", "email": "E-mail", "endDate": "<PERSON><PERSON><PERSON>", "highlightedFrom": "<PERSON><PERSON><PERSON><PERSON> kez<PERSON>e", "highlightedTo": "Kiemelés vége", "endpoints": "Végpontok", "evaluationArticle": "Értékelő cikk", "externalDate": "<PERSON><PERSON><PERSON><PERSON> dátum", "externalId": "Azonosító", "firstName": "Keresztnév", "gold": "<PERSON><PERSON>", "headerColor": "<PERSON>j<PERSON>c sz<PERSON>", "headerImage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hirstartColumnName": "<PERSON><PERSON><PERSON><PERSON>", "iconActive": "Aktív ikon", "iconInactive": "Inaktív ikon", "image": "Indexkép", "important_article": "Fontos", "isActive": "Aktív", "isAdultAd": "Felnőtteknek", "isCorrect": "<PERSON><PERSON><PERSON>", "isForAdult": "Felnőtteknek", "isHidden": "<PERSON><PERSON><PERSON><PERSON>", "isHighlighted": "<PERSON><PERSON><PERSON><PERSON>", "isOnHeaderBar": "Fejléc címke szalagban szerepel", "isPinnedToHomePage": "Főoldalra kitűzés", "isPrint": "Print", "isPublic": "<PERSON><PERSON><PERSON>", "isPublicated": "Kiemelve", "isRecommended": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item": "elem", "label": {"type": "<PERSON><PERSON><PERSON>", "MultiVote": {"MultiVoteDetail": "Multi-szavazás"}, "Sport": {"CompetitionDetail": "Bajnokság", "CompetitionTeamDetail": "Csapat", "TabellaDetail": "<PERSON><PERSON>", "RaceWeekendDetail": "Sportág", "RaceCompetitionDetail": "Bajnokság", "RaceDetail": "Szakasz", "GroupTabellaDetail": "Csoport", "CompetitionWithPhasesDetail": "Bajnokság", "NonMotorsportCompetitionDetail": "Bajnokság", "ResultScopeDetail": "<PERSON><PERSON> t<PERSON>", "ScheduleDetail": "Mérkőzés", "CompetitionStraightEliminationDetail": "Bajnokság", "DailySchedulesDetail": "Bajnokság", "CompetitionPhaseDetail": "Csoport"}, "ArticleListWithTitle": {"TitleDetail": "Cikk lista címe"}, "Recipe": {"RecipeDetail": "Recept"}, "InfoBox": {"InfoBoxTitle": "Cím", "InfoBoxDescription": "Le<PERSON><PERSON><PERSON>"}, "ContentGroup": {"RossmannQuizDetail": "Kvíz", "QuizDetail": "Kvíz"}, "ContentPage": {"ArticleDetail": "Cikk"}, "Game": {"GameBlockDetail": "Nyereményj<PERSON><PERSON>"}, "Media": {"File": {"MediaFileDetail": "File"}, "Gallery": {"GalleryDetail": "Galéria", "GalleryHighlightedImage": "Galéria kie<PERSON>t képe"}, "Game": {"GameBlockDetail": "Jaték"}, "Video": {"VideoDetail": "<PERSON><PERSON><PERSON>"}}, "TenArticleRcm": {"TenArticleTitleDetail": "Cikkaján<PERSON><PERSON> c<PERSON>", "TenArticleLeadDetail": "<PERSON>ik<PERSON><PERSON><PERSON><PERSON><PERSON> lead", "TenArticleWithImageDetail": "<PERSON>épes <PERSON>"}, "TenOpinionArticleRcm": {"TenOpinionArticleTitleDetail": "Vélemény a<PERSON> c<PERSON>", "TenOpinionArticleLeadDetail": "<PERSON><PERSON><PERSON><PERSON><PERSON> lead", "TenOpinionArticleWithImageDetail": "<PERSON>épes <PERSON>"}, "TippmixAdvert": {"TippmixAdvertDetail": "Tippmix Pro hirdetés kiválasztása"}, "Subsequent": {"Dossier": {"DossierDetail": "<PERSON><PERSON><PERSON><PERSON>"}, "DossierNewsFeed": {"DossierNewsFeedDetail": "Hírfolyam"}}, "Voting": {"VotingDetail": "Szavazás"}, "DoubleArticleRecommendation": {"DoubleTitleDetail": "Double aj<PERSON><PERSON>ó cím", "DoubleLeadDetail": "<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> lead"}, "DoubleArticleRecommendationOptional": {"DoubleTitleDetail": "Double aj<PERSON><PERSON>ó cím", "DoubleLeadDetail": "<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> lead"}, "Detail": {"Today": "<PERSON> nap", "DatePicker": "<PERSON><PERSON><PERSON>", "Reference": {"ScheduleCollection": "Mérkőzés", "Article": "Cikk", "ArticleOptional": "Cikkajánló", "OpinionArticle": "Vélemény cikk", "OpinionArticleOptional": "Vélemény cikk"}}, "Recommendation": {"ExternalRecommendationDescriptionDetail": "Le<PERSON><PERSON><PERSON>", "ExternalRecommendationUrlDetail": "URL", "ExternalRecommendationImageDetail": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>"}, "Test": {"RelatedArticle": {"RelatedArticleDetail": "Ka<PERSON><PERSON>ol<PERSON><PERSON><PERSON> cikk"}}, "Promotion": {"PromotionDetail": "Promóció"}, "externalDate": "<PERSON><PERSON><PERSON><PERSON> dátum", "externalId": "Azonosító", "isActive": "Aktív", "isForAdult": "Felnőtteknek", "isHidden": "<PERSON><PERSON><PERSON><PERSON>", "slug": "URL", "title": "Cím", "style": "<PERSON><PERSON><PERSON>", "vodData": {"category": "<PERSON><PERSON><PERSON>", "channel": "Csatorn<PERSON>", "description": "Le<PERSON><PERSON><PERSON>", "length": "Hossz", "onAirTime": "Adásidő", "tag": "<PERSON><PERSON><PERSON><PERSON>", "timeWindow": "<PERSON><PERSON><PERSON>"}, "wysiwyg": "Bekezdés", "rating": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SecretDaysCalendar": {"SecretDaysCalendarDetail": "Kalendárium"}}, "language": "Nyelvesítés", "lastName": "Vezetéknév", "latitude": "Szélesség", "lead": "Bevezető", "lead_article": "Vezető cikk", "length": "Hossz", "list_only_by_me": "Csak saját cikket tudja listázni.", "list_only_me_in_statistic": "User sztatisztik<PERSON><PERSON> csak saját mag<PERSON>t l<PERSON>.", "logoImage": "Logó", "longitude": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mayHiddenOnLayout": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "emphasizeOnArticleCard": "Kiemelés cikk kártyán", "medium": "Médium", "name": "Név", "normal_article": "<PERSON><PERSON><PERSON><PERSON>", "pageType": "<PERSON>al típus", "parent": "Szülő kategória", "phoneNumber": "Telefonszám", "plainPassword": "<PERSON><PERSON>", "portal": "<PERSON><PERSON><PERSON> oldal", "portalUsers": "Portal oldal", "price": "<PERSON><PERSON>", "primaryColumn": "Főrovat", "printStatusAccepted3": "Modosithatja a cikk print status 'elfogadott' checkbox-ot.", "printStatusEdited1": "Modosithatja a cikk print status 'szerkersztet' checkbox-ot.", "printStatusFrackedUp4": "Modosithatja a cikk print status 'tördelt' checkbox-ot.", "printStatusRead2": "Modosithatja a cikk print status 'olvasot' checkbox-ot.", "printStatusReady0": "Modosithatja a cikk print status 'kész' checkbox-ot.", "printStatusReloaded5": "Modosithatja a cikk print status 'visszatöltött' checkbox-ot.", "priority": "Prioritás", "program": "program", "programLocation": "Program Helyszín", "programType": "Program típus", "programTypes": "Program típusok", "publicAuthor": "Nyilvános szerző", "publicAuthorDescription": "Nyilvános szerző leírás", "publicAuthorName": "Nyilvános szerző név", "publishDate": "Publikálás <PERSON>", "question": "<PERSON><PERSON><PERSON><PERSON>", "questions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ratingFrom": "Értékt<PERSON>l", "ratingTo": "Értékig", "read_others_articles": "Mások cikket eltudja olvasni.", "recommendedFrom": "<PERSON><PERSON><PERSON><PERSON> kez<PERSON>e", "recommendedTill": "Kiemelés vége", "record_filter": "Szűrő ", "regions": "<PERSON><PERSON><PERSON><PERSON>", "relatedKey": "<PERSON><PERSON><PERSON><PERSON> oldal", "relatedType": "<PERSON><PERSON><PERSON><PERSON> típus", "roleGroupUsers": "Szerepkör csoport", "roles": "<PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>", "secondaryColumns": "<PERSON><PERSON><PERSON><PERSON>", "seoTitle": "SEO cím", "seoDescription": "SEO leírás", "seoCanonicalUrl": "Canonical URL", "seoMetaToolRobotsContents": "Robots <PERSON><PERSON>", "send_in_newsletter_access": "<PERSON>ik<PERSON> a 'send in newsletter' checkboxot modosithatja.", "showInNewsAggregators": "Megjelenítés híraggregátorokban", "showInRss": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ben", "slug": "URL", "sponsorship": "Sz<PERSON><PERSON>or<PERSON><PERSON><PERSON>", "sports": "Sportág", "sport": {"schedule": {"result_scope": {"player": "<PERSON><PERSON><PERSON><PERSON> tabella", "team": "Csapat tabella"}}}, "Sport": {"CompetitionSchedules": "Foci Eb 2024 Ered<PERSON><PERSON>ek", "MotorsportWorldCup": "VB állás", "Tabella": "<PERSON><PERSON>", "CompetitionTeam": "Csa<PERSON><PERSON> ad<PERSON>", "RaceWeekend": "Versenyhétvége", "GroupTabella": "Csoportállások", "Schedule": "Mérkőzések", "CompetitionStraightElimination": "<PERSON><PERSON><PERSON><PERSON>", "DailySchedules": "Napi program", "CompetitionPhaseStat": "Csapatok"}, "ShortCode": {"AutoMotor": {"AutoMotor": "Autó-Motor"}, "AnchorToComments": {"Item": "Szóljon hozzá a cikkhez"}, "Disclaimer": {"Item": "T<PERSON>j<PERSON><PERSON><PERSON><PERSON><PERSON> (magánvélemény)"}, "GraphData": {"Item": "Grafikon"}}, "startDate": "Kezdő dátum", "articleStatsTypes": "Statisztikai típus", "status": {"contentType": "Tartalom származása", "foundationTagSelect": "Alapkő tartalom címke", "aniCode": "ANI követő kód", "embedPrAdvert": "PR embed hirde<PERSON><PERSON>", "articleSource": "<PERSON><PERSON><PERSON>", "breakingNewsFromDate": "<PERSON><PERSON><PERSON><PERSON> kez<PERSON>e", "breakingNewsToDate": "Kiemelés vége", "hiddenOnLayoutFrom": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "hiddenOnLayoutUntil": "Elrejtés vége", "isAdultsOnly": "18+ tartalom", "showInNewsAggregators": "Híraggregátorba me<PERSON>t", "isAheadInLayoutDossier": "Dossziéban kie<PERSON>és", "isBreakingNews": "Rendkív<PERSON><PERSON> k<PERSON>", "isEditorMessageable": "Üzenetküldés a szerkesztőnek", "isHiddenOnLayout": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isHiddenOnLayoutDossier": "Címlapon dosszié boxban elrejtés", "isRecommendedToHomepage": "Címlaposnak küldés (Címlapi ajánlódobozba elhelyezés)", "isShortNewsType": "<PERSON><PERSON><PERSON>", "isShowInStatistics": "Megjelenik a statisztikában", "isOlympic": "Olimpiás tartalom", "journalIssue": "Hetilap", "messageToHomepageEditor": "Üzenet a címlaposnak", "notebook": "Jegyzet", "opinionType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isFastNewsType": "<PERSON><PERSON><PERSON> h<PERSON>", "printPageNumber": "Print oldal", "printPublishedDate": "Print megjelenés", "printStatusAccepted3": "J<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "printStatusActive": "Aktív", "printStatusEditDoneReleaseable": "Szerkesztés kész, kiengedhető", "printStatusEdited1": "Szerkesztett", "printStatusFrackedUp4": "<PERSON><PERSON><PERSON><PERSON>", "printStatusAccepted3AsApproved": "J<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "printStatusReloaded5AsRecharged": "Visszatöltött", "printStatusAccepted3AsPublished": "Publikált", "printStatusReloaded5AsFrontPaged": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "printStatusRead2": "<PERSON><PERSON><PERSON><PERSON>", "printStatusReady0": "Elkészült", "printStatusReloaded5": "Visszatöltött", "printStatusUnderWriting": "<PERSON><PERSON><PERSON>", "printStatusWriteDoneUnderEditing": "<PERSON><PERSON><PERSON>, szer<PERSON>z<PERSON><PERSON> alatt", "isPrioritySortRss": "RSS Prioritás", "user": "Szerző", "materialType": "<PERSON><PERSON><PERSON><PERSON>", "tourGuide": "Turnusvezető", "articleMedium": "Médium", "isProtectedContent": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON>", "isHighlightInTape": "Kiemelés a szalagba", "highlightInTapeFromDate": "Kiemelés a szalagba kezdete", "highlightInTapeToDate": "Kiemelés a szalagba vége", "accepted": "Elfogadottak", "deleted": "T<PERSON><PERSON><PERSON>ltek", "waiting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rejected": "Elutasítottak", "foundationTagAutoLink": "Alapkő címkék automatikus linkelése", "glossaryAutoLink": "Szószedet/tudástár/sportlexikon automatikus linkelése", "minuteToMinuteTimeHidden": "Percről percre bejegyzés idejének elrejtése"}, "tourGuide": "Turnusvezető", "tags": "<PERSON><PERSON><PERSON><PERSON>", "selections": "Válogatás", "targetBlank": "Megnyitás új ablakban", "text": "Szöveg", "topics": "Témák", "title": "Cím", "partnerDescription": "Megjegyzés", "titleColor": "<PERSON><PERSON><PERSON>", "mainColor": "Háttérsz<PERSON>", "url": "URL", "highlightedFromDate": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> d<PERSON>", "highlightedToDate": "Kiemelés végének dátuma", "createdAt": "Létrehozva", "phone": "Telefon", "city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "comment": "Megjegyzés", "country": "<PERSON><PERSON>", "countryCode": "Országkó<PERSON>", "houseNumber": "Házszám", "placeId": "<PERSON><PERSON>", "postalCode": "Irányí<PERSON>", "street": "Utca", "institutionCategory": "Intézmény kategória", "openingHours": "Nyitvatartási idő", "openingHoursSeasonal": "Szezonális nyitvatartási idő", "userColumns": "Rova<PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "userName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "validFromDate": "Kezdet", "validToDate": "Befejezés", "videaUrl": "Video url", "vodData": {"category": "<PERSON><PERSON><PERSON>", "channel": "Csatorn<PERSON>", "description": "Le<PERSON><PERSON><PERSON>", "length": "Hossz", "onAirTime": "Adásidő", "tag": "<PERSON><PERSON><PERSON><PERSON>", "timeWindow": "<PERSON><PERSON><PERSON>"}, "voteCount": "Szavazatok száma", "zonaId": "Zona ID", "fbIsPushNotificationOn": "<PERSON>ush értes<PERSON><PERSON><PERSON>", "fbNotificationTitle": "Értesítés címe", "fbNotificationBody": "Értesítés szövege", "fbIsCustomExpirationOn": "Időzített értesítés", "fbExpiration": "Értesítés <PERSON>", "DoubleArticleRecommendation": {"Double": "Double cikk a<PERSON>"}, "DoubleArticleRecommendationOptional": {"Double": "Double cikk a<PERSON>"}, "robotsTag": "ROBOTOK alapértelmezett vezérlése", "yearGrade": "É<PERSON><PERSON><PERSON><PERSON>", "pageNumber": "Lapszám", "pageNumberType": "<PERSON><PERSON><PERSON><PERSON><PERSON> tí<PERSON>", "textOfSpecialIssue": "Különszám szövege", "mainTitle": "Főcím", "mainText": "Főszöveg", "urlSlug": "URL slug", "pdfEmbedCode": "PDF beágyazott kód", "normal": "<PERSON><PERSON><PERSON><PERSON>", "special_issue": "Különszám", "merged": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "columnOrder": "<PERSON><PERSON><PERSON> sorrend", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "portals": "Portálok", "nickName": "Be<PERSON>név", "externalContributor": "Elszámolási szerző", "isShowInStatistics": "Megjelenik a statisztikában", "introduction": "Bemutatkozás", "emailAddress": "E-mail cím", "publicAuthorUsers": "Felhasználók", "publicAuthorM2M": "Nyilvános szerzők", "rank": "Titulus", "value": "<PERSON><PERSON><PERSON><PERSON>", "seoMetaTools": "SEO Meta URL-ek", "tiktok": "Tiktok", "facebook": "Facebook", "instagram": "Instagram", "isInner": "Belsős szerző", "isOpinionAuthor": "Vélemény szerző", "prTag": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "prActiveFromDate": "Pr időszak kezdete", "prActiveToDate": "Pr időszak vége", "publishStartDate": "Publikálás k<PERSON>det<PERSON> d<PERSON>", "publishEndDate": "Publikálás vég dátuma", "type": "<PERSON><PERSON><PERSON>", "itemEndImage": "Cikk végi kép", "popupImage": "<PERSON><PERSON> k<PERSON>p", "periodInDay": "<PERSON><PERSON>ódus (nap)", "digital": "<PERSON><PERSON><PERSON><PERSON>", "campaign": "Kampány", "dateEnd": "Kampány vége", "dateStart": "Kampány k<PERSON>e", "couponCode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hidden": "<PERSON><PERSON><PERSON><PERSON>", "mainCampaign": "Fő kampány", "renewEndDate": "Megújítás végdátuma", "renewNextPriceType": "<PERSON><PERSON> t<PERSON><PERSON> me<PERSON>", "isPaywalled": "Előfizetéses tartalom", "dbCache": {"commentsDisabled": "Hozzászólások letiltva", "likesAndDislikesDisabled": "Like és dislike letiltva", "isVidcast": "Vidcast tartalom"}, "isDefaultEmailCoupon": "Alapértelmezett email kupon kamp<PERSON>y", "subscriptionProducts": "Előfizetéses termékek", "subject": "<PERSON><PERSON><PERSON>", "timeNumber": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "event": "<PERSON><PERSON><PERSON><PERSON>", "timeUnit": "Időeltolás egysége", "EmailTemplate": {"EmailCoupon": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>"}, "partnerKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mailTo": "Címzett", "mailCc": "Másolat", "date": "<PERSON><PERSON><PERSON>", "uvRadiation": "UV Sugárzás", "sunshineHoursMin": "Napsütéses ór<PERSON> (min. érték)", "sunshineHoursMax": "Napsütéses ór<PERSON> (max. érték)", "newsFeed": "Hírfolyam", "newsFeedTitle": "Hírfolyam do<PERSON>z címe", "newsFeedDescription": "Hírfolyam doboz leírás<PERSON>", "fromPath": "Honnan (relatív URL)", "toPath": "Hová (relatív URL)", "isEnabledExtra": "Extra", "isEnabledFrontalSystem": "Fronthatás", "isEnabledNow": "<PERSON><PERSON><PERSON><PERSON>", "isEnabledPrediction": "Előrejelzés", "isEnabledTextPrediction": "Szöveges előrejelzés", "isEnabledTv": "TV", "state": "<PERSON><PERSON><PERSON><PERSON>", "silver": "<PERSON><PERSON><PERSON><PERSON>", "olympicsParticipant": "Olimpiai résztvevő ország", "manualGold": "<PERSON><PERSON><PERSON><PERSON>", "manualSilver": "<PERSON><PERSON><PERSON><PERSON>", "manualBronze": "<PERSON><PERSON><PERSON><PERSON>", "year": "<PERSON><PERSON>", "deviceWindDetection": "<PERSON><PERSON><PERSON><PERSON>", "deviceRainDetection": "Csapadék", "deviceIndoor": "Belső", "deviceOutdoor": "<PERSON><PERSON><PERSON><PERSON>", "internetProvider": "Internet szolgáltató", "zipCode": "Irányí<PERSON>", "maintainedAt": "Utolsó karbantartás ideje", "streetNumber": "Házszám", "streetName": "Közterület neve", "deviceExternalIdentifier": "Külső eszköz azonosítója", "gpsLat": "GPS koordináta (latitude)", "gpsLng": "GPS koordináta (longitude)", "preferredMaintenanceOnWeekday": "Preferált karbantartás hétköznapokon", "preferredMaintenanceOnWeekend": "Preferált karbantartás hétvégéken", "forecastPartnerName": "Partner neve", "forecastPartnerDescription": "Megjegyzés", "buzzword": "Hívószó", "maintenanceWeekendEarlyAfternoon": "Hétvégén (Kora délut<PERSON>)", "maintenanceWeekendLateAfternoon": "Hétvégén (Késő délután)", "maintenanceWeekendMorning": "Hétvégén (Délelőtt)", "maintenanceWeekdayLateAfternoon": "Hétköznap (Késő délután)", "maintenanceWeekdayEarlyAfternoon": "Hétköznap (Kora délután)", "maintenanceWeekdayMorning": "Hétköznap (Délelőtt)", "hasNetatmoStation": "Netatmo állomással rendelkezik", "mediaImage": "<PERSON><PERSON><PERSON>", "weightedPageViewsMultiplier": "Súlyozott oldalmegtekintési szorzó", "startMatch": "<PERSON><PERSON><PERSON> eleji ill<PERSON>", "endMatch": "Szó végi ill<PERSON>", "dossierOrder": "Sorszá<PERSON>", "isResultVisible": "Szavazás eredménye megtekinthető", "isMaestroAuthor": "Mindmegette maestro", "maestroTip": "Maestro tipp", "favoriteFoods": "Kedvenc ételek", "isBrandingBoxMaestroAuthor": "Branding box mindmegette maestro", "seo": {"rules": {"focusedKeywordIsTitleFirstWord": {"recipe": "Fő kulcsszó a név első szava"}, "focusedKeyword": {"recipe": "Fő kulcsszó szerepel a névben"}, "focusedKeywodInBodyFirstParagraph": {"recipe": "Recepttörzs el<PERSON>ő bekezdése tartalmaz fő kulcsszót"}, "videoAtArticleEnd": {"recipe": "Videó: A recept végén."}}}, "InfoBox": {"InfoBox": "Információs <PERSON>"}, "content_page": {"article": {"infobox": {"comment": "Információs doboz komment", "disclaimer": "Információs doboz lemon<PERSON>ás"}}}, "Recipe": {"Recipe": "Recept"}, "TippmixAdvert": {"TippmixAdvert": "Tippmix Pro hirdetés"}, "entityLog": {"mode": {"formSave": "Men<PERSON>s", "activate": "Aktiválás", "inactivate": "Inaktiválás", "delete": "Törlés", "restore": "Visszaállítás"}}, "recipeCategories": "Recept kategória", "dateOfBirth": "Születési d<PERSON>", "biography": "Életrajz", "dateOfDeath": "<PERSON><PERSON><PERSON>", "position": "Pozíció", "recipe": "Recept", "publicDate": "Publikálási d<PERSON>", "preTitle": "Előcím", "dossiers": "Dossziék", "photographer": "Fotós", "recommendedGallery0": "1. <PERSON><PERSON><PERSON><PERSON>", "recommendedGallery1": "2. <PERSON><PERSON><PERSON><PERSON>", "recommendedGallery2": "3. <PERSON><PERSON><PERSON><PERSON>", "recommendedGallery3": "4. <PERSON><PERSON><PERSON><PERSON>", "isAdult": "Felnőtt tartalom", "highlightedImage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "galleryMediaImages": "Gal<PERSON><PERSON>", "centralSendingPortals": "KPI portálok", "answerCount": "Maximum érték", "resultVisible": "<PERSON>red<PERSON><PERSON> megtekinthető", "active": "Aktív", "primaryCategory": "Főkategória", "otherCategories": "<PERSON><PERSON><PERSON><PERSON>", "studyAmount_1": "Ár 1 főre", "studyAmount_2": "Ár 2 főre", "studyAmount_3": "Ár 3 főre", "studyAmount_4": "Ár 4 főre", "isHighlightInTape": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "highlightInTapeFromDate": "<PERSON><PERSON><PERSON><PERSON> kez<PERSON>e", "highlightInTapeToDate": "Kiemelés vége", "featuredImage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "experience": "<PERSON><PERSON><PERSON><PERSON> neve", "startOfExperienceEvent": "Alkalom napja és időpontja", "isComingSoon": "<PERSON><PERSON><PERSON>", "place": "Alkalom helyszíne", "maxNumberOfSeats": "Férőhelyek száma", "duration": "Időtar<PERSON>", "hostAsPublicAuthor": "Házigazda", "fontColor": "Betűszín", "highlightedColor": "Háttérsz<PERSON>", "slogan": "Szlogen", "logoWidth": "Logo szélesség (px)", "logoHeight": "<PERSON><PERSON> (px)", "order": "<PERSON><PERSON><PERSON>", "SimplePlay": {"Status": {"INPAYMENT": "<PERSON><PERSON><PERSON><PERSON>", "PAID": "Fizetve", "FAILED_PAYMENT": "Sikertelen fizetés", "FAILED": "Sikertelen", "DELETED": "Törölve", "TIMEOUT": "Időtúllépés"}}, "notifier": {"form": {"unavailable_publish_period": "Publikálási periódus nem elérhető!"}}, "isDeleted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "badge": "Szöveges badge", "Promotion.Promotion": "Promóció", "quizCategories": "Kv<PERSON>z ka<PERSON>gó<PERSON>", "quizCategoryPageUrl": "További kvízek URL (Csak abszolút URL adható meg.)", "sponsorshipForHeader": "Fő szponzor fejlécben", "sponsorshipForFooter": "Fő szponzor láblécben", "backgroundImage": "<PERSON><PERSON><PERSON><PERSON><PERSON> h<PERSON>", "numberOfDays": "Napok száma", "colorOfDays": "Napok nevének betűszíne", "layerImageOfDays": "Napok layer háttere", "dateOfDay": "<PERSON><PERSON>", "daysOrder": "<PERSON><PERSON><PERSON>", "visibilityStart": "Láthatós<PERSON>g kezdete", "visibilityEnd": "Láthatóság vége", "openableStart": "Nyithatóság kezdete", "openableEnd": "Nyithatóság vége", "backgroundBeforeOpenImage": "<PERSON><PERSON> h<PERSON><PERSON><PERSON> kin<PERSON>", "backgroundAfterOpenImage": "<PERSON><PERSON> h<PERSON><PERSON>e kinyit<PERSON> után", "sponsorshipHeader": "Nap szponzora fejlécbe", "sponsorshipFooter": "Nap szponzora láblécbe", "relatedThematicTags": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> cí<PERSON>", "SecretDaysCalendar.SecretDaysCalendar": "Kalendárium", "logoUrl": "Logo URL", "headerBackgroundColor": "<PERSON><PERSON><PERSON><PERSON>", "headerBackgroundImage": "<PERSON><PERSON><PERSON><PERSON>", "boxName": "Doboz neve", "boxNameColor": "Doboz név betűszín", "boxBackgroundImage": "Doboz háttérkép", "texts": "Le<PERSON><PERSON><PERSON><PERSON>", "articles": "Cikkek", "footerText": "Lábléc szöveg", "footerTextColor": "Lábléc szöveg betűszín", "footerBackgroundColor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "footerBackgroundImage": "<PERSON><PERSON><PERSON><PERSON><PERSON>rk<PERSON>", "isAdTextVisible": "Hirde<PERSON>s felirat jelen<PERSON>n meg", "isTranslated": "Nyelvesített"}