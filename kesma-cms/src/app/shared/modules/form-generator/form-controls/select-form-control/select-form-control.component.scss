@use 'sass:color';

.color {
  display: inline-block;
  width: 30px;
  height: 15px;
  padding: 0px;
  vertical-align: middle;
}

.select-form-control-container {
  margin-bottom: 14px;
  min-height: 40px;

  nz-select,
  nz-tree-select {
    width: 100%;
  }
}

nz-select::ng-deep {
  nz-select-clear {
    // right: -20px;
  }
}

.select-all-container {
  border-bottom: 1px solid rgb(233, 233, 233);
  margin-top: 10px;
}

.type-list-wrapper {
  // padding: 10px 0 15px 4px;
  border: 1px solid #ebebeb;
  border-radius: 10px;
  padding: 10px;
  margin: 10px 0px 30px 0px;
}

.type-list-item {
  padding: 5px 0;
  display: flex;
  justify-content: space-between;
}

.type-input {
  position: absolute;
  right: 0px;
  top: 0;
  display: inline-block;
}

.add-me-button {
  height: 32px;
  margin-bottom: 10px;
  padding: 4.5px 15px;
  background-color: #7dbacd;
  border-color: #7dbacd;

  &:hover {
    background-color: color.adjust(#7dbacd, $lightness: 10%);
    border-color: color.adjust(#7dbacd, $lightness: 10%);
  }
}

.type-value-delete {
  height: 32px;
  margin-left: 5px;
  padding: 4.5px 15px;
  background-color: #7dbacd;
  border-color: #7dbacd;

  &:hover {
    background-color: color.adjust(#7dbacd, $lightness: 10%);
    border-color: color.adjust(#7dbacd, $lightness: 10%);
  }
}

.add-user {
  text-decoration: underline;
  padding-left: calc(100% - 110px);
}

.type-select-wrapper {
  margin-bottom: 20px;
}

.add-all-container {
  margin-top: 10px;
}

.select-row {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;

  button {
    margin-left: 10px;
  }
  nz-select:has(+ .explicit-create-button) {
    max-width: calc(100% - 40px);
  }
}

::ng-deep {
  nz-select-top-control {
    width: 100%;
  }
}

.custom-option {
  &.indent {
    padding-left: 8px;
  }
}

.explicit-create-button {
  flex: 0 0 auto;
}
