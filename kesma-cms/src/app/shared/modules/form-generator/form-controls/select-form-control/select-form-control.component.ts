import { Component, ElementRef, Injector, Input, OnInit, Renderer2, TemplateRef, ViewChild } from '@angular/core';
import { BaseFormControlComponent } from '../base-form-control.component';
import { ReqService } from '@external/http';
import { catchError, debounceTime, filter, map, skip, takeUntil, tap } from 'rxjs/operators';
import { fromEvent, Observable, of, Subject, throwError } from 'rxjs';
import { ComponentFieldDataDependency, ContentResponse, InputFieldContent } from 'src/app/core/api.definitons';
import { IHttpOptions } from 'src/app/core/core.definitions';
import { HttpErrorResponse, HttpParams } from '@angular/common/http';
import { FormControlService } from '../../services/form-control.service';
import { NzTreeNode } from 'ng-zorro-antd/tree';
import { ApiService } from 'src/app/core/services/api.service';
import { SharedService } from 'src/app/shared/services/shared.service';
import { UtilsService } from '@external/utils';
import { Limitable } from '@shared/definitions/shared.definitions';
import { FormControlAdapterService } from '../../../form-generator2/services/form-control-adapter.service';
import { PercentageService } from '../../../../../core/services/shared/percentage.service';
import { PortalConfigService } from '@shared/services/portal-config.service';
import { TranslateService } from '@ngx-translate/core';
import { PortalConfigSetting } from '@shared/definitions/portal-config';
import { isArray } from 'lodash';
import { ActivatedRoute } from '@angular/router';

const COUNT_LIMIT = 10;
@Component({
  selector: 'app-select-form-control-old',
  templateUrl: 'select-form-control.component.html',
  styleUrls: ['./select-form-control.component.scss'],
  standalone: false,
})
export class SelectFormControlComponent extends BaseFormControlComponent implements OnInit {
  @ViewChild('overrideFormControlsModal') private overrideFormControlsModal: TemplateRef<any>;

  @Input() public multiple: boolean;
  @Input() public valueProperty: string;
  @Input() public displayProperty: string;
  @Input() public sourceUrl: string;
  @Input() public mode: 'select' | 'treeSelect' | 'typeSelect' | 'simpleSelect';

  public get hasValue() {
    return this.multiple ? this.state.data.value && this.state.data.value.length : this.state.data.value;
  }

  public source: any[];
  public dependency: ComponentFieldDataDependency;
  public loading: boolean;
  public overriddenFormControls: any[];
  public allChecked = false;
  public indeterminate = false;
  public treeNodes: NzTreeNode[] = [];
  public treeSelectValues: string[];
  public filterable: boolean;
  public limitable: Limitable;
  public createUrl: string;
  public filterName: string;
  public formInfo: any;
  public translateSource: boolean;
  public isColor: boolean;
  public searchTermToCreate: string = '';
  public customDisplayProperty?: string;

  public typeSelectValues: any[]; // typeSelect select részének értéke
  public typeValues: any[] = []; // typeSelect type részének értéke

  private searchChangeSubject: Subject<string> = new Subject<string>();
  public searchChange$: Observable<string> = this.searchChangeSubject.asObservable();
  private typedValue: string;
  private options: IHttpOptions;
  private isInit = true;
  public hasAddAll = false;
  public isMoreOptionsLoading = false;
  private optionsLength = 0;
  private _isFocused = false;

  public compareWithFunction(item1: any, item2: any) {
    if (item1 && item2) {
      if (item1.id && item2.id) {
        return item1.id === item2.id;
      }
      if (item1.type && item2.type) {
        return item1.type === item2.type;
      }
      if (item1.value && item2.value) {
        return item1.value === item2.value;
      }
      if (item1.color && item2.color) {
        return item1.color === item2.color;
      }
    }
    return item1 === item2;
  }

  constructor(
    private reqService: ReqService,
    protected formControlService: FormControlService,
    public elementRef: ElementRef,
    protected injector: Injector,
    private apiService: ApiService,
    private sharedService: SharedService,
    private utilsService: UtilsService,
    protected renderer: Renderer2,
    private formControlAdapterService: FormControlAdapterService,
    private percentagesService: PercentageService,
    private portalConfigService: PortalConfigService,
    private translateService: TranslateService,
    private readonly activatedRoute: ActivatedRoute
  ) {
    super(formControlService, elementRef, injector, renderer);
  }

  ngOnInit() {
    this.source = [];
    this.mode = this.mode || (this.inputField.inputInfo.treeSelect ? 'treeSelect' : 'select');
    this.mode = this.inputField.inputInfo.typeSelect ? 'typeSelect' : this.mode;
    this.mode = this.inputField.inputInfo.simpleSelect ? 'simpleSelect' : this.mode;
    this.createUrl = this.inputField.inputInfo && this.inputField.inputInfo.createUrl;
    this.translateSource = this.inputField.inputInfo && this.inputField.inputInfo.translateSource;
    this.multiple = this.multiple || (this.inputField && this.inputField.multiple);
    this.hasAddAll = this.multiple && this.inputField.inputInfo && this.inputField.inputInfo.addAllButton;
    this.valueProperty = this.valueProperty || (this.inputField && this.inputField.referenceSource.selectKeyProperty) || 'id';
    this.displayProperty = this.displayProperty || (this.inputField && this.inputField.referenceSource.selectDisplayProperty);
    this.isColor = this.inputField.inputInfo.isColor ? this.inputField.inputInfo.isColor : false;
    this.sourceUrl = this.sourceUrl || (this.inputField && this.inputField.referenceSource.url);
    this.dependency = this.inputField && this.inputField.dependency;

    // This can be used for display an object property if backend don't send a referenceSource.
    this.customDisplayProperty = this.activatedRoute.snapshot.data?.['customSelectDisplayProperty'];

    if (!this.dependency) {
      const options: IHttpOptions = {
        params: {
          rowFrom_limit: '0',
          rowCount_limit: COUNT_LIMIT.toString(),
        },
      };
      this.setSource(true, options);
      this.state$
        .pipe(
          takeUntil(this.componentDestroyed$),
          filter((formControlState) => this.formControlService.isInTriggeredBy(['refreshSourceData'], formControlState.meta.lastTriggeredBy))
        )
        .subscribe((formControlState) => {
          if (formControlState?.data?.sourceUrl) {
            this.sourceUrl = formControlState.data.sourceUrl;
            this.setSource(false, options);
          }
          this.fillSourceWithControlValue();
        });
    } else {
      this.state$
        .pipe(
          takeUntil(this.componentDestroyed$),
          filter((formControlState) => this.formControlService.isInTriggeredBy(['refreshSourceData'], formControlState.meta.lastTriggeredBy))
        )
        .subscribe((formControlState) => {
          const queryParamKey = this.dependency.queryParamKey;
          const selectKeyProperty = this.dependency.selectKeyProperty;
          const queryParamValue = formControlState.data.refreshSourceData && formControlState.data.refreshSourceData[selectKeyProperty];
          let options;
          if (Array.isArray(queryParamValue)) {
            queryParamValue.forEach((queryParam) => {
              options = this.toggleParams({ [queryParamKey]: queryParam });
            });
          } else {
            options = this.toggleParams({ [queryParamKey]: queryParamValue });
          }
          options.params = {
            ...options.params,
            rowFrom_limit: '0',
            rowCount_limit: COUNT_LIMIT.toString(),
          };
          this.setSource(this.isInit, options);
          this.isInit = false;
        });
    }

    this.setupSearchChangeListener();
  }

  onFormControlClicked() {
    if (!this.limitable) {
      return;
    }

    let dropdownWithScrollbar = null;
    switch (this.mode) {
      case 'select':
        dropdownWithScrollbar = document.querySelector('.ant-select-dropdown .cdk-virtual-scroll-orientation-vertical');
        break;
      default:
        dropdownWithScrollbar = document.getElementsByClassName('ant-select-dropdown')[0];
        break;
    }

    if (dropdownWithScrollbar) {
      fromEvent(dropdownWithScrollbar, 'scroll')
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(() => {
          if (dropdownWithScrollbar.scrollTop + dropdownWithScrollbar.clientHeight >= dropdownWithScrollbar.scrollHeight) {
            console.log('reached end');
            const rowFrom = +this.options.params['rowFrom_limit'] + COUNT_LIMIT;
            const options: IHttpOptions = {
              params: {
                ...this.options.params,
                rowFrom_limit: rowFrom.toString(),
                rowCount_limit: COUNT_LIMIT.toString(),
              },
            };
            this.setSource(false, options);
          }
        });
    }
  }

  loadMoreOptions() {
    if (!this.limitable || this.isMoreOptionsLoading || this.limitable.rowAllCount <= this.optionsLength) {
      return;
    }
    const rowFrom = this.source.length;
    const options: IHttpOptions = {
      params: {
        ...this.options.params,
        rowFrom_limit: rowFrom.toString(),
        rowCount_limit: COUNT_LIMIT.toString(),
      },
    };
    this.isMoreOptionsLoading = true;
    this.formControlAdapterService.fetchSource(this.sourceUrl, options.params).subscribe(
      (data) => {
        // Filter options, so they don't get added to options list multiple times
        const filteredArray = this.filterOptions(data, this.source);
        this.optionsLength = this.source.length + data.length;
        this.source = [...this.source, ...filteredArray];
        this.isMoreOptionsLoading = false;
      },
      () => {
        this.isMoreOptionsLoading = false;
      }
    );
  }

  /*filterOptions(searchValue: string, shouldSetSelectedValue: boolean = true) {
    if (this.dependency && !searchValue) {
      return of(null);
    } else {
      this.options = this.toggleFilter(searchValue);
      // return this.getSource(shouldSetSelectedValue);
    }
  }*/

  private async setSource(isInit: boolean, options?: IHttpOptions, isSearch?: boolean) {
    if (isInit) {
      const { initialValue } = this.state.data;
      this.setState({ value: initialValue });
    }
    this.options = options;
    if (this.mode === 'typeSelect') {
      this.options.params = {
        ...(this.options.params || {}),
        rowCount_limit: '20000',
      };
    }
    if (this.hasAddAll === true) {
      this.options.params = {
        ...(this.options.params || {}),
        rowCount_limit: '20000',
      };
    }
    const source = await this.getSource(options);
    this.source = source;
    this.optionsLength = this.source?.length;
    if (!isSearch) {
      this.fillSourceWithControlValue();
    }

    /*this.source = this.limitable
      ? this.source.concat(source)
      : source;*/

    if (this.mode === 'treeSelect') {
      this.treeNodes = this.getTreeNodes();
      const value = this.state.data.value;
      if (this.multiple) {
        this.treeSelectValues = value && value.map((item) => item.id);
      } else {
        this.treeSelectValues = value && value.id;
      }
    }
    if (this.mode === 'typeSelect') {
      const value = this.state.data.value;
      if (this.multiple) {
        this.typeSelectValues =
          value &&
          value.map((item) => ({
            id: item.id,
            name: item.fullName,
          }));
        value.sort((a, b) => (a.name < b.name ? -1 : a.name > b.name ? 1 : 0));
        this.typeValues = value;
      } else {
        this.typeSelectValues = [
          {
            id: value.id,
            name: value.fullName,
          },
        ];
        this.typeValues = [value];
      }
    }
  }

  private fillSourceWithControlValue() {
    if (!this.formControl || !this.formControl.value) {
      return;
    }

    if (this.dependency) {
      if (Array.isArray(this.formControl.value)) {
        this.formControl.setValue(
          this.formControl.value.filter((selectedValue) => this.source?.find((src) => src[this.valueProperty] === selectedValue[this.valueProperty]))
        );
      } else {
        const source = this.source?.find((src) => src[this.valueProperty] === this.formControl.value[this.valueProperty]);
        if (!source) {
          // Üres a select, ha nincs benne az első 20 találatban a value.
          this.source?.unshift(this.formControl?.value);
        }
        this.formControl.setValue(source || this.formControl?.value);
      }
    } else {
      if (Array.isArray(this.formControl.value)) {
        const missingValues = this.formControl.value.filter(
          (selectedValue) => !this.source?.find((src) => src[this.valueProperty] === selectedValue[this.valueProperty])
        );
        this.source = this.source?.concat(missingValues);
      } else {
        const isvalueMissing = this.source?.find((src) => src[this.valueProperty] === this.formControl.value[this.valueProperty]) === undefined;
        if (isvalueMissing) {
          this.source?.unshift(this.formControl.value);
        }
      }
    }
  }

  private getSource(options?: IHttpOptions): Promise<any[]> {
    if (!this.sourceUrl && this.inputField.choices) {
      return Promise.resolve(this.inputField.choices);
    }
    // const isRequestCachable = !(['contentPage', 'relatedKey'].includes(inputField.key));
    // const headers = new HttpHeaders({ 'X-Cachable':  isRequestCachable ? '1' : '0' });
    this.loading = true;
    return this.reqService
      .get(this.sourceUrl, { ...this.options, ...options })
      .pipe(
        catchError((err) => {
          this.loading = false;
          return of([]);
        }),
        tap((res) => {
          // BE üres []-t küld vissza, ha nem létezik, egyébként pedig objectet
          this.filterable = res.meta?.filterable && !Array.isArray(res.meta.filterable) && Object.keys(res.meta.filterable).includes('global_filter');
          this.limitable = res.meta?.limitable;
          this.filterName = this.filterable ? 'global_filter' : null; // Object.keys(res.meta.filterable)[0];
        }),
        map((res) => (res?.hasOwnProperty('data') ? res.data : res)),
        tap(() => (this.loading = false))
      )
      .toPromise();
  }

  private setupSearchChangeListener() {
    this.searchChange$.pipe(takeUntil(this.componentDestroyed$), skip(1), debounceTime(500)).subscribe((searchValue) => {
      this.typedValue = searchValue;
      if (this.filterName) {
        const options = this.toggleParams({ [this.filterName]: searchValue });
        this.setSource(false, options, true);
      }
    });
  }

  private toggleParams(params: HttpParams | { [param: string]: string | string[] }): IHttpOptions {
    const options: IHttpOptions = this.options || {};
    const currentParams = options.params || {};
    const newParams = Object.assign({}, currentParams, params);

    for (const key in newParams) {
      if (!newParams[key]) {
        delete newParams[key];
      }
    }

    options.params = newParams;
    return options;
  }

  private getTreeNodes(): NzTreeNode[] {
    const source = this.source || [];
    let parentNodes = source.filter((item) => !item.parentId).map((item) => ({ ...item, key: item.id, children: [] }));

    if (!parentNodes || parentNodes === null) {
      parentNodes = [];
    }

    source
      .filter((item) => item.parentId)
      .forEach((childItem) => {
        const parentId = childItem.parentId;
        const parent = parentNodes.find((p) => p.id === parentId);
        if (parent) {
          parent.children.push({ ...childItem, key: childItem.id, isLeaf: true });
        } else {
          parentNodes.push({ ...childItem, key: childItem.id, isLeaf: true });
        }
      });
    const nodes: NzTreeNode[] = parentNodes.map((parent) => {
      if (!parent?.children?.length) {
        return {
          ...parent,
          isLeaf: true,
        };
      }
      return parent;
    });
    return nodes;
  }

  keydownInDo(event) {
    if (this.createUrl && event.keyCode === 13 && this.source.length === 0) {
      this.create();
    }
  }

  create() {
    const notSuchOption = this.source.findIndex((item) => item[this.displayProperty] === this.typedValue) === -1;
    if (notSuchOption) {
      this.reqService.get(this.createUrl).subscribe((res) => {
        this.formInfo = res;
        this.formInfo.data.forEach((element) => {
          this.overrideValueToExplicitCreate(element, this.createUrl);
        });
        this.reqService.post(this.createUrl, this.formInfo).subscribe(
          (result) => {
            const options: IHttpOptions = {
              params: {
                [this.filterName]: this.typedValue,
                rowFrom_limit: '0',
                rowCount_limit: COUNT_LIMIT.toString(),
              },
            };
            this.setSource(false, options);
          },
          (err) => {
            this.handleErrors(err);
          }
        );
      });
    }
  }

  /**
   * Create a new item by clicking the add button if there is a createUrl property supplied
   */
  explicitCreate(): void {
    const notSuchOption = this.source.findIndex((item) => item[this.displayProperty] === this.searchTermToCreate) === -1;
    if (notSuchOption) {
      //Get the form from the BE to prepare it for submission.
      this.reqService.get(this.createUrl).subscribe((res) => {
        this.formInfo = res;
        this.formInfo.data.forEach((element) => {
          this.overrideValueToExplicitCreate(element, this.createUrl);
        });
        //Post the created form to the BE. The response will contain the created element's details along with it's ID!
        this.reqService.post(this.createUrl, this.formInfo).subscribe(
          async (result) => {
            const options: IHttpOptions = {
              params: {
                [this.filterName]: this.searchTermToCreate,
                rowFrom_limit: '0',
                rowCount_limit: COUNT_LIMIT.toString(),
              },
            };
            //Set filtering to the source to get the newly added item's details.
            this.setSource(false, options);

            //Get the created item from the BE as the POST response does not contain the ID.
            const createdResponse = await this.getSource(options);
            if (createdResponse?.length > 0) {
              const createdItem = createdResponse[0];

              //Create a new state for the input with a newly added item as an additional value.
              const newState = {
                ...this.state.data,
                value: this.state.meta.inputFieldIndentifier === 'ingredient' ? createdItem : [...(this.state.data.value ?? []), createdItem],
              };
              this.setState(newState);
              //Set the source filtering to empty to refresh the input.
              this.setSource(false);
            }
          },
          (err) => {
            this.handleErrors(err);
          }
        );
      });
    }
  }

  /**
   * This is the callback for the create button to use for the disabled property.
   * It checks whether the input has at least a character and it does not already exists.
   */
  public checkCreateButtonDisabled(): boolean {
    if (!this._isFocused) {
      return true;
    }
    if (this.searchTermToCreate.length <= 1) {
      //Prevent 1 char tags or if the term is empty
      return true;
    }
    return !!this.source.find((item) => item[this.displayProperty].toLowerCase() === this.searchTermToCreate.toLowerCase());
  }

  protected handleErrors(err: any): Observable<any> {
    let errors = [];
    if (err instanceof HttpErrorResponse) {
      const errorsArray = err.error.errors || err.error.data;
      errors = errorsArray.map((error) => error.message);
      errors.forEach((errorMessage) => this.sharedService.showNotification('error', errorMessage));
    }
    return throwError(errors);
  }

  /*private setSelectedValue(value: any | any[], initialSetValue: boolean = false) {
    const { inputField, formControlName } = this.data.formControlInfo;

    let filterRequest: Observable<any[]> = of(this.source);
    const parts = this.formControlName.split('_');
    const field = this.selectSettingsService.getFieldSettings(parts[parts.length - 1]);
    if (field && field.trimInput) {
      if (value) {
        if (inputField.multiple) {
          const searchValues = value.map(item => item[this.displayProperty].trim());
          const requests = searchValues.map(searchValue => this.filterOptions(searchValue, false));
          filterRequest = forkJoin(requests).pipe(
            map((res: any[]) => res.map(item => item.data).reduce((prev, curr) => prev.concat(curr), []))
          );
        } else {
          const searchValue = value[this.displayProperty].trim();
          filterRequest = this.filterOptions(searchValue, false).pipe(
            map(res => res.data)
          );
        }
      }
    }

    filterRequest.pipe(
      takeUntil(this.destroy$)
    ).subscribe(filteredSource => {
      const selectedValue = value
        ? inputField.multiple
          ? filteredSource.filter(item => value.find(i => i[this.valueProperty] === item[this.valueProperty]))
          : filteredSource.find(item => item[this.valueProperty] === value[this.valueProperty])
        : formControlName.indexOf('fontSize') !== -1
          ? this.source.find(item => item.key === 'medium')
          : value;
      this.form.controls[this.formControlName].setValue(selectedValue, { emitEvent: false });
      if (!initialSetValue) {
        this.handleOtherFormControlChange(selectedValue);
      }
    });
  }*/

  /*private getdependentSettings() {
    let dependentFormControlName;
    let dependentFormControl: AbstractControl;

    const fieldName = this.dependency.inputPathway[this.dependency.inputPathway.length - 1];
    dependentFormControlName = fieldName;
    if (this.formControlName.includes('_')) {
      const arr = this.formControlName.split('_');
      arr[arr.length - 1] = fieldName;
      dependentFormControlName =  arr.join('_');
    }
    dependentFormControl = this.form.controls[dependentFormControlName];

    return {
      formControl: this.form.controls[dependentFormControlName],
      name: dependentFormControlName,
      value: dependentFormControl?.value,
      key: dependentFormControl?.value?.key,
      type: dependentFormControl?.value?.type
    };
  }*/

  /*clearFormAndField() {
    this.field.errors = { required: null };
    this.cd.detectChanges();
    this.form.controls[this.formControlName].clearValidators();
    this.form.controls[this.formControlName].updateValueAndValidity();
  }*/

  /*private handleDependency() {
    switch (this.dependency.mode) {
      case 'neighbour': {
        const dependent = this.getdependentSettings();
        const parts = this.formControlName.split('_');
        const nonRequiredTypes = this.selectSettingsService.getNotRequiredContentTypes(parts[parts.length - 1]);
        if (dependent.value) {
          if (dependent.key && nonRequiredTypes.indexOf(dependent.key) === -1 ||
          dependent.type && nonRequiredTypes.indexOf(dependent.type) === -1) {
            this.field.errors = { required: this.translate.instant('CMS.field_required')};
            this.cd.detectChanges();
            this.form.controls[this.formControlName].setValidators(Validators.required);
            this.form.controls[this.formControlName].updateValueAndValidity();
            this.options = {
              params: {
                [this.dependency.queryParamKey]: dependent.value[this.dependency.selectKeyProperty]
              },
              responseType: 'json'
            };
            this.getSource().subscribe();
          } else {
            this.clearFormAndField();
          }
        }
        dependent.formControl.valueChanges.pipe(
          takeUntil(this.destroy$),
          switchMap(value => {
            this.form.controls[this.formControlName].setValue(null);
            if (value) {
              if (value.key && nonRequiredTypes.indexOf(value.key) === -1 ||
                  value.type && nonRequiredTypes.indexOf(value.type) === -1) {
                this.field.errors = { required: this.translate.instant('CMS.field_required')};
                this.cd.detectChanges();
                this.form.controls[this.formControlName].setValidators(Validators.required);
                this.form.controls[this.formControlName].updateValueAndValidity();
                this.options = {
                  params: {
                    [this.dependency.queryParamKey]: value[this.dependency.selectKeyProperty]
                  },
                  responseType: 'json'
                };
                return this.getSource();
              } else {
                this.clearFormAndField();
                return of(null);
              }
            } else {
              this.source = [];
              this.clearFormAndField();
              return of(null);
            }
          })
        ).subscribe();
      } break;
    }
  }
  private handleOtherFormControlChange(selectedValue: any) {
    const parts = this.formControlName.split('_');
    const componentType = this.parentComponent ? this.parentComponent.type : 'default';
    const component = this.selectSettingsService.getParentComponentSettings(componentType);
    const field = this.selectSettingsService.getFieldSettings(parts[parts.length - 1], selectedValue);

    if (component && field && selectedValue && selectedValue != null) {

      this.overriddenFormControls = [];
      field.properties.forEach(property => {
        const componetField = component.fields.find(f => f.type === property.type);
        if (componetField) {

          let controlName;
          if (parts.length === 4) {
            const tabSetIndex = parts[1];
            const tabIndex = parts[2];
            const formControlParts = Object.keys(this.form.controls)
            .map(key => key.split('_'))
            .find(p => p[1] === tabSetIndex && p[2] === tabIndex && p[3] === componetField.propertyName);
            controlName = formControlParts.join('_');
          } else {
            controlName = componetField.propertyName;
          }
          const controlValue = this.form.controls[controlName] && this.form.controls[controlName].value;
          if (controlValue) {
            this.overriddenFormControls.push({
              label: this.translate.instant(componetField.label),
              formControlName: controlName,
              newValue: property.value,
              checked: false
            });
          } else if (controlName && this.form.controls[controlName]) {
            this.form.controls[controlName].setValue(property.value);
          }
        }
      });
      this.createOverridModal();
    }
  }

  createOverridModal() {
    const isFormReset = this.form.value.formReset;
    if (this.overriddenFormControls.length > 0 && !isFormReset) {
      this.allChecked = false;
      this.modal.create({
        nzTitle: this.translate.instant('CMS.contentOverride'),
        nzContent: this.overrideFormControlsModal,
        nzMaskClosable: false,
        nzClosable: false,
        nzCancelText: null,
        nzOkText: 'OK',
        nzOnOk: () => {
          this.overriddenFormControls
            .filter(item => item.checked)
            .forEach(item => this.form.controls[item.formControlName].setValue(item.newValue));
        }
      });
    }
  }*/

  updateAllChecked(): void {
    this.indeterminate = false;
    this.overriddenFormControls = this.overriddenFormControls.map((item) => {
      return {
        ...item,
        checked: this.allChecked,
      };
    });
  }

  updateSingleChecked(): void {
    this.allChecked = this.overriddenFormControls.every((item) => item.checked);
    this.indeterminate = !this.allChecked && this.overriddenFormControls.some((item) => item.checked);
  }

  onSearch(value: string) {
    if (value.length > 0 || (value.length === 0 && this.searchTermToCreate.length <= 1)) {
      this.searchTermToCreate = value;
    }
    this.searchChangeSubject.next(value);
  }

  onTreeSelectChange(selectedId: string[] | string) {
    let selectedItem = null;
    if (this.multiple) {
      selectedItem = this.source.filter((item) => selectedId.includes(item.id));
    } else {
      selectedItem = this.source.find((item) => item.id === selectedId) || null;
    }
    this.setState({ value: selectedItem });
  }

  profileResponseToSourceItem(profileResponse: ContentResponse) {
    const profileData: any = {};
    profileResponse.data.forEach((inputElement) => {
      profileData[inputElement.key] = inputElement.value;
    });
    return {
      id: profileResponse.meta.id,
      name: `${profileData.lastName} ${profileData.firstName}`,
      fullName: `${profileData.lastName} ${profileData.firstName}`,
    };
  }

  onAddMe() {
    const isExternalContributors = this.portalConfigService.isConfigSet(PortalConfigSetting.ENABLE_EXTERNAL_CONTRIBUTOR_FEATURE);
    if (isExternalContributors) {
      this.apiService.getCurrentExternalContributor().subscribe((externalContributor) => {
        let contributor = externalContributor?.data;
        if (!isArray(contributor)) {
          contributor = [contributor];
        }
        if (!this.source.find((source) => source?.id === contributor?.id)) {
          this.source.push(...contributor);
        }
        this.onTypeSelectChange(contributor.concat(this.typeValues));
      });
      return;
    }

    this.apiService.getProfile().subscribe((res) => {
      const userId = res.meta.id;
      const sourceIndex = this.source.findIndex((i) => i.id === userId);
      const typeSelectIndex = this.typeValues.findIndex((i) => i.id === userId);
      let added = [];
      if (typeSelectIndex === -1) {
        if (this.typeSelectValues.length > 0 && sourceIndex !== -1) {
          added = this.typeSelectValues.concat([this.source[sourceIndex]]);
        } else {
          added = [this.source[sourceIndex]];
        }

        if (!added || !this.source[sourceIndex]) {
          added = [this.profileResponseToSourceItem(res)];
          this.source.push(...added);
        }

        this.onTypeSelectChange(added.concat(this.typeValues));
      }
    });
  }

  onTypeSelectChange(selectedItems: any[] | any) {
    selectedItems = this.percentagesService.setSomePercentage(
      selectedItems?.filter((value, index, self) => index === self?.findIndex((item) => item.id === value.id))
    );

    selectedItems = selectedItems.map((item) => {
      if (item.name === undefined) {
        const sourceItem = this.source.find((sourceItem) => sourceItem.id === item.id);
        return {
          ...sourceItem,
          ...item,
        };
      }
      return item;
    });

    this.typeValues = selectedItems;
    this.typeSelectValues = this.typeValues;
    this.setState({ value: this.typeValues });
  }

  onTypeValueChange(value: number, id: string) {
    if (!value) {
      value = 1;
    }
    const typeValueIndex = this.typeValues.findIndex((typeValue) => typeValue.id === id);
    this.typeValues[typeValueIndex].percentage = value;

    const totalPercentage = this.percentagesService.getTotalPercentage(this.typeValues);
    if (totalPercentage > 100) {
      this.sharedService.showNotification('warning', this.translateService.instant('CMS.contributor-percent-warning'));
      this.typeValues = this.percentagesService.setSomePercentage(this.typeValues);
    }

    this.setState({ value: this.typeValues });
  }

  onDeleteTypeValue(id: string) {
    this.typeSelectValues = this.typeSelectValues.filter((obj) => obj.id !== id);
    this.typeValues = this.percentagesService.setSomePercentage(this.typeValues.filter((obj) => obj.id !== id));
    this.setState({ value: this.typeValues });
  }

  onAddUserToSelect() {
    // belekerül a user 0 százalékkal
    // vagy 1 százalékkal 0 egyáltalán választható?
  }

  public onFocusStateChanged(state: boolean) {
    setTimeout(() => {
      this._isFocused = state;
    }, 500);
  }

  onAddAll() {
    switch (this.mode) {
      case 'select':
        this.setState({ value: this.source });
        break;
      case 'treeSelect':
        this.treeSelectValues = this.source.map((item) => item.id);
        this.setState({ value: this.source });
        break;
      case 'typeSelect':
        this.onTypeSelectChange(this.source);
        break;
      case 'simpleSelect':
        const allValues = this.source.map((item) => item[this.valueProperty]);
        this.setState({ value: allValues });
        break;
    }
  }

  onRemoveAll() {
    switch (this.mode) {
      case 'select':
        this.setState({ value: [] });
        break;
      case 'treeSelect':
        this.treeSelectValues = [];
        this.setState({ value: [] });
        break;
      case 'typeSelect':
        this.onTypeSelectChange([]);
        this.setState({ value: [] });
        break;
      case 'simpleSelect':
        this.setState({ value: [] });
        break;
    }
  }

  private overrideValueToExplicitCreate(inputField: InputFieldContent, createUrl: string): void {
    if (inputField.inputType === 'text') {
      if (inputField.key === 'slug') {
        inputField.value = this.utilsService.generateSlug(this.searchTermToCreate);
      } else {
        inputField.value = this.searchTermToCreate;
      }
    }
    if (inputField.inputType === 'checkbox') {
      if (inputField.key === 'isActive' && createUrl.endsWith('ingredient')) {
        inputField.value = true;
      }
    }
  }

  private filterOptions(arr1, arr2) {
    return arr1.filter((obj1) => {
      return !arr2.some((obj2) => {
        return obj1.id === obj2.id;
      });
    });
  }
}
