<!--<nz-form-item [formGroup]="form" trendFormControlErrorContainer>
  <nz-form-label [nzSm]="6" [nzXs]="24">{{ title }}<span *ngIf="field.errors.required" class="required-indicator">*</span></nz-form-label>

  <nz-form-control [nzSm]="14" [nzXs]="24" [nzValidateStatus]="validateStatus" [nzHasFeedback]="form.controls[formControlName].enabled">
    <nz-spin [nzSpinning]="loading" nzSize="small">
      <nz-select
        nzShowSearch
        nzAllowClear
        nzServerSearch
        [nzDisabled]="form.controls[formControlName].disabled"
        [nzMode]="mode"
        [formControlName]="formControlName"
        [errors]="field.errors"
        (nzOnSearch)="onSearch($event)">

        <ng-container *ngIf="!loading">
          <nz-option *ngFor="let item of source" [nzLabel]="item[displayProperty]" [nzValue]="item"></nz-option>
        </ng-container>

        <nz-option *ngIf="loading" nzDisabled nzCustomContent>
          <i nz-icon nzType="loading" class="loading-icon"></i> Betöltés...
        </nz-option>
      </nz-select>
    </nz-spin>
  </nz-form-control>

</nz-form-item>-->

<!--
  [nzHasFeedback]="form.controls[formControlName].enabled
  [errors]="field.errors"
-->
<div class="select-form-control-container form-control-container" [hidden]="state.data.isHidden">
  <!-- <div *ngIf="mode === 'typeSelect'">
    <a class="add-user" (click)="onAddUserToSelect()" >Adj hozzá engem</a>
  </div> -->
  <nz-form-control [nzValidateStatus]="validateStatus">
    <nz-spin [nzSpinning]="loading" nzSize="small">
      <ng-container *ngIf="mode === 'select' && !isColor" [ngTemplateOutlet]="selectTemplate"> </ng-container>

      <ng-container *ngIf="mode === 'select' && isColor" [ngTemplateOutlet]="selectColorTemplate"> </ng-container>

      <ng-container *ngIf="mode === 'treeSelect'" [ngTemplateOutlet]="treeSelectTemplate"> </ng-container>

      <ng-container *ngIf="mode === 'typeSelect'" [ngTemplateOutlet]="typeSelectTemplate"> </ng-container>

      <ng-container *ngIf="mode === 'simpleSelect'" [ngTemplateOutlet]="simpleTemplate"> </ng-container>

      <label class="form-control-label" [ngClass]="{ 'has-value': hasValue || state.data.isFocused }">
        {{ label | translate }} <span *ngIf="state.data.asserts?.NotBlank" class="required-indicator">*</span>
      </label>
    </nz-spin>
  </nz-form-control>
  <div *ngIf="hasAddAll" class="add-all-container">
    <button
      nz-button
      nzType="primary"
      nzSize="small"
      class="type-value-delete"
      [disabled]="state.data.isDisabled"
      [attr.test-key]="'mind hozzáadása gomb'"
      (click)="onAddAll()"
    >
      Mind
    </button>
    <button
      *ngIf="formControl.value?.length > 0"
      nz-button
      nzType="primary"
      nzSize="small"
      class="type-value-delete"
      [attr.test-key]="'egyik sem hozzáadása gomb'"
      [disabled]="state.data.isDisabled"
      (click)="onRemoveAll()"
    >
      Egyik sem
    </button>
  </div>

  <!-- Input validációs szövegek megjelenítésére -->
  <input type="hidden" [formControl]="formControl" [errors]="errors" />
</div>

<ng-template #typeSelectTemplate>
  <div class="type-select-wrapper">
    <button
      nz-button
      nzType="primary"
      nzSize="small"
      class="add-me-button"
      [attr.test-key]="'Adj hozzá engem gomb'"
      [disabled]="state.data.isDisabled"
      (click)="onAddMe()"
    >
      Adj hozzá engem
    </button>

    <nz-select
      class="form-control"
      nzShowSearch
      nzAllowClear
      [attr.test-key]="label | translate"
      [nzServerSearch]="filterable"
      [nzDisabled]="state.data.isDisabled"
      [nzMode]="multiple ? 'multiple' : 'default'"
      [compareWith]="compareWithFunction"
      [ngModel]="typeSelectValues"
      (nzOnSearch)="onSearch($event)"
      (ngModelChange)="onTypeSelectChange($event)"
      (nzScrollToBottom)="loadMoreOptions()"
    >
      <ng-container *ngIf="!loading">
        <nz-option
          *ngFor="let item of source"
          [nzDisabled]="item.disabled"
          [nzLabel]="translateSource ? (item[displayProperty] | translate) : item[displayProperty]"
          [attr.test-key]="translateSource ? (item[displayProperty] | translate) : item[displayProperty]"
          [nzValue]="item"
        >
        </nz-option>
      </ng-container>

      <nz-option *ngIf="loading" nzDisabled nzCustomContent> <i nz-icon nzType="loading" class="loading-icon"></i> Betöltés... </nz-option>
    </nz-select>
    <div class="type-list-wrapper" *ngIf="typeValues && typeValues?.length > 0">
      <div *ngFor="let typeValue of typeValues">
        <nz-form-control class="type-list-item">
          <label>
            {{ typeValue.name || typeValue?.fullName }}
          </label>
          <div class="type-input">
            <nz-input-number
              class="form-control"
              [nzMin]="1"
              [nzMax]="100"
              [attr.test-key]="typeValue.name + ' szerkesztés'"
              [nzDisabled]="state.data.isDisabled"
              [ngModel]="typeValue.percentage"
              (ngModelChange)="onTypeValueChange($event, typeValue.id)"
            ></nz-input-number>
            <button
              nz-button
              nzType="primary"
              nzSize="small"
              class="type-value-delete"
              [attr.test-key]="typeValue.name + ' törlés'"
              [disabled]="state.data.isDisabled"
              (click)="onDeleteTypeValue(typeValue.id)"
            >
              X
            </button>
          </div>
        </nz-form-control>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #simpleTemplate>
  <nz-select
    class="form-control"
    nzShowSearch
    nzAllowClear
    [attr.test-key]="label | translate"
    [nzServerSearch]="filterable"
    [nzDisabled]="state.data.isDisabled"
    [nzMode]="multiple ? 'multiple' : 'default'"
    [compareWith]="compareWithFunction"
    [formControl]="formControl"
    [errors]="null"
    (nzOnSearch)="onSearch($event)"
    (keydown)="keydownInDo($event)"
    (nzScrollToBottom)="loadMoreOptions()"
  >
    <ng-container *ngIf="!loading">
      <nz-option
        *ngFor="let item of source"
        [nzDisabled]="item.disabled"
        [nzLabel]="translateSource ? (item[displayProperty] | translate) : item[displayProperty]"
        [attr.test-key]="translateSource ? (item[displayProperty] | translate) : item[displayProperty]"
        [nzValue]="item[valueProperty]"
      >
      </nz-option>
    </ng-container>

    <nz-option *ngIf="loading || isMoreOptionsLoading" nzDisabled nzCustomContent>
      <i nz-icon nzType="loading" class="loading-icon"></i> Betöltés...
    </nz-option>
  </nz-select>
</ng-template>

<ng-template #selectTemplate>
  <div class="select-row">
    <nz-select
      class="form-control"
      nzShowSearch
      nzAllowClear
      [attr.test-key]="label | translate"
      [nzServerSearch]="filterable"
      [nzDisabled]="state.data.isDisabled"
      [nzMode]="multiple ? 'multiple' : 'default'"
      [compareWith]="compareWithFunction"
      [formControl]="formControl"
      [errors]="null"
      (nzFocus)="onFocusStateChanged(true)"
      (nzBlur)="onFocusStateChanged(false)"
      (nzOnSearch)="onSearch($event)"
      (keydown)="keydownInDo($event)"
      (nzScrollToBottom)="loadMoreOptions()"
    >
      <ng-container *ngIf="!loading">
        <nz-option
          *ngFor="let item of source"
          [nzDisabled]="item.disabled"
          [nzLabel]="translateSource ? (item[displayProperty] | translate) : item[displayProperty]"
          [attr.test-key]="translateSource ? (item[displayProperty] | translate) : item[displayProperty]"
          [nzValue]="customDisplayProperty ? item[customDisplayProperty] : item"
        >
        </nz-option>
      </ng-container>

      <nz-option *ngIf="loading" nzDisabled nzCustomContent> <i nz-icon nzType="loading" class="loading-icon"></i> Betöltés... </nz-option>
    </nz-select>
    <button *ngIf="createUrl" (click)="explicitCreate()" [disabled]="checkCreateButtonDisabled()" nzType="primary" class="explicit-create-button" nz-button>
      <span nz-icon nzType="plus" nzTheme="outline"></span>
    </button>
  </div>
</ng-template>

<ng-template #selectColorTemplate>
  <nz-select
    class="form-control"
    nzShowSearch
    nzAllowClear
    [attr.test-key]="label | translate"
    [nzServerSearch]="filterable"
    [nzDisabled]="state.data.isDisabled"
    [nzMode]="multiple ? 'multiple' : 'default'"
    [compareWith]="compareWithFunction"
    [formControl]="formControl"
    [errors]="null"
    (nzOnSearch)="onSearch($event)"
    (keydown)="keydownInDo($event)"
  >
    <ng-container *ngIf="!loading">
      <nz-option
        nzCustomContent
        *ngFor="let item of source"
        [nzDisabled]="item.disabled"
        [nzLabel]="translateSource ? (item[displayProperty] | translate) : item[displayProperty]"
        [attr.test-key]="translateSource ? (item[displayProperty] | translate) : item[displayProperty]"
        [nzValue]="item"
      >
        <div>
          {{ item[displayProperty] }}
          <div class="color" [ngStyle]="{ 'background-color': item[displayProperty] }"></div>
        </div>
      </nz-option>
    </ng-container>
    <nz-option *ngIf="loading" nzDisabled nzCustomContent> <i nz-icon nzType="loading" class="loading-icon"></i> Betöltés... </nz-option>
  </nz-select>
</ng-template>

<ng-template #treeSelectTemplate>
  <!-- Az nz-tree-selectben nincs lehetőség backend keresésre, ami a rovat hírstart mezőnél kell, így ide custom megoldást alkalmazunk. -->
  <!-- Viszont ebben a custom megoldásban jelenleg csak parent-child viszonyt tudunk ábrázolni, több szintűt nem, ezért nem cseréljük le véglegesen. -->
  <!-- A hosszútávú megoldás: form generator 2 használata, ott már ez meg van jól csinálva. -->
  <ng-container *ngIf="label === 'hirstartColumnName'">
    <nz-select
      class="form-control"
      nzShowSearch
      nzAllowClear
      [attr.test-key]="label | translate"
      [nzServerSearch]="filterable"
      [nzDisabled]="state.data.isDisabled"
      [nzMode]="multiple ? 'multiple' : 'default'"
      [compareWith]="compareWithFunction"
      [formControl]="formControl"
      [errors]="null"
      (nzOnSearch)="onSearch($event)"
      (keydown)="keydownInDo($event)"
      (nzScrollToBottom)="loadMoreOptions()"
    >
      <ng-container *ngIf="!loading">
        <nz-option
          *ngFor="let item of source"
          [nzCustomContent]="true"
          [nzDisabled]="item.disabled"
          [nzLabel]="translateSource ? (item[displayProperty] | translate) : item[displayProperty]"
          [attr.test-key]="translateSource ? (item[displayProperty] | translate) : item[displayProperty]"
          [nzValue]="item"
        >
          <div class="custom-option {{ item.parentId ? 'indent' : '' }}">
            {{ item[valueProperty] }}
          </div>
        </nz-option>
      </ng-container>

      <nz-option *ngIf="loading || isMoreOptionsLoading" nzDisabled nzCustomContent>
        <i nz-icon nzType="loading" class="loading-icon"></i> Betöltés...
      </nz-option>
    </nz-select>
  </ng-container>

  <ng-container *ngIf="label !== 'hirstartColumnName'">
    <nz-tree-select
      class="form-control"
      nzShowSearch
      [nzNodes]="treeNodes"
      [attr.test-key]="label | translate"
      [nzDefaultExpandAll]="true"
      [nzMultiple]="multiple"
      [nzDisabled]="state.data.isDisabled"
      [ngModel]="treeSelectValues"
      (ngModelChange)="onTreeSelectChange($event)"
    >
    </nz-tree-select>
  </ng-container>
</ng-template>

<ng-template #overrideFormControlsModal>
  <h4>{{ 'CMS.contentOverrideDescription' | translate }}</h4>
  <div class="select-all-container">
    <label nz-checkbox [(ngModel)]="allChecked" (ngModelChange)="updateAllChecked()" [nzIndeterminate]="indeterminate">
      {{ 'CMS.selectAll' | translate }}
    </label>
  </div>
  <br />
  <nz-checkbox-group [(ngModel)]="overriddenFormControls" (ngModelChange)="updateSingleChecked()"></nz-checkbox-group>
</ng-template>
