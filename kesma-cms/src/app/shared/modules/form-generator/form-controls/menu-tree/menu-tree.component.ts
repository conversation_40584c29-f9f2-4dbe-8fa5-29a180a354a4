import { DOCUMENT } from '@angular/common';
import { Component, Input, Output, EventEmitter, Inject } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

interface TreeNode {
  key: string;
  children: TreeNode[];
  isExpanded?: boolean;
}

interface DropInfo {
  targetId: string;
  action?: string;
}

@Component({
  selector: 'app-menu-tree',
  templateUrl: './menu-tree.component.html',
  styleUrls: ['./menu-tree.component.scss'],
  standalone: false,
})
export class MenuTreeComponent {
  @Input() set treeData(treeData) {
    this.prepareExpanded(treeData);
    this._treeData = treeData;
    this.dropTargetIds = [];
    this.nodeLookup = {};
    this.prepareDragDrop(this._treeData);
  }
  @Output() editTreeNodeEvent: EventEmitter<any> = new EventEmitter();
  @Output() checkBoxChange: EventEmitter<any> = new EventEmitter();
  @Output() expandChange: EventEmitter<any> = new EventEmitter();
  @Output() nodeMoved: EventEmitter<any> = new EventEmitter();
  @Input() maxTreeLevel: number = 2;

  // Required for automated testing.
  @Input() columnIndex: number = 0;

  _treeData = [];
  nodes: TreeNode[] = [];

  // ids for connected drop lists
  dropTargetIds = [];
  nodeLookup = {};
  dropActionTodo: DropInfo = null;

  private expandedList = [];
  private checkedList = [];

  constructor(
    @Inject(DOCUMENT) private readonly document: Document,
    private readonly translateService: TranslateService
  ) {}

  /**
   * Generates a unique test key for automated testing purposes.
   * For example: expander-2-depth-1-column-0
   */
  getTestKey(key: string, index: number, depth: number): string {
    return `${key}-${index}-depth-${depth - 1}-column-${this.columnIndex}`;
  }

  prepareDragDrop(nodes: TreeNode[]) {
    nodes.forEach((node) => {
      this.dropTargetIds.push(node.key);
      this.nodeLookup[node.key] = node;
      this.prepareDragDrop(node.children);
    });
  }

  getTargetLevel(targetId: string, nodesToSearch: TreeNode[], startLevel: number = 1) {
    for (const node of nodesToSearch) {
      if (node.key === targetId) {
        return startLevel;
      }
      const ret = this.getTargetLevel(targetId, node.children, startLevel + 1);
      if (ret) {
        return ret;
      }
    }
    return null;
  }

  prepareExpanded(nodes: TreeNode[]) {
    nodes.forEach((node) => {
      node.key = `${node.key}`;
      if (this.expandedList.includes(node.key)) {
        node.isExpanded = true;
      }
      this.prepareExpanded(node.children);
    });
  }

  dragMoved(event) {
    const e = this.document.elementFromPoint(event.pointerPosition.x, event.pointerPosition.y);

    if (!e) {
      this.clearDragInfo();
      return;
    }
    const container = e.classList.contains('node-item') ? e : e.closest('.node-item');
    if (!container) {
      this.clearDragInfo();
      return;
    }
    const targetId = container.getAttribute('data-id');
    this.dropActionTodo = {
      targetId: targetId,
    };

    const draggedItemId = event.source.data;
    const draggedItem = this.nodeLookup[draggedItemId];
    const draggedItemCantBeLeaf = (draggedItem.children && draggedItem.children.length > 0) || draggedItem.type === 'ColumnSeparator';

    const targetLevel = this.getTargetLevel(targetId, this._treeData);
    const canDropHere = draggedItemCantBeLeaf && targetLevel <= this.maxTreeLevel;

    const targetRect = container.getBoundingClientRect();
    const oneThird = targetRect.height / 3;

    if (event.pointerPosition.y - targetRect.top < oneThird) {
      // before
      this.dropActionTodo['action'] = canDropHere || (draggedItem.type === 'Dropdown' && targetLevel === this.maxTreeLevel) ? 'before-invalid' : 'before';
    } else if (event.pointerPosition.y - targetRect.top > 2 * oneThird) {
      // after
      this.dropActionTodo['action'] = canDropHere || (draggedItem.type === 'Dropdown' && targetLevel === this.maxTreeLevel) ? 'after-invalid' : 'after';
    } else {
      // inside
      const targetedItem = this.nodeLookup[targetId];
      this.dropActionTodo['action'] =
        draggedItemCantBeLeaf ||
        targetLevel >= this.maxTreeLevel ||
        targetedItem.type === 'ColumnSeparator' ||
        (draggedItem.type === 'Dropdown' && targetLevel === this.maxTreeLevel - 1)
          ? 'inside-invalid'
          : 'inside';
    }
    this.showDragInfo();
  }

  drop(event) {
    if (!this.dropActionTodo) {
      return;
    }

    const draggedItemId = event.item.data;

    const targetListId = this.getParentNodeId(this.dropActionTodo.targetId, this._treeData, 'main');
    const targetList = targetListId !== 'main' ? this.nodeLookup[targetListId] : '';

    const draggedItem = this.nodeLookup[draggedItemId];
    const targetedItem = this.nodeLookup[this.dropActionTodo.targetId];
    targetedItem.parentNode = targetList;

    const result: any = { node: targetedItem, dragNode: draggedItem };

    switch (this.dropActionTodo.action) {
      case 'before':
        result.pos = -1;
        break;
      case 'after':
        result.pos = 1;
        break;
      case 'inside':
        result.pos = 0;
        break;
      default:
        this.clearDragInfo();
        return;
    }

    this.nodeMoved.emit(result);
    this.clearDragInfo(true);
  }

  getParentNodeId(id: string, nodesToSearch: TreeNode[], parentId: string): string {
    for (const node of nodesToSearch) {
      if (node.key === id) {
        return parentId;
      }
      const ret = this.getParentNodeId(id, node.children, node.key);
      if (ret) {
        return ret;
      }
    }
    return null;
  }
  showDragInfo() {
    this.clearDragInfo();
    if (this.dropActionTodo) {
      this.document.getElementById('node-' + this.dropActionTodo.targetId).classList.add('drop-' + this.dropActionTodo.action);
    }
  }
  clearDragInfo(dropped = false) {
    if (dropped) {
      this.dropActionTodo = null;
    }
    this.document.querySelectorAll('.drop-before').forEach((element) => element.classList.remove('drop-before'));
    this.document.querySelectorAll('.drop-after').forEach((element) => element.classList.remove('drop-after'));
    this.document.querySelectorAll('.drop-inside').forEach((element) => element.classList.remove('drop-inside'));
    this.document.querySelectorAll('.drop-before-invalid').forEach((element) => element.classList.remove('drop-before-invalid'));
    this.document.querySelectorAll('.drop-after-invalid').forEach((element) => element.classList.remove('drop-after-invalid'));
    this.document.querySelectorAll('.drop-inside-invalid').forEach((element) => element.classList.remove('drop-inside-invalid'));
  }

  nodeChecked(e: MouseEvent, node) {
    e.stopPropagation();
    if (node.disableCheckbox) {
      return;
    }
    if (node.checked) {
      node.checked = false;
      this.checkedList = this.checkedList.filter((element) => element !== node.key);
    } else {
      node.checked = true;
      this.checkedList.push(node.key);
    }
    this.checkBoxChange.emit(this.checkedList);
  }

  nodeExpaned(node) {
    if (node.children && node.children.length) {
      if (node.isExpanded) {
        node.isExpanded = false;
        this.expandedList = this.expandedList.filter((element) => element !== node.key);
      } else {
        node.isExpanded = true;
        this.expandedList.push(node.key);
      }
      this.expandChange.emit(this.expandedList);
    }
  }

  editTreeNode(e: MouseEvent, node) {
    e.stopPropagation();
    this.editTreeNodeEvent.emit(node);
  }

  public clearChecked() {
    this.checkedList = [];
    this.checkBoxChange.emit(this.checkedList);
  }
}
