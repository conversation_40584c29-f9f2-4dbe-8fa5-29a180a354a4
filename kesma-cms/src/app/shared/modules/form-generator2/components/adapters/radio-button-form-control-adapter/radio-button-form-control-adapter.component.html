<app-radio-group-form-control
  [options]="options"
  [value]="value"
  [isDisabled]="isDisabled"
  [isRequired]="isRequired"
  [errorMessage]="errorMessage"
  (valueChange)="onValueChange($event)"
  [label]="label"
  valueProperty="value"
></app-radio-group-form-control>

<div *ngIf="value" class="buttons">
  <button
    *ngFor="let languageColumn of languageColumns"
    class="language-button"
    nz-button
    nzType="primary"
    nzSize="large"
    nz-popconfirm
    nzPopconfirmTitle="Új nyelvesített cikk létrehozása?"
    nzPopconfirmPlacement="top"
    (nzOnConfirm)="onLanguageSelect(languageColumn.slug)"
  >
    {{ languageColumn.title }} - nyelvesítés
  </button>
</div>
