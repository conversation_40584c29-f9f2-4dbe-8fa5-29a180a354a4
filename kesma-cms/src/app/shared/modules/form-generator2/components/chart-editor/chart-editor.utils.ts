import { ChartType } from 'chart.js';
import { KesmaChartConfig } from './chart-editor.definitions';

export const AVAILABLE_CHART_TYPES: ChartType[] = ['bar', 'doughnut', 'polarArea', 'pie', 'line'];
export const PRIMARY_COLOR = '#1890ff';

export const CHART_CONFIG: Record<string, KesmaChartConfig> = {
  bar: {
    hasDatasetTitle: true,
    isBackgroundColorNotArray: true,
  },
  polarArea: {
    cannotCreateMoreSets: true,
  },
  line: {
    hasDatasetTitle: true,
    hasBorderColor: true,
    isBackgroundColorNotArray: true,
  },
  doughnut: {
    cannotCreateMoreSets: true,
  },
  pie: {
    cannotCreateMoreSets: true,
  },
};
