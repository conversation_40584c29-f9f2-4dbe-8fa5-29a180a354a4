@use "shared" as *;

:host {
  margin-top: -15px;
  display: block;
  .select::ng-deep {
    .ant-select-selector {
      border-radius: 4px;
    }
    .ant-select-selection-item {
      background-color: $primary-color;
      color: $white;
      nz-icon {
        color: $white;
      }
    }
  }
}
.types {
  display: flex;
  gap: 20px;
  .preview-box {
    border: 1px solid lightgray;
    max-height: 230px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 5px 10px;
    border-radius: 5px;
    cursor: pointer;
    flex: 1;
    &.active {
      border-color: $primary-color;
      border-width: 2px;
    }
  }
  .preview {
    height: 100%;
    object-fit: contain;
    width: 100%;
  }
}
.alert {
  margin-bottom: 20px;
}
.configuration {
  display: flex;
  gap: 20px;
}
.groups {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  .group {
    display: flex;
    flex-direction: column;
    gap: 4px;
    &-label {
      font-weight: bold;
    }
  }
}
.bordered {
  border: 1px solid $grey-1;
  border-radius: 4px;
  padding: 16px;
}
.color-picker {
  width: 32px;
  height: 32px;
  margin-left: 5px;
}
.datasets {
  max-height: 500px;
  overflow-y: auto;
  .group {
    margin-bottom: 12px;
  }
}
.delete-btn {
  width: fit-content;
  display: flex;
  align-items: center;
  margin-left: auto;
  margin-top: 10px;
}
.preview {
  kesma-chart {
    aspect-ratio: 16/9;
  }
}

nz-input-number {
  width: 100%;
}
nz-icon {
  color: $primary-color;
}
