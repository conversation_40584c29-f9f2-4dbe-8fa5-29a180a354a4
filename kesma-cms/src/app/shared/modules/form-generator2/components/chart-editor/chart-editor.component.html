<nz-tabset [(nzSelectedIndex)]="selectedTabIndex" [nzAnimated]="false">
  <nz-tab nzTitle="Grafikon típusa">
    @if (chartData().type) {
      <nz-alert
        class="alert"
        nzType="warning"
        nzMessage="Figyelem! A diagram típusának megváltoztatásával elvesztésre kerül az összes beállított adathalmaz!"
        nzShowIcon>
      </nz-alert>
    }
    <div class="types">
      @for (chart of AVAILABLE_CHART_TYPES; track chart) {
        <div
          nz-popconfirm
          nzPopconfirmTitle="Biztos módosítani szeretnéd a grafikon típusát?"
          nzCancelText="Mégse"
          [nzCondition]="!chartData().type"
          nzOkText="Módosítás"
          (nzOnConfirm)="handleTypeChange(chart)"
          class="preview-box"
          [class.active]="chart === chartData().type">
          <img class="preview" src="/assets/images/charts/{{ chart }}-chart.webp" alt="" />
        </div>
      }
    </div>
  </nz-tab>
  <nz-tab [nzDisabled]="!chartData().type" nzTitle="Konfigurálás">
    <div class="configuration">
      <div class="groups">
        <div class="group">
          <span class="group-label">
            Adatcímkék:
            <nz-icon
              nz-tooltip
              nzTooltipTitle="Címke hozzáadásához kezdje el begépelni a kívánt szöveget, majd nyomja meg az Enter billentyűt a hozzáadáshoz."
              nzType="info-circle"
              nzTheme="fill"
            />
          </span>
          <nz-select
            class="select"
            nzMode="tags"
            nzPlaceHolder="Január, Február, Március"
            [ngModel]="chartData().labels"
            (ngModelChange)="handleLabelChange($event)">
          </nz-select>
        </div>
        <div class="group">
          <span class="group-label">Előnézet:</span>
          @if (chartData().labels.length) {
            <div class="preview">
              <kesma-chart [data]="chartData()" />
            </div>
          } @else {
            <p>Az előnézet megjelenítéséhez legalább egy adatcímkét be kell állítani.</p>
          }
        </div>
      </div>
      <div class="groups">
        <div class="group">
          <span class="group-label">Adathalmazok:</span>
          <div class="datasets">
            @if (!chartData().labels.length) {
              Az adathalmazok megjelenítéséhez legalább egy adatcímkét be kell állítani.
            } @else {
              @for (dataSet of chartData().datasets; track $index) {
                <div class="group bordered">
                  @if (chartConfig()?.hasDatasetTitle) {
                    <nz-space-compact nzBlock>
                      <nz-input-group nzAddOnBefore="{{ $index + 1 }}. Adathalmaz címe">
                        <input
                          nz-input
                          placeholder="Cím"
                          [(ngModel)]="dataSet.label"
                          (ngModelChange)="triggerChartUpdate()"
                        />
                      </nz-input-group>
                      @if (chartConfig()?.isBackgroundColorNotArray) {
                        <ng-container *ngTemplateOutlet="colorTemplate; context: { dataset: dataSet }"></ng-container>
                      } @else {
                        <ng-container
                          *ngTemplateOutlet="colorTemplate; context: { dataset: dataSet, index: $index }">
                        </ng-container>
                      }
                      @if (chartConfig()?.hasBorderColor) {
                        <ng-container
                          *ngTemplateOutlet="colorTemplate; context: { dataset: dataSet, isBorderColor: true }">
                        </ng-container>
                      }
                    </nz-space-compact>
                  }
                  @for (label of chartData().labels; track $index) {
                    <span class="group-label">{{ label }} adatcímke értéke:</span>
                    <nz-space-compact nzBlock>
                      <nz-input-number [(ngModel)]="dataSet.data[$index]" (ngModelChange)="triggerChartUpdate()">
                        <span nzInputAddonBefore>Érték</span>
                      </nz-input-number>
                      @if (!chartConfig()?.hasDatasetTitle) {
                        <ng-container
                          *ngTemplateOutlet="colorTemplate; context: { dataset: dataSet, index: $index }">
                        </ng-container>
                        @if (chartConfig()?.hasBorderColor) {
                          <ng-container
                            *ngTemplateOutlet="colorTemplate; context: { dataset: dataSet, isBorderColor: true }">
                          </ng-container>
                        }
                      }
                    </nz-space-compact>
                  }
                  @if (!chartConfig()?.cannotCreateMoreSets && !($first && $last)) {
                    <button
                      nz-button
                      nzType="primary"
                      [nzDanger]="true"
                      class="delete-btn"
                      (click)="deleteDataset($index)">
                      <i nz-icon nzType="delete"></i> Halmaz törlése
                    </button>
                  }
                </div>
              }
            }
          </div>
        </div>

        @if (!chartConfig()?.cannotCreateMoreSets && chartData().labels.length) {
          <button nz-button nzType="primary" class="create" (click)="addNewDataset()">Új halmaz hozzáadása</button>
        }
      </div>
    </div>
  </nz-tab>
</nz-tabset>

<ng-template #colorTemplate let-dataset="dataset" let-isBorderColor="isBorderColor" let-index="index">
  @let propertyName = isBorderColor ? "borderColor" : "backgroundColor";
  @let tooltip = isBorderColor ? "Körvonal szín beállítása" : "Háttérszín beállítása";
  @if (index !== undefined) {
    <input
      class="color-picker"
      nz-tooltip
      [nzTooltipTitle]="tooltip"
      type="color"
      [(ngModel)]="dataset[propertyName][index]"
      (change)="triggerChartUpdate()"
    />
  } @else {
    <input
      class="color-picker"
      nz-tooltip
      [nzTooltipTitle]="tooltip"
      type="color"
      [(ngModel)]="dataset[propertyName]"
      (change)="triggerChartUpdate()"
    />
  }
</ng-template>
