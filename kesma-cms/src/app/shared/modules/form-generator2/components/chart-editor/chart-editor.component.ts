import { Component, ChangeDetectionStrategy, signal, computed, Input, inject, OnInit, effect } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { NgZorroModule } from '@shared/modules/ng-zorro/ng-zorro.module';
import { ChartComponent } from '@trendency/kesma-ui';
import { ChartType, ChartDataset } from 'chart.js';
import { KesmaChartData } from '@shared/modules/form-generator2/components/chart-editor/chart-editor.definitions';
import { AVAILABLE_CHART_TYPES, CHART_CONFIG, PRIMARY_COLOR } from '@shared/modules/form-generator2/components/chart-editor/chart-editor.utils';
import { GeneratedFormControl } from '@shared/modules/form-generator2/definitions/form-generator-adapters.definitions';
import { NZ_MODAL_DATA } from 'ng-zorro-antd/modal';
import { FormGenerator2Service } from '@shared/modules/form-generator2/services/form-generator.service';
import { NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'app-chart-editor',
  templateUrl: './chart-editor.component.html',
  styleUrl: './chart-editor.component.scss',
  imports: [NgZorroModule, FormsModule, ChartComponent, NgTemplateOutlet],
  providers: [FormGenerator2Service],
})
export class ChartEditorComponent implements OnInit {
  private readonly nzModalData = inject(NZ_MODAL_DATA);
  private readonly formGeneratorService = inject(FormGenerator2Service);

  @Input() set formControls(formControls: GeneratedFormControl[]) {
    this.formGeneratorService.formControls = formControls;
    const [formControl] = formControls;
    try {
      const chartData = JSON.parse(formControl.value);
      this.chartData.set(chartData);
      if (chartData?.type) {
        this.selectedTabIndex = 1;
      }
    } catch {
      /* empty */
    }
  }

  /**
   * The current chart data state.
   */
  readonly chartData = signal<KesmaChartData>({
    labels: [],
    datasets: [
      {
        data: [],
      },
    ],
    type: null,
  });
  /**
   * Chart configuration based on current chart type.
   */
  readonly chartConfig = computed(() => {
    return CHART_CONFIG?.[this.chartData().type] ?? {};
  });
  /**
   * Available chart types for user selection.
   */
  readonly AVAILABLE_CHART_TYPES = AVAILABLE_CHART_TYPES;

  selectedTabIndex = 0;

  constructor() {
    effect(() => {
      const [formControl] = this.formGeneratorService.formControls;
      const newFormControl = {
        ...formControl,
        value: JSON.stringify(this.chartData()),
      } as GeneratedFormControl;
      this.formGeneratorService.setFormControlByIndex(newFormControl, 0);
    });
  }

  ngOnInit(): void {
    Object.keys(this.nzModalData).forEach((key) => {
      this[key] = this.nzModalData[key];
    });
  }

  get formControls(): GeneratedFormControl[] {
    return this.formGeneratorService.formControls;
  }

  handleTypeChange(newType: ChartType): void {
    // First, we need to modify the chart type so that the chartConfig()
    // called by the populateDatasetFields function already includes the newly added type.
    this.chartData.update((chartData) => ({
      ...chartData,
      type: newType,
    }));
    this.chartData.update((chartData) => ({
      ...chartData,
      datasets: [{ data: [] }].map((dataset) => this.populateDatasetFields(dataset)),
    }));
    this.selectedTabIndex = 1;
  }

  handleLabelChange(labels: string[] | unknown[]): void {
    this.chartData.update((chartData) => ({
      ...chartData,
      datasets: chartData.datasets.map((dataset) => this.populateDatasetFields(dataset, labels)),
      labels,
    }));
  }

  addNewDataset(): void {
    this.chartData.update((chartData) => ({
      ...chartData,
      datasets: [...chartData.datasets, this.populateDatasetFields({ data: [] })],
    }));
  }

  deleteDataset(datasetIndex: number): void {
    this.chartData.update((chartData) => ({
      ...chartData,
      datasets: chartData.datasets.filter((_dataset, index: number) => index !== datasetIndex),
    }));
  }

  triggerChartUpdate(): void {
    this.chartData.update((chartData) => ({ ...chartData }));
  }

  private populateDatasetFields(dataset: ChartDataset, labels = this.chartData().labels): ChartDataset {
    const { hasBorderColor, isBackgroundColorNotArray } = this.chartConfig();
    return {
      ...dataset,
      label: dataset?.label ?? '',
      data: Array(labels.length)
        .fill(null)
        .map((_item, index: number) => {
          return dataset.data.at(index) || 1;
        }),
      backgroundColor: !isBackgroundColorNotArray
        ? Array(labels.length || 1)
            .fill(null)
            .map((_item, index: number) => {
              return dataset?.backgroundColor?.[index] || PRIMARY_COLOR;
            })
        : dataset?.backgroundColor || PRIMARY_COLOR,
      ...(hasBorderColor
        ? {
            borderColor: String(dataset?.borderColor || PRIMARY_COLOR),
          }
        : {}),
    } as ChartDataset;
  }
}
