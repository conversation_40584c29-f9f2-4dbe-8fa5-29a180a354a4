import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output, ViewContainerRef } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { NzModalService } from 'ng-zorro-antd/modal';
import { GeneratedFormControl, GeneratedFormControlWithSourceData } from '../../../definitions/form-generator-adapters.definitions';
import { ComponentFormControl } from '../../../definitions/form-generator-body-adapters.definitions';
import { FormGeneratorComponent } from '../../form-generator/form-generator.component';
import { CustomComponentInteractions } from '@shared/modules/form-generator2/interactions/custom-component.interactions';
import { FormControlListener } from '@shared/modules/form-generator2/definitions/form-control-interaction.definitions';
import { ChartEditorComponent } from '@shared/modules/form-generator2/components/chart-editor/chart-editor.component';
import { ModalOptions } from 'ng-zorro-antd/modal/modal-types';
import { ArticleBodyType } from '@trendency/kesma-ui';

@Component({
  selector: 'app-custom-component',
  templateUrl: 'custom-component.component.html',
  styleUrls: ['./custom-component.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [CustomComponentInteractions],
  standalone: false,
})
export class CustomComponentComponent implements OnInit {
  // eslint-disable-next-line @angular-eslint/no-input-rename
  @Input('componenttype') componentType: string;

  // eslint-disable-next-line @angular-eslint/no-input-rename
  @Input('iseditable') set isEditableAttribute(value: 'true' | 'false') {
    this._isEditable = value === 'true' ? true : false;
  }

  // eslint-disable-next-line @angular-eslint/no-input-rename
  @Input('isdisabled') set isDisabledAttribute(value: 'true' | 'false') {
    this._isDisabled = value === 'true' ? true : false;
  }

  // eslint-disable-next-line @angular-eslint/no-input-rename
  @Input('innerformcontrols') set innerFormControlsAttribute(value: string) {
    try {
      if (typeof value === 'object') {
        this._innerFormControls = value;
      } else {
        this._innerFormControls = JSON.parse(value);
        if (Array.isArray(this._innerFormControls)) {
          this._innerFormControls = this._innerFormControls.filter((n) => !!n);
        }
      }
    } catch (error) {
      this._innerFormControls = [];
    }
  }

  @Output() valueChange: EventEmitter<GeneratedFormControl[]> = new EventEmitter<GeneratedFormControl[]>();
  @Output() delete: EventEmitter<void> = new EventEmitter<void>();

  private _isDisabled: boolean = false;
  private _isEditable: boolean = true;
  private _innerFormControls: GeneratedFormControl[] = [];

  public get isDisabled(): boolean {
    return this._isDisabled;
  }

  public get isEditable(): boolean {
    return this._isEditable;
  }

  public get selectedValue(): ComponentFormControl['value'][] {
    return this._innerFormControls.map((c) => c?.value as ComponentFormControl['value']);
  }

  constructor(
    private readonly translate: TranslateService,
    private readonly modal: NzModalService,
    private readonly viewContainerRef: ViewContainerRef,
    private readonly cdr: ChangeDetectorRef,
    private readonly customComponentInteraction: CustomComponentInteractions
  ) {}

  ngOnInit() {}

  onEdit() {
    this.openEditModal();
  }

  onDelete() {
    this.delete.emit();
  }

  mapToDisplayProperty(data: ComponentFormControl['value'] | [], index: number) {
    const source = (this._innerFormControls[index] as GeneratedFormControlWithSourceData)?.sourceData;
    const displayProperty = source?.displayProperty;

    if (this.componentType === ArticleBodyType.GraphData) {
      try {
        const value = JSON.parse(String(data));
        return value?.type;
      } catch {
        return null;
      }
    }

    if (!displayProperty || typeof data?.[0] !== 'object') {
      return data;
    }

    return (data as ComponentFormControl['value'][])?.map((item) => item?.[displayProperty]);
  }

  private openEditModal() {
    const modal = this.modal.create({
      nzTitle: 'Szerkesztés',
      ...this.getComponentByType(),
      nzViewContainerRef: this.viewContainerRef,
      nzData: {
        formControls: this._innerFormControls.map((control) => {
          return {
            ...control,
            interactionListeners: this.getInteractionListenersToFormControl(control, this._innerFormControls),
          };
        }),
        shouldAutoSave: false,
      },
      nzFooter: [
        {
          label: this.translate.instant('CMS.cancel'),
          type: 'default',
          onClick: () => {
            modal.destroy();
          },
        },
        {
          label: this.translate.instant('CMS.save'),
          type: 'primary',
          onClick: () => {
            modal.destroy();
            const formGeneratorComponent: FormGeneratorComponent = modal.getContentComponent();

            formGeneratorComponent.formControls.forEach((formControl: GeneratedFormControl, index: number) => {
              const selectedValue = formGeneratorComponent.formControls[index]?.value as ComponentFormControl['value'];
              (this._innerFormControls[index] as any) = {
                ...this._innerFormControls[index],
                value: selectedValue,
              };
            });
            this.valueChange.emit(formGeneratorComponent.formControls);
            this.cdr.detectChanges();
          },
        },
      ],
    });
  }

  private getInteractionListenersToFormControl(formControl: GeneratedFormControl, formControls: GeneratedFormControl[]): FormControlListener[] {
    return [
      ...this.customComponentInteraction.interactions,
      // Other interactions can be added here in the future...
    ]
      .filter((interaction) => {
        return Array.isArray(interaction.formControlNames)
          ? interaction.formControlNames.includes(formControl.key)
          : interaction.formControlNames === formControl.key;
      })
      .map((interaction) => interaction.listeners)
      .flat()
      .map((listener) => ({ ...listener, formControls }));
  }

  private getComponentByType(): Partial<ModalOptions> {
    switch (this.componentType) {
      case ArticleBodyType.GraphData:
        return {
          nzContent: ChartEditorComponent,
          nzWidth: '80%',
        };
    }
    return {
      nzContent: FormGeneratorComponent,
    };
  }
}
