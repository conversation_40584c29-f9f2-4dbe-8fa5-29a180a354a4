<section tabindex="0" class="component-form-control-adapter">
  <span class="component-form-control-adapter-name">
    [ {{ componentType | translate }} ]
    <span class="separated-list comma" *ngIf="isEditable && selectedValue?.length > 0 && selectedValue[0]">
      - (<span *ngFor="let value of selectedValue; index as index">{{
        $any(value)?.fileName || (value?.title | translate) || value?.['name'] || value?.['caption'] || ($any(value)?.length > 0 ? mapToDisplayProperty(value, index) : null)
      }}</span
      >)
    </span>
  </span>

  <div class="component-form-control-adapter-buttons" *ngIf="!isDisabled">
    <i *ngIf="isEditable" data-edit-button nz-icon nzType="edit" nzTheme="fill" nz-tooltip nzTooltipTitle="Szerkesztés" (click)="onEdit()"> </i>
    <i *ngIf="componentType !== 'Paywall.Paywall'" nz-icon nzType="delete" nzTheme="fill" nz-tooltip nzTooltipTitle="Törlés" (click)="onDelete()"> </i>
  </div>
</section>

<div class="selected-value">
  {{ selectedValue | json }}
</div>
