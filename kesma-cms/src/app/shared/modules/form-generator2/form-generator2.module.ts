import { CommonModule } from '@angular/common';
import { Injector, NgModule } from '@angular/core';
import { UtilService } from '@trendency/kesma-core';
import { SharedModule } from '../../shared.module';
import { FormControlsModule } from '../form-controls/form-controls.module';
import { CheckboxFormControlAdapterComponent } from './components/adapters/checkbox-form-control-adapter/checkbox-form-control-adapter.component';
import { ComponentFormControlAdapterComponent } from './components/adapters/component-form-control-adapter/component-form-control-adapter.component';
import { ContributorsSelectFormControlAdapterComponent } from './components/adapters/contributors-select-form-control-adapter/contributors-select-form-control-adapter.component';
import { DatetimeFormControlAdapterComponent } from './components/adapters/datetime-form-control-adapter/datetime-form-control-adapter.component';
import { HtmlFormControlAdapterComponent } from './components/adapters/html-form-control-adapter/html-form-control-adapter.component';
import { ImageFormControlAdapterComponent } from './components/adapters/image-form-control-adapter/image-form-control-adapter/image-form-control-adapter.component';
import { NumberFormControlAdapterComponent } from './components/adapters/number-form-control-adapter/number-form-control-adapter.component';
import { RadioButtonFormControlAdapterComponent } from './components/adapters/radio-button-form-control-adapter/radio-button-form-control-adapter.component';
import { RadioGroupFormControlAdapterComponent } from './components/adapters/radio-group-form-control-adapter/radio-group-form-control-adapter.component';
import { TranslateOptionsLabelPipe } from './components/adapters/radio-group-form-control-adapter/translate-options-label.pipe';
import { SelectFormControlAdapterComponent } from './components/adapters/select-form-control-adapter/select-form-control-adapter.component';
import { TextFormControlAdapterComponent } from './components/adapters/text-form-control-adapter/text-form-control-adapter.component';
import { FormControlContextMenuComponent } from './components/form-control-menu/form-control-context-menu/form-control-context-menu.component';
import { FormControlMenuDirective } from './components/form-control-menu/form-control-menu.directive';
import { FormControlToolbarMenuComponent } from './components/form-control-menu/form-control-toolbar-menu/form-control-toolbar-menu.component';
import { FormGeneratorBodyComponent } from './components/form-generator-body/form-generator-body.component';
import { FormGeneratorComponent } from './components/form-generator/form-generator.component';
import { LayoutGeneratorComponent } from './components/layout-generator/layout-generator.component';
import { ComponentFormControlValueTitlePipe } from './pipes/component-form-control-value-title.pipe';
import { ConvertContentDataToBodyFormControlsPipe } from './pipes/convert-content-data-to-body-form-controls.pipe';
import { ConvertContentDataToFormControlsPipe } from './pipes/convert-content-data-to-form-controls.pipe';
import { ConvertFormControlsToContentDataPipe } from './pipes/convert-form-controls-to-content-data.pipe';
import { CheckboxGroupFormControlAdapterComponent } from './components/adapters/checkbox-group-form-control-adapter/checkbox-group-form-control-adapter.component';
import { TranslateCheckboxLabelPipe } from './components/adapters/checkbox-group-form-control-adapter/translate-checkbox-label.pipe';
import {
  AddressFormControlTypeGuardPipe,
  CheckboxFormControlTypeGuardPipe,
  CheckboxGroupFormControlTypeGuardPipe,
  ComponentFormControlTypeGuardPipe,
  ContributorsSelectFormControlTypeGuardPipe,
  DateFormControlTypeGuardPipe,
  DatePickerFormControlTypeGuardPipe,
  DatetimeFormControlTypeGuardPipe,
  DatetimeRangeFormControlTypeGuardPipe,
  EadvertFormControlTypeGuardPipe,
  HtmlFormControlTypeGuardPipe,
  ImageFormControlTypeGuardPipe,
  NumberFormControlTypeGuardPipe,
  OpeningHoursFormControlTypeGuardPipe,
  OtherTimesFormControlTypeGuardPipe,
  PasswordFormControlTypeGuardPipe,
  RadioButtonFormControlTypeGuardPipe,
  RadioGroupFormControlTypeGuardPipe,
  SelectFormControlTypeGuardPipe,
  SelectHighlightedImageFormControlTypeGuardPipe,
  TableFormControlTypeGuardPipe,
  TextFormControlTypeGuardPipe,
} from './pipes/form-control-type-guard.pipe';
import { BackendTreeToTreePipe } from './pipes/backend-tree-to-tree.pipe';
import { EadvertFormControlAdapterComponent } from './components/adapters/eadvert-form-control-adapter/eadvert-form-control-adapter.component';
import { KpiStatusComponent } from './components/kpi-status/kpi-status.component';
import { TableFormControlAdapterComponent } from './components/adapters/table-form-control-adapter/table-form-control-adapter.component';
import { FormGeneratorBodySingleComponent } from './components/form-generator-body-single/form-generator-body-single.component';
import { AvailableComponentFilterPipe } from './pipes/available-component-filter.pipe';
import { CustomComponentComponent } from './components/form-generator-body-single/custom-component/custom-component.component';
import { createCustomElement } from '@angular/elements';

import { MediaStoreModule } from 'src/app/media-store/media-store.module';
import { OpeningHoursFormControlAdapterComponent } from './components/adapters/opening-hours-form-control-adapter/opening-hours-form-control-adapter.component';
import { AddressFormControlAdapterComponent } from './components/adapters/address-form-control-adapter/address-form-control-adapter.component';
import { OtherTimesFormControlAdapterComponent } from './components/adapters/other-times-form-control-adapter/other-times-form-control-adapter.component';
import { SelectHighlightedImageFormControlAdapterComponent } from './components/adapters/select-highlighted-image-form-control-adapter/select-highlighted-image-form-control-adapter.component';
import { PasswordFormControlAdapterComponent } from './components/adapters/password-form-control-adapter/password-form-control-adapter.component';
import { DatetimeRangeFormControlAdapterComponent } from './components/adapters/datetime-range-form-control-adapter/datetime-range-form-control-adapter.component';
import { ChartEditorComponent } from '@shared/modules/form-generator2/components/chart-editor/chart-editor.component';

const FORM_CONTROL_ADAPTERS = [
  TextFormControlAdapterComponent,
  NumberFormControlAdapterComponent,
  SelectFormControlAdapterComponent,
  HtmlFormControlAdapterComponent,
  ComponentFormControlAdapterComponent,
  CheckboxFormControlAdapterComponent,
  ContributorsSelectFormControlAdapterComponent,
  DatetimeFormControlAdapterComponent,
  RadioButtonFormControlAdapterComponent,
  RadioGroupFormControlAdapterComponent,
  CheckboxGroupFormControlAdapterComponent,
  ImageFormControlAdapterComponent,
  EadvertFormControlAdapterComponent,
  TableFormControlAdapterComponent,
  OtherTimesFormControlAdapterComponent,
];

const FORM_CONTROL_TYPE_GUARD_PIPES = [
  TextFormControlTypeGuardPipe,
  PasswordFormControlTypeGuardPipe,
  NumberFormControlTypeGuardPipe,
  SelectFormControlTypeGuardPipe,
  CheckboxFormControlTypeGuardPipe,
  HtmlFormControlTypeGuardPipe,
  ComponentFormControlTypeGuardPipe,
  ContributorsSelectFormControlTypeGuardPipe,
  DatetimeFormControlTypeGuardPipe,
  DatetimeRangeFormControlTypeGuardPipe,
  DateFormControlTypeGuardPipe,
  RadioButtonFormControlTypeGuardPipe,
  RadioGroupFormControlTypeGuardPipe,
  CheckboxGroupFormControlTypeGuardPipe,
  ImageFormControlTypeGuardPipe,
  EadvertFormControlTypeGuardPipe,
  TableFormControlTypeGuardPipe,
  OpeningHoursFormControlTypeGuardPipe,
  AddressFormControlTypeGuardPipe,
  DatePickerFormControlTypeGuardPipe,
  OtherTimesFormControlTypeGuardPipe,
  SelectHighlightedImageFormControlTypeGuardPipe,
];

const FORM_CONTROL_MENU = [FormControlContextMenuComponent, FormControlToolbarMenuComponent, FormControlMenuDirective];

@NgModule({
  declarations: [
    ...FORM_CONTROL_ADAPTERS,
    ...FORM_CONTROL_TYPE_GUARD_PIPES,
    ...FORM_CONTROL_MENU,
    FormGeneratorBodyComponent,
    FormGeneratorBodySingleComponent,
    FormGeneratorComponent,
    LayoutGeneratorComponent,
    ConvertContentDataToFormControlsPipe,
    ConvertContentDataToBodyFormControlsPipe,
    ConvertFormControlsToContentDataPipe,
    ComponentFormControlValueTitlePipe,
    TranslateOptionsLabelPipe,
    TranslateCheckboxLabelPipe,
    KpiStatusComponent,
    BackendTreeToTreePipe,
    AvailableComponentFilterPipe,
    CustomComponentComponent,
    OpeningHoursFormControlAdapterComponent,
    AddressFormControlAdapterComponent,
    SelectHighlightedImageFormControlAdapterComponent,
    PasswordFormControlAdapterComponent,
    DatetimeRangeFormControlAdapterComponent,
  ],
  imports: [CommonModule, FormControlsModule, SharedModule, MediaStoreModule, ChartEditorComponent],
  exports: [
    FormGeneratorBodyComponent,
    FormGeneratorBodySingleComponent,
    FormGeneratorComponent,
    LayoutGeneratorComponent,
    ConvertContentDataToFormControlsPipe,
    ConvertContentDataToBodyFormControlsPipe,
    ConvertFormControlsToContentDataPipe,
    KpiStatusComponent,
    CustomComponentComponent,
  ],
})
export class FormGenerator2Module {
  constructor(
    private injector: Injector,
    private readonly utilService: UtilService
  ) {
    this.registerCustomComponentWebComponent();
  }

  private registerCustomComponentWebComponent() {
    const elementConstructor = createCustomElement(CustomComponentComponent, {
      injector: this.injector,
    });
    const isWebComponentAlreadyDefined = this.utilService.isBrowser() ? window.customElements.get('custom-editor-component') : true;

    if (isWebComponentAlreadyDefined) {
      return;
    }
    customElements.define('custom-editor-component', elementConstructor);
  }
}
