import { PageableSelectLoader } from '@shared/definitions/pageable-select-loader.definitions';
import { BehaviorSubject, EMPTY, Observable, Subject, take, tap } from 'rxjs';
import { debounceTime, filter, map, switchMap } from 'rxjs/operators';
import { IHttpOptions } from '@external/http';
import { HttpParams } from '@angular/common/http';

export class SelectPageableHttpLoader<T> implements PageableSelectLoader<T> {
  private _currentPage$ = new BehaviorSubject<number>(0);
  private _data$ = new BehaviorSubject<T[]>([]);
  private _isLoading$ = new BehaviorSubject<boolean>(false);
  private _pageLimit = 20;
  private _pageMax = Number.MAX_SAFE_INTEGER;
  private _rowAllCount = 0;
  private previousSearchTerm?: string;
  private _res$ = new BehaviorSubject<any>(null);
  private _searchTerm$ = new BehaviorSubject<string>('');
  private initialData: T[] = [];

  currentPage$ = this._currentPage$.asObservable();
  /**
   * Contains all the data items for the select. You can use this observable to show select options.
   */
  data$: Observable<T[]> = this._data$.asObservable();

  /**
   * Shows whether a we are currently waiting for a request to finish, or not.
   */
  isLoading$ = this._isLoading$.asObservable();

  /**
   * Contains the meta property from the last received API response.
   * @private
   */
  latestMeta$ = this._res$.asObservable().pipe(
    filter((res) => !!res),
    map((res) => res.meta)
  );

  searchTerm$ = this._searchTerm$.asObservable();

  constructor(private sourceRequest: (options: IHttpOptions) => Observable<any>) {
    this._searchTerm$
      .pipe(
        debounceTime(500),
        switchMap((searchValue: string) => {
          if (searchValue.length === 0 && !this.previousSearchTerm) {
            return EMPTY;
          }

          if (!searchValue) {
            this.init();
            return EMPTY;
          }
          this.previousSearchTerm = searchValue;
          this._currentPage$.next(0);

          return this.fetchSource(this.getHttpParams());
        })
      )
      .subscribe((items: T[]) => {
        this._data$.next([...items]);
      });
  }
  removeDuplicates(array: T[]) {
    return array.filter((item, index, arr) => index === arr.findIndex((other) => this.compareFn(item, other)));
  }

  readonly compareFn = (o1: T, o2: T) => o1 && o2 && o1['id'] === o2['id'];

  /**
   * Closes all observables to also trigger automatic unsubscription inside the current class instance.
   * It is recommended to use this function to prevent memory leaks.
   */
  close() {
    this._data$.complete();
    this._searchTerm$.complete();
    this._currentPage$.complete();
    this._res$.complete();
    this._isLoading$.complete();
  }

  /**
   * Returns the zero-based index of the current page that will be requested.
   */
  currentPage() {
    return this._currentPage$.value;
  }

  /**
   * Fetches the supplied API endpoint for items.
   * @param params The used HTTP params for making the request (for example: page limit, global_filter, etc)
   * @private
   */
  private fetchSource(params: HttpParams | { [param: string]: string | string[] } = null): Observable<T[]> {
    this._isLoading$.next(true);
    return this.sourceRequest({ params }).pipe(
      take(1),
      tap((res) => {
        this._res$.next(res);
        if (res.meta?.limitable?.hasOwnProperty('pageMax')) {
          this._pageMax = res.meta.limitable.pageMax;
        }
        if (res.meta?.limitable?.hasOwnProperty('rowAllCount')) {
          this._rowAllCount = res.meta.limitable.rowAllCount;
        }
      }),
      map((res) => res.data),
      tap(() => {
        this._isLoading$.next(false);
        this._currentPage$.next(this.currentPage() + 1);
      })
    );
  }

  /**
   * Initializes the loader and makes the first request for the supplied API endpoint.
   */
  init(initialData?: T[]) {
    if (initialData !== undefined && initialData !== null) {
      this.initialData = initialData;
    }
    if (this.initialData?.length > 0) {
      this._data$.next(this.initialData);
    }
    this._currentPage$.next(0);
    this.fetchSource(this.getHttpParams()).subscribe((source) => {
      this._data$.next(this.removeDuplicates([...this.initialData, ...source]));
    });
  }

  /**
   * Triggers the loader to load more items if it is possible.
   */
  loadMore(): void {
    if (this.currentPage() > this.pageMax()) {
      // Cannot extend source anymore...
      return;
    }

    this._isLoading$.next(true);

    this.fetchSource(this.getHttpParams()).subscribe((source) => {
      this._data$.next(this.removeDuplicates([...this._data$.value, ...source]));
    });
  }

  /**
   * Return the number of items that are on a given page.
   */
  pageLimit() {
    return this._pageLimit;
  }

  /**
   * Returns how many pages can be requested for the given API endpoint, considering the page size.
   * This can be only used after initializing and making the first request, as this is supplied by the API meta.
   * Note that after changing the pageLimit, this value won't be changed until you make a new request with the loader.
   */
  pageMax() {
    return this._pageMax;
  }

  rowAllCount(): number {
    return this._rowAllCount;
  }

  /**
   * Triggers a new search for the supplied search term.
   * The search happens asynchronously with a 500ms debounce time.
   * Refer the ngOnInit function to see how the observable is being handled.
   * @param searchValue
   */
  search(searchValue: string) {
    this._searchTerm$.next(searchValue);
  }

  /**
   * Returns the search term that was used for the last request.
   */
  searchTerm() {
    return this._searchTerm$.value;
  }

  /**
   * Sets the page limit for the subsequential requests. You can also use this function to set the initial page limit
   * before calling the init() method.
   * @param limit
   */
  setPageLimit(limit: number): number {
    this._pageLimit = limit;
    return this._pageLimit;
  }

  clear(): void {
    this._data$.next([]);
  }

  private getHttpParams(): { [param: string]: string } {
    return {
      page_limit: this.currentPage().toString(),
      rowCount_limit: this.pageLimit().toString(),
      ...(this.searchTerm ? { global_filter: this.searchTerm().toString() } : {}),
    };
  }
}
