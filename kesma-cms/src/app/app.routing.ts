import { Routes } from '@angular/router';
import { LoginComponent } from './core/modules/login/login.component';
import { AdminComponent } from './core/modules/admin/admin.component';
import { ErrorPageComponent } from './core/modules/error-page/error-page.component';
import { AuthGuard } from './core/guards/auth.guard';
import { InitResolver } from './core/resolvers/init.resolver';
import { DomainSelectComponent } from './core/modules/domain-select/domain-select.component';
import { LockedPageComponent } from '@shared/modules/locked-page/locked-page.component';
import { MosaicEditorComponent } from './media-store/mosaic-editor/mosaic-editor.component';
import { DomainChangedSinceReloadGuard } from './core/guards/domain-changed-since-reload.guard';
import { highlightedItemsResolver } from './modules/highlighted-items/highlighted-items.resolver';
import { HighlightedItemsService } from './modules/highlighted-items/highlighted-items.service';

export const appRoutes: Routes = [
  {
    path: '',
    component: LoginComponent,
    pathMatch: 'full',
  },
  {
    path: 'password-recovery',
    loadChildren: () => import('src/app/core/modules/password-recovery/password-recovery.module').then((m) => m.PasswordRecoveryModule),
  },
  {
    path: 'domain-select',
    component: DomainSelectComponent,
    canActivate: [AuthGuard],
    resolve: { init: InitResolver },
  },
  {
    path: 'admin',
    component: AdminComponent,
    canActivate: [AuthGuard, DomainChangedSinceReloadGuard],
    resolve: { init: InitResolver },
    children: [
      {
        path: 'layout-cloner',
        loadChildren: () => import('./layout-cloner/layout-cloner.routing').then((m) => m.layoutClonerRouting),
      },
      {
        path: 'best-practices',
        loadChildren: () => import('./modules/best-practice/best-practice.module').then((m) => m.BestPracticeModule),
      },
      // MME maestro-k nem lesznek külön kezelve (lásd: KESMA-11665), ezért kikerült a menüpont
      // {
      //   path: 'maestros',
      //   loadChildren: () =>
      //     import("./modules/maestro/maestro.module").then(
      //       (m) => m.MaestroModule,
      //     )
      // },
      {
        path: 'allergens',
        loadChildren: () => import('./modules/allergen/allergen.module').then((m) => m.AllergenModule),
      },
      {
        path: 'recipes',
        loadChildren: () => import('./modules/recipe/recipe.module').then((m) => m.RecipeModule),
      },
      {
        path: 'recipe-categories',
        loadChildren: () => import('./modules/recipe-category/recipe-category.module').then((m) => m.RecipeCategoryModule),
      },
      {
        path: 'ingredients',
        loadChildren: () => import('./modules/ingredient/ingredient.module').then((m) => m.IngredientModule),
      },
      {
        path: 'weekly-menus',
        loadChildren: () => import('./modules/weekly-menu/weekly-menu.module').then((m) => m.WeeklyMenuModule),
      },
      {
        path: 'selections',
        loadChildren: () => import('./modules/selection/selection.module').then((m) => m.SelectionModule),
      },
      {
        path: 'phases',
        loadChildren: () => import('./modules/phase/phase.module').then((m) => m.PhaseModule),
      },
      {
        path: 'competitions',
        loadChildren: () => import('./modules/competition/competition.module').then((m) => m.CompetitionModule),
      },
      {
        path: 'tv-stations',
        loadChildren: () => import('./modules/tv-station/tv-station.module').then((m) => m.TvStationModule),
      },
      {
        path: 'players',
        loadChildren: () => import('./modules/player/player.module').then((m) => m.PlayerModule),
      },
      {
        path: 'teams',
        loadChildren: () => import('./modules/team/team.module').then((m) => m.TeamModule),
      },
      {
        path: 'seasons',
        loadChildren: () => import('./modules/season/season.module').then((m) => m.SeasonModule),
      },
      {
        path: 'staff',
        loadChildren: () => import('./modules/staff/staff.module').then((m) => m.StaffModule),
      },
      {
        path: 'staff-positions',
        loadChildren: () => import('./modules/staff-position/staff-position.module').then((m) => m.StaffPositionModule),
      },
      {
        path: 'player-positions',
        loadChildren: () => import('./modules/player-position/player-position.module').then((m) => m.PlayerPositionModule),
      },
      {
        path: 'schedules',
        loadChildren: () => import('./modules/schedule/schedule.module').then((m) => m.ScheduleModule),
      },
      {
        path: 'schedule-events',
        loadChildren: () => import('./modules/schedule-event/schedule-event.module').then((m) => m.ScheduleEventModule),
      },
      {
        path: 'live-sports',
        loadChildren: () => import('./modules/live-sport/live-sport.module').then((m) => m.LiveSportModule),
      },
      {
        path: 'facilities',
        loadChildren: () => import('./modules/facility/facility.module').then((m) => m.FacilityModule),
      },
      {
        path: 'olympics-medals',
        loadChildren: () => import('./modules/olympics-medals/olympics-medals.routes').then((m) => m.OLYMPICS_MEDALS_ROUTES),
      },
      {
        path: 'athletes',
        loadChildren: () => import('./modules/athletes/athletes.module').then((m) => m.AthletesModule),
      },
      {
        path: 'mosaic',
        component: MosaicEditorComponent,
      },
      {
        path: 'perceptions',
        loadChildren: () => import('./modules/detections/detections.module').then((m) => m.DetectionsModule),
      },
      {
        path: 'stations',
        loadChildren: () => import('./modules/stations/stations.module').then((m) => m.StationsModule),
      },
      {
        path: 'perceptions-banned-words',
        loadChildren: () => import('src/app/modules/comments-banned-words/comments-banned-words.module').then((m) => m.CommentsBannedWordsModule),
      },
      {
        path: 'orvosmet',
        loadChildren: () => import('./modules/orvosmet/orvosmet.module').then((m) => m.OrvosmetModule),
      },
      {
        path: 'seo-meta-urls',
        loadChildren: () => import('./modules/seo-meta-urls/seo-meta-urls.routes').then((m) => m.SeoMetaUrlsRoutes),
      },
      {
        path: 'seo-meta-robots',
        loadChildren: () => import('./modules/seo-meta-robots/seo-meta-robots.routes').then((m) => m.SeoMetaRobotsRoutes),
      },
      {
        path: 'top-ranking-glossary',
        loadChildren: () => import('./modules/top-ranking-glossary/top-ranking-glossary.routes').then((m) => m.TopRankingGlossaryRoutes),
      },
      {
        path: 'gastro-experience',
        loadChildren: () => import('@modules/gastro-experience/gastro-experience.routes').then((m) => m.GastroExperienceRoutes),
      },
      {
        path: 'gastro-experience-categories',
        loadChildren: () =>
          import('./modules/gastro-experience-categories/gastro-experience-categories.routes').then((m) => m.GastroExperienceCategoriesRoutes),
      },
      {
        path: 'gastro-experience-occasion',
        loadChildren: () => import('./modules/gastro-experience-occasion/gastro-experience-occasion.routes').then((m) => m.GastroExperienceOccasionRoutes),
      },
      {
        path: 'gastro-purchases',
        loadChildren: () => import('./modules/gastro-purchases/gastro-purchases.routes').then((m) => m.GastroPurchasesRoutes),
      },
      {
        path: 'maps',
        loadChildren: () => import('./modules/maps/maps.module').then((m) => m.MapsModule),
      },
      {
        path: 'popup-notifier',
        loadChildren: () => import('./modules/popup-notifier/popup-notifier.module').then((m) => m.PopupNotifierModule),
      },
      {
        path: 'subscriptions_addressee',
        loadChildren: () => import('./modules/subscriptions/export-addressee/export-addressee.module').then((m) => m.ExportAddresseeModule),
      },
      {
        path: 'marketing-campaign',
        loadChildren: () => import('./modules/subscriptions/marketing-campaign/marketing-campaign.module').then((m) => m.MarketingCampaignModule),
      },
      {
        path: 'sport-match',
        loadChildren: () => import('./modules/sport-match/sport-match.module').then((m) => m.SportMatchModule),
      },
      {
        path: 'subscriptions-calendar',
        loadChildren: () => import('./modules/subscriptions/calendar/calendar.module').then((m) => m.CalendarModule),
      },
      {
        path: 'subscriptions-product',
        loadChildren: () => import('./modules/subscriptions/product/product.module').then((m) => m.ProductModule),
      },
      {
        path: 'subscriptions-subscriptions',
        loadChildren: () => import('./modules/subscriptions/subscriptions/list/subscriptions-list.module').then((m) => m.SubscriptionsListModule),
      },
      {
        path: 'subscriptions-export',
        loadChildren: () => import('./modules/subscriptions/export/export.module').then((m) => m.SubscriptionsExportModule),
      },
      {
        path: 'subscription-email-templates',
        loadChildren: () => import('./modules/subscriptions/email-template/email-template.module').then((m) => m.SubscriptionEmailTemplateModule),
      },
      {
        path: 'short-url',
        loadChildren: () => import('src/app/modules/short-url/short-url.module').then((m) => m.ShortUrlModule),
      },
      {
        path: 'dashboard',
        loadChildren: () => import('src/app/modules/dashboard/dashboard.module').then((m) => m.DashboardModule),
      },
      {
        path: 'forecast_external_users',
        loadChildren: () => import('src/app/modules/partners/partners-api/partners-api.module').then((m) => m.PartnersApiModule),
      },
      {
        path: 'automail',
        loadChildren: () => import('src/app/modules/partners/automail/automail.module').then((m) => m.AutomailModule),
      },
      {
        path: 'create-article-by-params',
        loadChildren: () => import('src/app/modules/news-reception-create/news-reception-create.module').then((m) => m.NewsReceptionCreateModule),
      },
      {
        path: 'cms-users',
        loadChildren: () => import('src/app/modules/cms-users/cms-users.module').then((m) => m.CmsUsersModule),
      },
      {
        path: 'portal-users',
        loadChildren: () => import('src/app/modules/portal-users/portal-users.module').then((m) => m.PortalUsersModule),
      },
      {
        path: 'contributors',
        loadChildren: () => import('src/app/modules/contributors/contributors.module').then((m) => m.ContributorsModule),
      },
      {
        path: 'public-authors',
        loadChildren: () => import('src/app/modules/public-authors/public-authors.module').then((m) => m.PublicAuthorsModule),
      },
      {
        path: 'users-statistics',
        loadChildren: () => import('src/app/modules/users-statistics/users-statistics.module').then((m) => m.UsersStatisticsModule),
      },
      {
        path: 'authors-performance',
        loadChildren: () => import('src/app/modules/authors-performance/authors-performance.module').then((m) => m.AuthorsPerformanceModule),
      },
      {
        path: 'columns-performance',
        loadChildren: () => import('src/app/modules/columns-performance/columns-performance.module').then((m) => m.ColumnsPerformanceModule),
      },
      {
        path: 'main-page',
        loadChildren: () => import('src/app/modules/home-pages/home-pages.module').then((m) => m.HomePagesModule),
      },
      {
        path: 'category-pages',
        loadChildren: () => import('src/app/modules/category-pages/category-pages.module').then((m) => m.CategoryPagesModule),
      },
      {
        path: 'articles',
        loadChildren: () => import('src/app/modules/thematic-articles/thematic-articles.module').then((m) => m.ThematicArticlesModule),
      },
      {
        path: 'branding-boxes',
        loadChildren: () => import('src/app/modules/branding-boxes/branding-boxes.module').then((m) => m.BrandingBoxesModule),
      },
      {
        path: 'import-xml',
        loadChildren: () => import('src/app/modules/import-print-xml/import-print-xml.module').then((m) => m.ImportPrintXmlModule),
      },
      {
        path: 'import-xml-approve',
        loadChildren: () => import('src/app/modules/import-print-approve/approve.module').then((m) => m.ApproveModule),
      },
      {
        path: 'static-pages',
        loadChildren: () => import('src/app/modules/static-pages/static-pages.module').then((m) => m.StaticPagesModule),
      },
      {
        path: 'categories',
        loadChildren: () => import('src/app/modules/categories/categories.module').then((m) => m.CategoriesModule),
      },
      {
        path: 'pr-tags',
        loadChildren: () => import('src/app/modules/pr-tags/pr-tags.module').then((m) => m.PrTagsModule),
      },
      {
        path: 'tags',
        loadChildren: () => import('src/app/modules/tags/tags.module').then((m) => m.TagsModule),
      },
      {
        path: 'regions',
        loadChildren: () => import('src/app/modules/regions/regions.module').then((m) => m.RegionsModule),
      },
      {
        path: 'sports',
        loadChildren: () => import('src/app/modules/sports/sports.module').then((m) => m.SportsModule),
      },
      {
        path: 'did-you-know',
        loadChildren: () => import('src/app/modules/did-you-know/did-you-know.module').then((m) => m.DidYouKnowModule),
      },
      {
        path: 'dossiers',
        loadChildren: () => import('src/app/modules/dossiers/dossiers.module').then((m) => m.DossiersModule),
      },
      {
        path: 'sponsorships',
        loadChildren: () => import('src/app/modules/sponsorships/sponsorships.module').then((m) => m.SponsorshipsModule),
      },
      {
        path: 'menu',
        loadChildren: () => import('src/app/modules/menu/menu.module').then((m) => m.MenuModule),
      },
      {
        path: 'languages',
        loadChildren: () => import('src/app/modules/languages/languages.module').then((m) => m.LanguagesModule),
      },
      {
        path: 'videos',
        loadChildren: () => import('src/app/modules/videos/videos.module').then((m) => m.VideosModule),
      },
      {
        path: 'podcasts',
        loadChildren: () => import('src/app/modules/podcasts/podcasts.module').then((m) => m.PodcastsModule),
      },
      {
        path: 'layout',
        loadChildren: () => import('src/app/modules/layout/layout.module').then((m) => m.LayoutModule),
      },
      {
        path: 'publications',
        loadChildren: () => import('src/app/modules/publications/publications.module').then((m) => m.PublicationsModule),
      },
      {
        path: 'advertisements',
        loadChildren: () => import('src/app/modules/advertisements/advertisements.module').then((m) => m.AdvertisementsModule),
      },
      {
        path: 'profile',
        loadChildren: () => import('src/app/modules/profile/profile.module').then((m) => m.ProfileModule),
      },
      {
        path: 'news-reception',
        loadChildren: () => import('src/app/modules/news-reception/news-reception.module').then((m) => m.NewsReceptionModule),
      },
      {
        path: 'program-recommendations',
        loadChildren: () => import('src/app/modules/programs/programs.module').then((m) => m.ProgramsModule),
      },
      {
        path: 'program-types',
        loadChildren: () => import('src/app/modules/program-types/program-types.module').then((m) => m.ProgramTypesModule),
      },
      {
        path: 'program-locations',
        loadChildren: () => import('src/app/modules/program-locations/program-locations.module').then((m) => m.ProgramLocationsModule),
      },
      {
        path: 'star-occupations',
        loadChildren: () => import('src/app/modules/star-occupations/star-occupations.module').then((m) => m.StarOccupationsModule),
      },
      {
        path: 'star-birthplaces',
        loadChildren: () => import('src/app/modules/star-birthplaces/star-birthplaces.module').then((m) => m.StarBirthplacesModule),
      },
      {
        path: 'star-awards',
        loadChildren: () => import('src/app/modules/star-awards/star-awards.module').then((m) => m.StarAwardsModule),
      },
      {
        path: 'star-stars',
        loadChildren: () => import('src/app/modules/star-stars/star-stars.module').then((m) => m.StarStarsModule),
        data: {
          imageType: 'starDictionary',
        },
      },
      {
        path: 'search-static',
        loadChildren: () => import('src/app/modules/search-static/search-static.module').then((m) => m.SearchStaticModule),
      },
      {
        path: 'marketplace',
        loadChildren: () => import('src/app/modules/marketplace/marketplace.module').then((m) => m.MarketplaceModule),
      },
      {
        path: 'marketplace-item',
        loadChildren: () => import('src/app/modules/marketplace-item/marketplace-item.module').then((m) => m.MarketplaceItemModule),
      },
      {
        path: 'multi-voting',
        loadChildren: () => import('src/app/modules/multi-voting/multi-voting.module').then((m) => m.MultiVotingModule),
      },
      {
        path: 'voting',
        loadChildren: () => import('src/app/modules/voting/voting.module').then((m) => m.VotingModule),
      },
      {
        path: 'extraordinary-notification',
        loadChildren: () =>
          import('src/app/modules/extraordinary-notification/extraordinary-notification.routing').then((m) => m.extraordinaryNotificationRoutes),
      },
      {
        path: 'entity-log',
        loadChildren: () => import('src/app/modules/entity-log/entity-log.module').then((m) => m.EntityLogModule),
      },
      {
        path: 'test-pages',
        loadChildren: () => import('src/app/test-pages/test-pages.module').then((m) => m.TestPagesModule),
      },
      {
        path: 'settings',
        loadChildren: () => import('src/app/modules/settings/settings.module').then((m) => m.SettingsModule),
      },
      {
        path: 'datastudio',
        loadChildren: () => import('src/app/modules/datastudio/datastudio.module').then((m) => m.DatastudioModule),
      },
      {
        path: 'portal-config',
        loadChildren: () => import('src/app/modules/portal-config/portal-config.module').then((m) => m.PortalConfigModule),
      },
      {
        path: 'role-groups',
        loadChildren: () => import('src/app/modules/role-groups/role-groups.module').then((m) => m.RoleGroupsModule),
      },
      {
        path: 'facebook-post',
        loadChildren: () => import('src/app/content-page-editor/components/facebook-share/facebook-share.module').then((m) => m.FacebookShareModule),
      },
      {
        path: 'quizzes',
        loadChildren: () => import('src/app/modules/quiz/quiz.module').then((m) => m.QuizModule),
      },
      {
        path: 'kpi-article-slot',
        loadChildren: () => import('src/app/modules/kpi-article-slot/kpi-article-slot.module').then((m) => m.KpiArticleSlotModule),
      },
      {
        path: 'article-network-slots',
        loadChildren: () => import('src/app/modules/article-network-slots/article-network-slots.module').then((m) => m.ArticleNetworkSlotsModule),
      },
      {
        path: 'voting-network-slots',
        loadChildren: () => import('src/app/modules/voting-network-slots/voting-network-slots.module').then((m) => m.VotingNetworkSlotsModule),
      },
      {
        path: 'games',
        loadChildren: () => import('src/app/modules/games/games.module').then((m) => m.GamesModule),
      },
      {
        path: 'journal_issue',
        loadChildren: () => import('src/app/modules/journals/journals.module').then((m) => m.JournalsModule),
      },
      {
        path: 'comments',
        loadChildren: () => import('src/app/modules/comments/comments.module').then((m) => m.CommentsModule),
      },
      {
        path: 'comment-reports',
        loadChildren: () => import('src/app/modules/comments-reports/comments-reports.module').then((m) => m.CommentsReportsModule),
      },
      {
        path: 'banned-words',
        loadChildren: () => import('src/app/modules/comments-banned-words/comments-banned-words.module').then((m) => m.CommentsBannedWordsModule),
      },
      {
        path: 'institutions',
        loadChildren: () => import('src/app/modules/institutions/institutions/institutions.module').then((m) => m.InstitutionsModule),
      },
      {
        path: 'institution-categories',
        loadChildren: () =>
          import('src/app/modules/institutions/institution-categories/institution-categories.module').then((m) => m.InstitutionCategoriesModule),
      },
      {
        path: 'dossier-network-slots',
        loadChildren: () => import('src/app/modules/dossier-network-slots/dossier-network-slots.module').then((m) => m.DossierNetworkSlotsModule),
      },
      {
        path: 'glossary',
        loadChildren: () => import('src/app/modules/glossary/glossary.module').then(({ GlossaryModule }) => GlossaryModule),
      },
      {
        path: 'content-json',
        loadChildren: () => import('src/app/content-json-editor/content-json-editor.module').then((m) => m.ContentJsonEditorModule),
      },
      {
        path: 'highlighted-items',
        loadComponent: () =>
          import('src/app/modules/highlighted-items/highlighted-items.component').then(({ HighlightedItemsComponent }) => HighlightedItemsComponent),
        providers: [HighlightedItemsService],
        resolve: {
          list: highlightedItemsResolver,
        },
      },
      {
        path: 'gallery',
        loadChildren: () => import('src/app/media/media-gallery/gallery-list.routing').then(({ galleryListRouting }) => galleryListRouting),
      },
      {
        path: 'star-gallery',
        loadChildren: () => import('src/app/media/media-gallery/gallery-list.routing').then(({ galleryListRouting }) => galleryListRouting),
        data: {
          imageType: 'starDictionary',
        },
      },
      {
        path: 'promotions',
        loadChildren: () => import('src/app/modules/promotions/promotions.routing').then((m) => m.promotionsRouting),
      },
      {
        path: 'quiz-categories',
        loadChildren: () => import('src/app/modules/quiz-categories/quiz-categories.routing').then((m) => m.quizCategoriesRouting),
      },
      {
        path: 'calendars',
        loadChildren: () => import('src/app/modules/calendars/calendars.routing').then((m) => m.calendarsRoutes),
      },
      {
        path: 'error',
        component: ErrorPageComponent,
      },
      {
        path: 'locked',
        component: LockedPageComponent,
      },
    ],
  },
  {
    path: '**',
    redirectTo: 'admin',
  },
];
