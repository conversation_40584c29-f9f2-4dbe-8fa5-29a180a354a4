import { computed, inject, Injectable, signal } from '@angular/core';
import { StorageService } from '@trendency/kesma-core';
import { TranslateService } from '@ngx-translate/core';
import { CleanHttpService } from '@core/services/clean-http.service';
import { ApiUrlCollection, IEnvironment } from 'src/environments/environment.definitions';
import { Observable } from 'rxjs';
import { BaseContentVariant } from '@core/api.definitons';
import { LayoutPageType } from '@modules/layout/layout-core.definitions';
import { DomainService } from '@core/modules/admin/services/domain.service';
import { map } from 'rxjs/operators';
import { LayoutApiService } from '@modules/layout/services/layout-api.service';
import { IHttpOptions } from '@external/http';
import { environment } from 'src/environments/environment';

const LANGUAGE_KEY = 'contentLang';
const STRUCTURE_KEY = 'draftVersion.structJson';
const CONTENT_KEY = 'draftVersion.rawContentJson';
const TEMPLATE_STRUCTURE_KEY = 'structJson';
const TOKEN_KEY = 'jwt_token';

type SourceLayout = {
  relatedDefinitionType?: string;
  id: string;
};

@Injectable()
export class LayoutCloner {
  private readonly layoutApi = inject(LayoutApiService);
  private readonly storageService = inject(StorageService);
  private readonly translateService = inject(TranslateService);
  private readonly cleanHttpService = inject(CleanHttpService);
  private readonly domainService = inject(DomainService);

  /**
   * Defines the security level of each environment regarding X-Auth-Token usage.
   * @safe: Tokens can be reused across environments in this category.
   * @sensitive: Tokens from or to these environments require manual override.
   */
  private readonly environmentTokenSecurityLevel: Record<string, 'safe' | 'sensitive'> = {
    local: 'safe',
    dev: 'safe',
    teszt: 'safe',
    prod: 'sensitive',
  };

  readonly layoutType = signal(LayoutPageType.HOME);
  readonly selectedDomain = signal<string | null>(this.domainService.currentDomain.info.portalHeader);
  readonly selectedEnvironment = signal<IEnvironment | null>(null);
  readonly authToken = signal<string | null>(null);

  readonly isTemplate = computed<boolean>(() => this.layoutType() === null);

  readonly useOverwrittenToken = computed(() => {
    const type = this.selectedEnvironment()?.type;
    return type && this.environmentTokenSecurityLevel[environment.type] !== this.environmentTokenSecurityLevel[type];
  });

  readonly headers = computed(() => ({
    portal: this.selectedDomain(),
    'X-Auth-Token': this.useOverwrittenToken() ? this.authToken() : this.storageService.getCookie(TOKEN_KEY),
  }));

  getCurrentLayouts$(options: IHttpOptions): Observable<BaseContentVariant[]> {
    return this.isTemplate() ? this.layoutApi.listBaseTemplates(options) : this.layoutApi.listCompleteTemplates(this.layoutType(), options);
  }

  getStructureBySelectedId$(layout: SourceLayout): Observable<BaseContentVariant> {
    const { relatedDefinitionType, id } = layout;
    return this.isTemplate()
      ? this.layoutApi.getBaseTemplate(relatedDefinitionType, id).pipe(map(({ data }) => data.find(({ key }) => key === TEMPLATE_STRUCTURE_KEY).value))
      : this.layoutApi.getCompleteLayout(this.layoutType(), id).pipe(map(({ data }) => data.find(({ key }) => key === STRUCTURE_KEY).value));
  }

  getTargetEnvLayouts$(options: IHttpOptions): Observable<{ data: BaseContentVariant[] }> {
    return this.cleanHttpService.get<{
      data: BaseContentVariant[];
    }>(`${this.getApi(this.selectedEnvironment())}/layout-editor/${this.isTemplate() ? 'templates' : this.layoutType() + '/layouts'}`, {
      params: options.params,
      headers: this.headers(),
    });
  }

  getStructureByTargetId$(structure: object, targetLayout: SourceLayout): Observable<BaseContentVariant[]> {
    const { relatedDefinitionType, id } = targetLayout;
    // We use cleanHttpService to avoid CORS issues.
    return this.cleanHttpService
      .get<{
        data: BaseContentVariant[];
      }>(`${this.getApi()}/layout-editor/${this.isTemplate() ? `${relatedDefinitionType}/template/${id}` : `${this.layoutType()}/layout/${id}`}`, {
        headers: this.headers(),
      })
      .pipe(
        map(({ data }) => data),
        map((data) =>
          data.map((entry) => {
            switch (entry.key) {
              case CONTENT_KEY:
                return {
                  ...entry,
                  value: [],
                };
              case TEMPLATE_STRUCTURE_KEY:
              case STRUCTURE_KEY:
                return {
                  ...entry,
                  value: structure,
                };
            }
            return entry;
          })
        )
      );
  }

  patchStructureByTargetId$(
    targetLayout: SourceLayout,
    data: object
  ): Observable<{
    data: BaseContentVariant[];
  }> {
    const { relatedDefinitionType, id } = targetLayout;
    // We use cleanHttpService to avoid CORS issues.
    return this.cleanHttpService.patch<{ data: BaseContentVariant[] }>(
      `${this.getApi()}/layout-editor/${this.isTemplate() ? `${relatedDefinitionType}/template/${id}` : `${this.layoutType()}/layout/${id}`}`,
      { data },
      {
        headers: this.headers(),
      }
    );
  }

  private getApi(environment: IEnvironment = this.selectedEnvironment()): string {
    return `${(environment.apiUrls as ApiUrlCollection).api}/api/${this.lang}`;
  }

  private get lang(): string {
    return `${this.translateService.currentLang}/${this.storageService.getSessionStorageData(LANGUAGE_KEY) ?? this.translateService.currentLang}`;
  }
}
