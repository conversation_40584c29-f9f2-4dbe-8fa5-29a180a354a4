<nz-page-header [nzGhost]="false" nzTitle="Elrendezés másoló">
  <nz-page-header-content>
    Elrendezéseket van lehetőség másolni környezetek között. <br />
    A modul kizárólag belső használatra készült, és PROD-ra vagy PROD-ról másolás csak <strong>engedéllyel
    szabad!</strong>
  </nz-page-header-content>
</nz-page-header>

<nz-card>
  <label class="label">Elrendezési típusok:</label>
  <nz-select
    class="block select"
    [ngModel]="layoutCloner.layoutType()"
    (ngModelChange)="layoutCloner.layoutType.set($event); layoutCloner.selectedEnvironment.set(null); selectedLayout.set(null); layoutsLoader.init()">
    <nz-option [nzValue]="null" nzLabel="Sablonok"></nz-option>
    <nz-option nzValue="HomePage" nzLabel="Címlapok"></nz-option>
    <nz-option nzValue="Column" nzLabel="Rovat címlapok"></nz-option>
    <nz-option nzValue="Opinion" nzLabel="Vélemény címlapok"></nz-option>
    <nz-option nzValue="Sidebar" nzLabel="Közös területek"></nz-option>
    <nz-option nzValue="ColumnSidebar" nzLabel="Rovat közös területek"></nz-option>
    <nz-option nzValue="CustomBuiltPage" nzLabel="Egyedi oldalak"></nz-option>
  </nz-select>

  <label class="label">Aktuális környezet elrendezései:</label>
  <nz-select
    class="block select"
    nzPlaceHolder="Válassz tartalmat"
    [nzAllowClear]="true"
    [nzServerSearch]="true"
    [ngModel]="selectedLayout()"
    (ngModelChange)="selectedLayout.set($event)"
    [compareWith]="layoutsLoader.compareFn"
    (nzScrollToBottom)="layoutsLoader.loadMore()"
    (nzOnSearch)="layoutsLoader.search($event)"
    [nzLoading]="layoutsLoader.isLoading$ | async">
    @for (layout of layoutsLoader.data$ | async; track layout.id) {
      <nz-option [nzValue]="layout" [nzLabel]="layout.title"></nz-option>
    }
  </nz-select>

  <label class="label">Válaszd ki, hogy melyik környezetre szeretnéd végrehajtani a másolást:</label>
  <nz-radio-group
    [ngModel]="layoutCloner.selectedEnvironment()"
    (ngModelChange)="handleEnvironmentChange($event)"
    [nzDisabled]="targetLayoutsLoader.isLoading$ | async"
    nzButtonStyle="solid"
    class="block">
    <button nz-radio-button [nzValue]="DevEnvironment">
      DEV
    </button>
    <button nz-radio-button [nzValue]="TestEnvironment">
      TESZT
    </button>
    <button nz-radio-button [nzValue]="ProdEnvironment">
      PROD
    </button>
  </nz-radio-group>

  @if (layoutCloner.useOverwrittenToken()) {
    <div class="block">
      @if (!(targetLayoutsLoader.data$ | async)?.length) {
        <nz-alert
          class="block"
          nzType="warning"
          [nzAction]="actionTemplate"
          nzMessage="Autentikáció szükséges, add meg a kiválasztott környezethez tartozó X-Auth-Tokent, majd ellenőrizd!"
          nzShowIcon
        />
        <ng-template #actionTemplate>
          <button
            nz-button
            nzType="primary"
            [nzDanger]="true"
            [disabled]="!layoutCloner.authToken()"
            (click)="handleEnvironmentChange(layoutCloner.selectedEnvironment())">
            Ellenőrzés
          </button>
        </ng-template>
      } @else {
        <nz-alert class="block" nzType="success" nzMessage="Autentikáció sikeres!" nzShowIcon />
      }
      <textarea
        class="block" rows="4" nz-input placeholder="X-Auth-Token"
        [ngModel]="layoutCloner.authToken()"
        (ngModelChange)="layoutCloner.authToken.set($event)">
      </textarea>
    </div>
  }

  @if (isMegyei()) {
    <label class="label">Válaszd ki, hogy melyik Megyeire szeretnéd végrehajtani a másolást:</label>
    <nz-select
      class="block select"
      [ngModel]="layoutCloner.selectedDomain()"
      (ngModelChange)="layoutCloner.selectedDomain.set($event); selectedTargetLayout.set(null); targetLayoutsLoader.init()">
      @for (megyei of MEGYEI_LAPOK; track $index) {
        <nz-option [nzValue]="megyei" [nzLabel]="megyei"></nz-option>
      }
    </nz-select>
  }

  @if (layoutCloner.selectedEnvironment()) {
    <label class="label">
      {{ layoutCloner.selectedEnvironment().type.toUpperCase() }} környezethez tartozó elrendezések:
    </label>
    <nz-select
      class="block select"
      nzPlaceHolder="Válassz tartalmat"
      [nzAllowClear]="true"
      [nzServerSearch]="true"
      [ngModel]="selectedTargetLayout()"
      (ngModelChange)="selectedTargetLayout.set($event)"
      [compareWith]="targetLayoutsLoader.compareFn"
      (nzScrollToBottom)="targetLayoutsLoader.loadMore()"
      (nzOnSearch)="targetLayoutsLoader.search($event)"
      [nzLoading]="targetLayoutsLoader.isLoading$ | async">
      @for (layout of targetLayoutsLoader.data$ | async; track layout.id) {
        <nz-option [nzValue]="layout" [nzLabel]="layout.title"></nz-option>
      }
    </nz-select>
  }

  <button
    nz-button
    nzType="primary"
    [disabled]="!selectedLayout() || !selectedTargetLayout() || !layoutCloner.selectedEnvironment() || errorMessage()"
    [nzLoading]="isCopying()"
    (click)="handleLayoutCopy()">
    <nz-icon nzType="copy" />
    Másolás indítása
  </button>

  @if (errorMessage()) {
    <nz-alert class="error" nzType="error" [nzMessage]="errorMessage()" nzShowIcon></nz-alert>
  }
</nz-card>
