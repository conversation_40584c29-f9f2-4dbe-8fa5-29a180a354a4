import { ChangeDetectionStrategy, Component, DestroyRef, inject, On<PERSON><PERSON>roy, OnInit, signal } from '@angular/core';
import { NzPageHeaderComponent, NzPageHeaderContentDirective } from 'ng-zorro-antd/page-header';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { FormsModule } from '@angular/forms';
import { NzCardComponent } from 'ng-zorro-antd/card';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { environment } from 'src/environments/environment';
import { environment as DevEnvironment } from 'src/environments/environment.dev';
import { environment as TestEnvironment } from 'src/environments/environment.teszt';
import { environment as ProdEnvironment } from 'src/environments/environment.prod';
import { IHttpOptions } from '@external/http';
import { SelectPageableHttpLoader } from '@shared/modules/form-controls/components/selects/select-form-control/select-pageable-http-loader';
import { AsyncPipe } from '@angular/common';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { DomainService, MEGYEI_LAPOK } from '@core/modules/admin/services/domain.service';
import { catchError, finalize, switchMap } from 'rxjs/operators';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { HttpErrorResponse } from '@angular/common/http';
import { throwError } from 'rxjs';
import { NzAlertComponent } from 'ng-zorro-antd/alert';
import { SharedService } from '@shared/services/shared.service';
import { LayoutCloner } from './layout-cloner';
import { NzInputDirective } from 'ng-zorro-antd/input';
import { IEnvironment } from 'src/environments/environment.definitions';

@Component({
  selector: 'app-layout-cloner',
  templateUrl: './layout-cloner.component.html',
  styleUrl: './layout-cloner.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NzPageHeaderComponent,
    FormsModule,
    NzSelectModule,
    NzCardComponent,
    NzButtonModule,
    NzIconModule,
    NzPageHeaderContentDirective,
    AsyncPipe,
    NzRadioModule,
    NzAlertComponent,
    NzInputDirective,
  ],
  providers: [LayoutCloner],
})
export class LayoutClonerComponent implements OnInit, OnDestroy {
  private readonly domainService = inject(DomainService);
  private readonly destroyRef = inject(DestroyRef);
  private readonly sharedService = inject(SharedService);

  readonly layoutCloner = inject(LayoutCloner);

  readonly isCopying = signal<boolean>(false);
  readonly errorMessage = signal<string | null>(null);
  readonly isMegyei = signal<boolean>(this.domainService.isCurrentDomainMegyeiLap());
  readonly selectedTargetLayout = signal(null);
  readonly selectedLayout = signal(null);

  readonly layoutsLoader = new SelectPageableHttpLoader((options: IHttpOptions) => this.layoutCloner.getCurrentLayouts$(options));

  readonly targetLayoutsLoader = new SelectPageableHttpLoader((options: IHttpOptions) =>
    this.layoutCloner.getTargetEnvLayouts$(options).pipe(
      catchError((error: HttpErrorResponse) => {
        this.errorMessage.set(error.message);
        this.hideErrorWithDelay();
        return throwError(() => error);
      })
    )
  );

  readonly environment = environment;
  readonly DevEnvironment = DevEnvironment;
  readonly TestEnvironment = TestEnvironment;
  readonly ProdEnvironment = ProdEnvironment;
  readonly MEGYEI_LAPOK = MEGYEI_LAPOK;

  ngOnInit(): void {
    this.layoutsLoader.init();
  }

  ngOnDestroy(): void {
    this.layoutsLoader.close();
    this.targetLayoutsLoader?.close();
  }

  handleEnvironmentChange(environment: IEnvironment): void {
    this.targetLayoutsLoader.clear();
    this.layoutCloner.selectedEnvironment.set(environment);
    this.selectedTargetLayout.set(null);
    if (!(this.layoutCloner.useOverwrittenToken() && !this.layoutCloner.authToken())) {
      this.targetLayoutsLoader.init();
    }
  }

  handleLayoutCopy(): void {
    this.isCopying.set(true);
    this.layoutCloner
      .getStructureBySelectedId$(this.selectedLayout())
      .pipe(
        finalize(() => this.isCopying.set(false)),
        takeUntilDestroyed(this.destroyRef),
        switchMap((structure) => this.layoutCloner.getStructureByTargetId$(structure, this.selectedTargetLayout())),
        switchMap((data) => this.layoutCloner.patchStructureByTargetId$(this.selectedTargetLayout(), data)),
        catchError((error: HttpErrorResponse) => {
          this.errorMessage.set(error.message);
          this.hideErrorWithDelay();
          return throwError(() => error);
        })
      )
      .subscribe(() => {
        this.sharedService.showNotification('success', 'Az elrendezés másolása sikeres');
        this.selectedTargetLayout.set(null);
      });
  }

  private hideErrorWithDelay(): void {
    setTimeout(() => {
      this.errorMessage.set(null);
    }, 10_000);
  }
}
