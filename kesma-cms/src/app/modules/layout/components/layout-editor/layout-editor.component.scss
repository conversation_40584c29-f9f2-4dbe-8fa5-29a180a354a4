@use 'shared' as *;
@use '../../bootstrap-overwrite-editor' as *;

.alert {
  margin-bottom: 20px;
  max-width: 1200px;
  width: 100%;
}

.layout-editor {
  position: relative;

  @include bootstrap-overwrite-editor();

  .disabled-overlay {
    position: absolute;
    top: -21px;
    left: -21px;
    width: calc(100% + 44px);
    height: calc(100% + 44px);
    z-index: 500;
    background-color: rgba($grey-5, 0.6);
  }

  .row {
    margin-right: 0;
    margin-left: 0;
  }

  .tools {
    position: fixed;
    right: 16px;
    top: 220px;
    transition: 0.5s all;
    transform: translateX(0);

    &.closed {
      transform: translateX(90%);

      .title {
        span {
          opacity: 0;
        }
      }
    }

    .toolbox {
      border: 1px solid $zorro-dark;
      width: 200px;
      cursor: pointer;

      &.containers {
        margin-bottom: 10px;
      }

      &.contents {
        .actions {
          height: calc(100vh - 455px);
          max-height: 40vh;
          overflow-y: scroll;

          .drag-placeholder {
            display: none;
            height: 0;
          }
        }
      }

      .title {
        background-color: $zorro-dark;
        height: 30px;
        font-size: 16px;
        line-height: 16px;
        padding: 7px 10px;
        color: $white;
        display: flex;
        align-items: center;

        i {
          opacity: 0;
          transition: 0.5s all;

          &.active {
            opacity: 1;
          }
        }

        span {
          transition: 0.5s all;
          margin-right: 10px;
        }
      }

      .actions {
        background-color: $white;
        padding: 16px 10px;

        .drag-placeholder {
          display: none;
          height: 0;
        }
      }
    }
  }

  .layout {
    min-width: 1200px;
    max-width: calc(100% - 260px);
    height: 100%;
    overflow-y: auto;

    &.sidebar {
      min-width: 400px;
    }

    h2 {
      width: 100%;
      margin-bottom: 0.5em;
    }

    .preview {
      width: 1200px;

      &.sidebar {
        width: 400px;
      }

      @media only screen and (max-width: 1660px) {
        width: 1000px;
      }

      @media only screen and (max-width: 1460px) {
        width: 800px;
      }
    }
  }

  .layout-button {
    display: block;
    border-radius: 6px;
    color: $white;
    width: 100%;
    text-align: center;
    height: auto; // default: 30px
    padding: 4px;
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    &.add-row-button {
      background-color: rgba($layout-row, 0.8);

      &:hover {
        background-color: $layout-row;
      }
    }

    &.add-column-button {
      background-color: rgba($layout-column, 0.8);

      &:hover {
        background-color: $layout-column;
      }
    }

    &.add-content-button {
      background-color: rgba($layout-content, 0.8);

      &:hover {
        background-color: $layout-content;
      }
    }

    &.deselect-all-button {
      background-color: rgba($layout-select, 0.8);

      &:hover {
        background-color: $layout-select;
      }
    }
  }
}

.layout-element {
  border-radius: 8px;
  border-style: solid;
  background-color: $white;
  overflow: hidden;
  margin-bottom: 15px;
  cursor: grab;

  :active {
    cursor: grabbing;
  }

  &:last-child {
    margin-bottom: 0;
  }

  .element-head {
    width: 100%;
    display: flex;
    justify-content: space-between;

    .element-type,
    .element-controls {
      color: $white;
      padding: 6px;
    }

    .element-type {
      border-radius: 0 0 8px 0;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }

    .element-controls {
      border-radius: 0 0 0 8px;
      font-size: 20px;
      display: flex;
      align-items: center;

      &.small {
        border-radius: 0 0 8px 0;

        i,
        a i {
          margin-right: 2px;

          &::after {
            content: none;
          }
        }
      }

      a:link {
        color: white;
      }

      a:visited {
        color: white;
      }

      a:hover {
        color: white;
      }

      a:active {
        color: white;
      }

      a {
        line-height: 0em;
        margin-right: 15px;
        position: relative;

        &::after {
          content: '';
          display: block;
          height: 12px;
          width: 1px;
          background-color: $white;
          position: absolute;
          top: 4px;
          right: -7px;
        }

        &:last-child {
          margin-right: 0;

          &::after {
            display: none;
          }
        }

        &.disabled {
          cursor: default;

          i {
            opacity: 0.4;
          }
        }
      }

      i,
      a i {
        position: relative;
        color: white;
      }
    }
  }

  .element-bottom {
    width: 100%;
    display: flex;
    justify-content: space-between;

    .element-type {
      background-color: $layout-content;
      color: $white;

      border-radius: 0 8px 0 0;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;

      span {
        padding: 6px;
        display: block;
      }
    }

    .right {
      border-radius: 8px 0 0 0;
    }
  }

  .element-body {
    width: 100%;
  }

  &.row-element {
    border-color: rgba($layout-row, 0.8);
    width: 100%;

    &.active {
      border-color: rgba($layout-select, 1);

      > .element-head .element-type,
      > .element-head .element-controls {
        background-color: $layout-select;
      }
    }

    > .element-head .element-type,
    > .element-head .element-controls {
      background-color: rgba($layout-row, 0.8);
    }

    > .element-body {
      padding: 5px 0;

      .row {
        width: 100%;
      }
    }
  }

  &.column-element {
    --kui-teal-400: #06d36a;
    --kui-gray-500: #717171;

    border-color: rgba($layout-column, 0.8);

    &.active {
      border-color: rgba($layout-select, 1);

      > .element-head .element-type,
      > .element-head .element-controls {
        background-color: $layout-select;
      }
    }

    > .element-head .element-type,
    > .element-head .element-controls {
      background-color: rgba($layout-column, 0.8);
    }

    > .element-body {
      padding: 5px;
    }

    &.has-right-column-border::after {
      border: 1px solid var(--kui-layout-column-border-color, var(--kui-layout-column-border-color-default));
      content: ' ';
      display: block;
      position: absolute;
      height: 100%;
      width: 0;
      right: 0;
      top: 0;
    }

    &.has-left-column-border::before {
      border: 1px solid var(--kui-layout-column-border-color, var(--kui-layout-column-border-color-default));
      content: ' ';
      display: block;
      position: absolute;
      height: 100%;
      width: 0;
      left: 0;
      top: 0;
    }

    &.column-border-color-green::after,
    &.column-border-color-green::before {
      border-color: var(--kui-teal-400);
    }

    &.column-border-color-gray::after,
    &.column-border-color-gray::before {
      border-color: var(--kui-gray-500);
    }
  }

  &.content-element {
    border-color: $layout-content;

    > .element-head .element-type,
    .element-controls {
      background-color: $layout-content;
    }

    > .element-body {
      padding: 5px;

      .preview-image {
        width: 100%;
        height: auto;
        margin-bottom: 15px;

        &.multipart {
          margin-bottom: 0;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  &:last-child {
    margin-bottom: 0;
  }

  &.add-row-button {
    background-color: rgba($layout-row, 0.8);

    &:hover {
      background-color: $layout-row;
    }
  }

  &.add-column-button {
    background-color: rgba($layout-column, 0.8);

    &:hover {
      background-color: $layout-column;
    }
  }

  &.add-content-button {
    background-color: rgba($layout-content, 0.8);

    &:hover {
      background-color: $layout-content;
    }
  }

  &.deselect-all-button {
    background-color: rgba($layout-select, 0.8);

    &:hover {
      background-color: $layout-select;
    }
  }
}

.layout-element {
  border-radius: 8px;
  border-style: solid;
  background-color: $white;
  overflow: hidden;
  margin-bottom: 15px;
  cursor: grab;

  :active {
    cursor: grabbing;
  }

  &:last-child {
    margin-bottom: 0;
  }

  .element-head {
    width: 100%;
    display: flex;
    justify-content: space-between;

    .element-type,
    .element-controls {
      color: $white;
      padding: 6px;
    }

    .element-type {
      border-radius: 0 0 8px 0;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }

    .element-controls {
      border-radius: 0 0 0 8px;
      font-size: 20px;
      display: flex;
      align-items: center;

      &.small {
        border-radius: 0 0 8px 0;

        i,
        a i {
          margin-right: 2px;

          &::after {
            content: none;
          }
        }
      }

      a:link {
        color: white;
      }

      a:visited {
        color: white;
      }

      a:hover {
        color: white;
      }

      a:active {
        color: white;
      }

      a {
        line-height: 0em;
        margin-right: 15px;
        position: relative;

        &::after {
          content: '';
          display: block;
          height: 12px;
          width: 1px;
          background-color: $white;
          position: absolute;
          top: 4px;
          right: -7px;
        }

        &:last-child {
          margin-right: 0;

          &::after {
            display: none;
          }
        }
      }

      i,
      a i {
        position: relative;
        color: white;
      }
    }
  }

  .element-body {
    width: 100%;
  }

  &.row-element {
    border-color: rgba($layout-row, 0.8);
    width: 100%;

    &.active {
      border-color: rgba($layout-select, 1);

      > .element-head .element-type,
      > .element-head .element-controls {
        background-color: $layout-select;
      }
    }

    > .element-head .element-type,
    > .element-head .element-controls {
      background-color: rgba($layout-row, 0.8);
    }

    > .element-body {
      padding: 5px 0;

      .row {
        width: 100%;
      }
    }
  }

  &.column-element {
    border-color: rgba($layout-column, 0.8);

    &.active {
      border-color: rgba($layout-select, 1);

      > .element-head .element-type,
      > .element-head .element-controls {
        background-color: $layout-select;
      }
    }

    > .element-head .element-type,
    > .element-head .element-controls {
      background-color: rgba($layout-column, 0.8);
    }

    > .element-body {
      padding: 5px;
    }
  }

  &.content-element {
    border-color: $layout-content;

    > .element-head .element-type,
    .element-controls {
      background-color: $layout-content;
    }

    > .element-body {
      padding: 5px;

      .preview-image {
        width: 100%;
        height: auto;
        margin-bottom: 15px;

        &.multipart {
          margin-bottom: 0;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

.save-status-container {
  position: fixed;
  top: 80px;
  z-index: 9000;
  left: 60%;
  transform: translateX(-50%);
  max-width: 360px;
  margin: 0 auto;
  opacity: 1;
  transition: 0.3s all;

  &.hide {
    opacity: 0;
  }

  .save-status {
    transition: transform 0.2s ease-in-out;
  }
}

/*.cdk-drag-preview {
    box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
        0 8px 10px 1px rgba(0, 0, 0, 0.14),
        0 3px 14px 2px rgba(0, 0, 0, 0.12);
}*/

.cdk-drag-placeholder {
  opacity: 0;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.element-children.cdk-drop-list-dragging .layout-element:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.cdk-drop-list {
  min-height: 130px;
}

.drag-placeholder {
  opacity: 1;
  border-radius: 10px;
  min-height: 50px;
  display: block;
  width: 100%;
  min-width: 50px;
  margin-bottom: 15px;
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);

  &-row {
    border: 3px dotted rgba($layout-row, 1) !important;
    background-color: rgba($layout-row, 0.2) !important;
  }

  &-column {
    border: 3px dotted rgba($layout-column, 1) !important;
    background-color: rgba($layout-column, 0.2) !important;
  }

  &-content {
    border: 3px dotted rgba($layout-content, 1) !important;
    background-color: rgba($layout-content, 0.2) !important;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.5rem;
    color: rgba($layout-content, 1);
    font-weight: 500;
  }
}

.layout-drag-preview {
  font-size: 14px;

  i.anticon {
    font-size: 20px;
    width: 20px;
    height: 20px;
  }

  .row {
    margin-left: 0px;
    margin-right: 0px;

    & > .cdk-drag {
      padding-left: 5px;
      padding-right: 5px;
    }
  }
}

.add-content-button-preview {
  width: 180px;
  display: block;
  border-radius: 6px;
  color: $white;
  text-align: center;
  height: auto; // default: 30px
  padding: 4px;
  margin-bottom: 12px;
  background-color: rgba($layout-content, 0.8);
  transition: none !important;
  overflow: hidden;
}

.add-row-button-preview {
  max-width: 180px;
  display: block;
  border-radius: 6px;
  color: $white;
  width: 100%;
  text-align: center;
  height: fit-content; // default: 30px
  padding: 4px;
  margin-bottom: 12px;
  background-color: rgba($layout-row, 0.8);
  transition: none !important;
}

.drag-preview.drag-preview-content {
  max-width: 300px;
  transition: all 100ms cubic-bezier(0, 0, 0.2, 1);
  box-shadow:
    0 5px 5px -3px rgba(0, 0, 0, 0.2),
    0 8px 10px 1px rgba(0, 0, 0, 0.14),
    0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.col-12 {
  /**
    Blokkcím nem egyforma hosszú az alatta lévő előnézeti képpel. (Elrendezés módosítása fül)
    Azokon a portálokon, ahol a blokkcímnek háttérszíne van jelentős probléma. (pl. Mandiner)
   */
  img[alt='Block title'] {
    min-width: calc(100% + 10px);
    margin: 0 -5px;
  }
}

.col-12 > img.block-title {
  margin-bottom: 20px;
}

.column-mobile-order {
  font-size: 12px;
}
