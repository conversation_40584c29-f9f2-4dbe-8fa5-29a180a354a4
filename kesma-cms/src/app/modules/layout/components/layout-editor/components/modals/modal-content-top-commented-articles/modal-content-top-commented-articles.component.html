<div class="article-config">
  <div class="row">
    <div class="col-6">
      <div class="checkbox">
        <label nz-checkbox [(ngModel)]="withBlockTitle">{{ 'CMS.block-has-title' | translate }}</label>
      </div>
    </div>
    <div class="col-6">
      <div class="checkbox">
        <label nz-checkbox [(ngModel)]="hideMobile">{{ 'CMS.hide-mobile' | translate }}</label>
      </div>
    </div>
  </div>
  <h2>Megjelenítés formátuma</h2>
  <div class="row">
    <ng-container *ngIf="options[domainIndex]?.options">
      <div class="col-12" *ngFor="let option of options[domainIndex].options">
        <div class="option" [ngClass]="{ selected: selectedStyle?.styleId === option.styleId }" (click)="selectType(option)">
          <img [src]="option.previewImage" class="preview-image" />
        </div>
      </div>
    </ng-container>
  </div>
</div>
