import { ChangeDetectionStrategy, Component, input, OnInit, output, signal, TemplateRef } from '@angular/core';

@Component({
  selector: 'app-load-with-idle',
  templateUrl: './load-with-idle.component.html',
  styleUrl: './load-with-idle.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class LoadWithIdleComponent implements OnInit {
  data = input.required<object>();
  template = input.required<TemplateRef<{ $implicit: object }>>();
  /**
   * Flag indicating whether to delay rendering until the browser is idle.
   * If `false`, content is shown immediately.
   */
  delayUntilIdle = input.required<boolean>();
  shouldEmitWhenLoaded = input.required<boolean>();
  /**
   * Event emitted when this is the last component in a sequence and it has been loaded.
   */
  isContentLoaded = output<void>();

  showContent = signal<boolean>(false);

  ngOnInit(): void {
    if (!this.delayUntilIdle()) {
      this.showContent.set(true);
      if (this.shouldEmitWhenLoaded()) {
        this.isContentLoaded.emit();
      }
      return;
    }
    requestIdleCallback(() => {
      this.showContent.set(true);
      if (this.shouldEmitWhenLoaded()) {
        this.isContentLoaded.emit();
      }
    });
  }
}
