import { NZ_MODAL_DATA } from 'ng-zorro-antd/modal';
import { inject, Component, Input, OnInit } from '@angular/core';

@Component({
  selector: 'app-modal-content-did-you-know',
  templateUrl: './modal-content-did-you-know.component.html',
  styleUrls: ['./modal-content-did-you-know.component.scss'],
  standalone: false,
})
export class ModalContentDidYouKnowComponent implements OnInit {
  private readonly modalData = inject<Record<string, any>>(NZ_MODAL_DATA, {});
  @Input() hideMobile: boolean;
  @Input() withBlockTitle: boolean;
  constructor() {}

  ngOnInit(): void {
    Object.keys(this.modalData).forEach((key) => (this[key] = this.modalData[key]));
  }
}
