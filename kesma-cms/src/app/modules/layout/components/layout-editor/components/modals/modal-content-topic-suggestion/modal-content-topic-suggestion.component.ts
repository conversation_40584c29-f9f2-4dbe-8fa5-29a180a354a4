import { Component, inject, Input, OnInit } from '@angular/core';
import { DomainKey } from '@core/modules/admin/admin.definitions';
import { BasicLayoutElementPreview } from '@modules/layout/layout-core.definitions';
import { NZ_MODAL_DATA } from 'ng-zorro-antd/modal';
import { TranslatePipe } from '@ngx-translate/core';
import { NzCheckboxComponent } from 'ng-zorro-antd/checkbox';
import { NgClass } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-modal-content-topic-suggestion',
  templateUrl: './modal-content-topic-suggestion.component.html',
  styleUrl: './modal-content-topic-suggestion.component.scss',
  imports: [TranslatePipe, NzCheckboxComponent, NgClass, FormsModule],
})
export class ModalContentTopicSuggestionComponent implements OnInit {
  private readonly modalData = inject<Record<string, any>>(NZ_MODAL_DATA, {});
  @Input() hideMobile: boolean;
  @Input() withBlockTitle: boolean;
  @Input() styleId: number;
  @Input() domain: DomainKey;
  @Input() selectedType: {
    styleId: number;
    previewImage: string;
  };

  domainIndex = -1;
  selectedIndex = -1;

  options: {
    domain: DomainKey;
    options: BasicLayoutElementPreview[];
  }[] = [
    {
      domain: 'bors',
      options: [
        {
          styleId: 1,
          previewImage: '/assets/images/layout-frames/bors/topic-suggestion.png',
        },
      ],
    },
  ];

  ngOnInit(): void {
    Object.keys(this.modalData).forEach((key) => (this[key] = this.modalData[key]));

    this.domainIndex = this.options.findIndex((o) => o.domain === this.domain);
    this.selectTypeIndex(this.styleId);
  }

  selectType(index: number): void {
    this.selectedIndex = index;
    this.selectedType = this.options[this.domainIndex].options[this.selectedIndex];
  }

  selectTypeIndex(styleId: number): void {
    const type = this.options[this.domainIndex].options.find((option) => option.styleId === styleId || 1);
    this.selectedIndex = this.options[this.domainIndex].options.indexOf(type);
    this.selectedType = this.options[this.domainIndex].options[this.selectedIndex];
  }
}
