import { NZ_MODAL_DATA } from 'ng-zorro-antd/modal';
import { inject, Component, Input, OnInit } from '@angular/core';
import { DomainKey } from 'src/app/core/modules/admin/admin.definitions';
import { BasicLayoutElementPreview } from '@modules/layout/layout-core.definitions';

@Component({
  selector: 'app-modal-content-sub-columns',
  templateUrl: './modal-content-top-commented-articles.component.html',
  styleUrls: ['./modal-content-top-commented-articles.component.scss'],
  standalone: false,
})
export class ModalContentTopCommentedArticlesComponent implements OnInit {
  private readonly modalData = inject<Record<string, any>>(NZ_MODAL_DATA, {});
  @Input() selectedStyle: BasicLayoutElementPreview;
  @Input() hideMobile: boolean;
  @Input() withBlockTitle: boolean;
  @Input() domain: DomainKey;

  domainIndex = -1;
  options: {
    domain: DomainKey;
    options: BasicLayoutElementPreview[];
  }[] = [
    {
      domain: 'bors',
      options: [
        {
          styleId: 1,
          previewImage: '/assets/images/layout-frames/bors/top-commented-articles.webp',
        },
      ],
    },
  ];

  ngOnInit(): void {
    Object.keys(this.modalData).forEach((key) => (this[key] = this.modalData[key]));
    this.domainIndex = this.options.findIndex(({ domain }) => domain === this.domain);
    this.selectType(this.options[this.domainIndex]?.options.find((o) => o.styleId === this.selectedStyle?.styleId));
  }

  selectType(option: BasicLayoutElementPreview): void {
    this.selectedStyle = option;
  }
}
