import { NZ_MODAL_DATA } from 'ng-zorro-antd/modal';
import { inject } from '@angular/core';
import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { UntypedFormControl, Validators } from '@angular/forms';
import { DomainKey } from '../../../../../../../core/modules/admin/admin.definitions';
import { LayoutElementPreviewConfig, LayoutElementVideoArticlesPreview, LayoutPageType } from '../../../../../layout-core.definitions';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-modal-content-articles-with-video',
  templateUrl: './modal-content-articles-with-video.component.html',
  styleUrls: ['./modal-content-articles-with-video.component.scss'],
  standalone: false,
})
export class ModalContentArticlesWithVideoComponent implements OnInit, OnDestroy {
  private readonly modalData = inject<Record<string, any>>(NZ_MODAL_DATA, {});
  @Input() hideMobile: boolean;
  @Input() withBlockTitle: boolean;
  @Input() domain: DomainKey;
  @Input() styleId: number;
  @Input() selectedVideoBlock: LayoutElementVideoArticlesPreview;
  @Input() contentLength = 1;
  @Input() mobileOrder: number;
  @Input() useMobileOrder: boolean;
  @Input() articleCount = 3;
  @Input() pageType: LayoutPageType;

  readonly ORIGO_MIN_CONTENT_LENGTH = 3;
  readonly ORIGO_MAX_CONTENT_LENGTH = 15;

  domainIndex = -1;
  selectedIndex = -1;

  videoBlockOptions: LayoutElementPreviewConfig<LayoutElementVideoArticlesPreview>[];
  mobilOrderControl: UntypedFormControl;
  articleCountControl: UntypedFormControl;

  private readonly destroy$ = new Subject<void>();

  constructor() {
    this.videoBlockOptions = [
      {
        domain: 'mandiner',
        options: [
          {
            styleId: 1,
            articleCount: 3,
            previewImage: '/assets/images/layout-frames/mandiner/video-block01.png',
          },
        ],
      },
      {
        domain: 'nso',
        options: [
          {
            styleId: 1,
            articleCount: 3,
            previewImage: '/assets/images/layout-frames/nso/video-articles-block01.png',
          },
        ],
      },
      {
        domain: 'origo',
        options: [
          {
            styleId: 0,
            articleCount: 15,
            previewImage: '/assets/images/layout-frames/origo/video-block01.png',
          },
          {
            styleId: 1,
            articleCount: 15,
            previewImage: '/assets/images/layout-frames/origo/video-block02.png',
          },
        ],
      },
      {
        domain: 'vilaggazdasag',
        options: [
          {
            styleId: 1,
            articleCount: 4,
            previewImage: '/assets/images/layout-frames/vg/video-block01.png',
          },
        ],
      },
      {
        domain: 'pesti_sracok',
        options: [
          {
            styleId: 1,
            articleCount: 4,
            previewImage: '/assets/images/layout-frames/pestisracok/article-video-block01.png',
          },
        ],
      },
      {
        domain: 'bors',
        options: [
          {
            styleId: 1,
            articleCount: 4,
            previewImage: '/assets/images/layout-frames/bors/article-video-block01.webp',
          },
        ],
      },
      {
        domain: 'magyarNemzet',
        options: [
          {
            styleId: 1,
            articleCount: 4,
            previewImage: '/assets/images/layout-frames/mno/article-video-block01.png',
          },
        ],
      },
    ];
  }

  ngOnInit(): void {
    Object.keys(this.modalData).forEach((key) => (this[key] = this.modalData[key]));

    this.mobilOrderControl = new UntypedFormControl(this.mobileOrder);
    this.articleCountControl = new UntypedFormControl(this.articleCount, [
      Validators.required,
      Validators.min(this.ORIGO_MIN_CONTENT_LENGTH),
      Validators.max(this.ORIGO_MAX_CONTENT_LENGTH),
    ]);
    this.domainIndex = this.videoBlockOptions.findIndex((o) => o.domain === this.domain);
    this.selectTypeIndex(this.styleId);

    if (this.domain === 'pesti_sracok' && this.pageType === LayoutPageType.SIDEBAR) {
      this.videoBlockOptions[this.domainIndex].options[0].articleCount = 1;
    }

    this.articleCountControl.valueChanges.pipe(takeUntil(this.destroy$)).subscribe((value) => {
      this.articleCount = value;
      this.videoBlockOptions[this.domainIndex].options[this.selectedIndex].articleCount = value;
    });
  }

  onStyleSelect(index: number): void {
    this.selectedIndex = index;
    this.selectedVideoBlock = this.videoBlockOptions[this.domainIndex].options[this.selectedIndex];
    this.articleCountControl.setValue(this.selectedVideoBlock.articleCount);
  }

  selectTypeIndex(styleId: number) {
    const type = this.videoBlockOptions[this.domainIndex]?.options?.find((o) => o.styleId === styleId);
    this.selectedIndex = this.videoBlockOptions[this.domainIndex]?.options?.indexOf(type);
    this.selectedVideoBlock = type;
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
