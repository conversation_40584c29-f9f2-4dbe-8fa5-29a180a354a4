@if (isLoading()) {
  <nz-alert
    class="alert"
    nzType="warning"
    nzMessage="Az elrendezés a háttérben épp betöltődik, ez idő alatt a Fogd és Vidd technológia nem elérhető. Kis türelmet kérünk!"
    [nzIcon]="loadingIcon"
    [nzShowIcon]="true">
  </nz-alert>
  <ng-template #loadingIcon>
    <nz-icon nzType="loading" nzTheme="outline" />
  </ng-template>
}

@if (currentSettings) {
  <div class="layout-editor">
    <app-navigate-to-top></app-navigate-to-top>
    @if (isDisabled) {
      <div class="disabled-overlay"></div>
    }
    @if (!isReadonly) {
      <div class="tools" [class.closed]="!toolsOpened">
        @if (currentSettings.canAddRow || currentSettings.canAddColumn) {
          <div class="toolbox containers">
            <div class="title">
              <i (click)="toggleToolbar('show')" [class.active]="!toolsOpened" nz-icon nzType="caret-left"
                 nzTheme="outline"></i>
              <span>Elrendezés típusok</span>
              <i (click)="toggleToolbar('hide')" [class.active]="toolsOpened" nz-icon nzType="caret-right"
                 nzTheme="outline"></i>
            </div>
            <div class="actions" cdkDropList [cdkDropListSortingDisabled]="true" appDragAndDropLayout>
              @if (currentSettings.canAddRow) {
                <button
                  type="button"
                  class="layout-button add-row-button"
                  (click)="openRowModal()"
                  cdkDrag
                  (cdkDragStarted)="layoutItemRowDragStarted($event)"
                  (cdkDragDropped)="layoutItemRowDragDropped($event)"
                  [cdkDragData]="{ type: 'row', init: true }"
                >
                  <div class="drag-placeholder drag-placeholder-row" *cdkDragPlaceholder></div>
                  <div *cdkDragPreview class="add-row-button-preview">Sor hozzáadása</div>
                  Sor hozzáadása
                </button>
              }
              @if (dragDropAddRowButtonPlaceholderVisible) {
                <button type="button" class="layout-button add-row-button">Sor hozzáadása</button>
              }
              @if (currentSettings.canAddColumn) {
                <button type="button" class="layout-button add-column-button" (click)="openColumnModal()">
                  Oszlop hozzáadása
                </button>
              }
              <button type="button" class="layout-button deselect-all-button" (click)="deselectAll()">Kijelölés
                elvetése
              </button>
            </div>
          </div>
        }

        @if (currentSettings.enabledContentTypes?.length > 0) {
          <div class="toolbox contents">
            <div class="title">Tartalmi típusok</div>
            <div class="actions" appDragAndDropLayout cdkDropList [cdkDropListSortingDisabled]="true"
                 [cdkDropListData]="currentSettings.enabledContentTypes">
              @for (contentType of currentSettings.enabledContentTypes; track contentType) {
                <button
                  cdkDrag
                  type="button"
                  class="layout-button add-content-button"
                  (click)="onAddContentButtonClick(getContentTypeEnumString(contentType))"
                  (cdkDragStarted)="toolboxContentItemDragStarted($event, currentSettings.enabledContentTypes, contentType, $index)"
                  (cdkDragDropped)="toolboxContentItemDropped($event, currentSettings.enabledContentTypes, $index)"
                  [cdkDragData]="{ type: 'content', init: getContentTypeEnumString(contentType) }">
                  <div class="drag-placeholder drag-placeholder-content" *cdkDragPlaceholder>
                    <ng-container *ngTemplateOutlet="CMSContentType"></ng-container>
                  </div>
                  <ng-template cdkDragPreview [matchSize]="true">
                    <div class="add-content-button-preview">
                      <ng-container [ngTemplateOutlet]="CMSContentType"></ng-container>
                    </div>
                  </ng-template>
                  <ng-container *ngTemplateOutlet="CMSContentType"></ng-container>

                  <!-- This should extend if you have to overwrite a title -->
                  <ng-template #CMSContentType>
                    @switch (domainKey) {
                      @case ('vilaggazdasag') {
                        @switch (contentType) {
                          @case ('text-box') {
                            {{ 'CMS.Layouts.layout-content-type-knowledge-box' | translate }}
                          }
                        }
                      }
                      @default {
                        {{ getContentTypeEnumString(contentType) | layoutContentName | translate }}
                      }
                    }
                  </ng-template>
                </button>
              }
            </div>
          </div>
        }
      </div>
    }

    <div class="layout" [class.sidebar]="type === 'Sidebar' || type === 'ColumnSidebar'">
      <div class="preview" [class.sidebar]="type === 'Sidebar' || type === 'ColumnSidebar'">
        <!-- start recursion on the template -->
        <ng-template
          [ngTemplateOutlet]="verticalTemplate"
          [ngTemplateOutletContext]="{
          $implicit: { elements: rootElements, parent: null },
        }"
        >
        </ng-template>
      </div>
    </div>
  </div>

}


<!-- vertical recursive template -->
<ng-template #verticalTemplate let-element>
  <div class="element-children"
       cdkDropList
       appDragAndDropLayout
       [cdkDropListDisabled]="isLoading()"
       (cdkDropListDropped)="dropped($event)"
       [cdkDropListData]="element">
    @for (layoutElement of element.elements; track layoutElement.id) {
      @switch (layoutElement.type) {
        @case ('row') {
          <div
            class="layout-element row-element row"
            [class.active]="activeElement?.id === layoutElement.id"
            (click)="selectElement($event, layoutElement)"
            (drop)="onDrop($event, layoutElement)"
            cdkDrag
            cdkDragPreviewClass="layout-drag-preview"
            cdkDragBoundary=".layout > .preview"
            [cdkDragData]="layoutElement"
            [style.background-color]="layoutElement.backgroundColor ?? 'transparent'"
            [class.has-custom-background]="layoutElement.backgroundColor?.length > 0"
          >
            <div class="drag-placeholder drag-placeholder-row" *cdkDragPlaceholder></div>
            <div class="element-head">
              <div class="element-type">Soros elrendezés</div>
              @if (!isReadonly) {
                <div class="element-controls">
                  <!--<a href=""><i nz-icon nzType="drag" nzTheme="outline"></i></a>-->
                  <a (click)="swapChildElements($event, element.parent, $index, $index - 1)" [class.disabled]="$first">
                    <i nz-icon nzType="arrow-up" nzTheme="outline"></i>
                  </a>
                  <a (click)="swapChildElements($event, element.parent, $index, $index + 1)" [class.disabled]="$last">
                    <i nz-icon nzType="arrow-down" nzTheme="outline"></i>
                  </a>
                  <a (click)="onDelete(layoutElement, element.parent)"><i nz-icon nzType="delete"
                                                                          nzTheme="fill"></i></a>
                  <a (click)="onConfigButtonClick(layoutElement.type, layoutElement, element.parent)">
                    <i nz-icon nzType="setting" nzTheme="fill"></i>
                  </a>
                </div>
              }
            </div>
            <div class="element-body">
              @if (layoutElement?.withBlockTitle) {
                <ng-container
                  [ngTemplateOutlet]="blockTitle"
                  [ngTemplateOutletContext]="{ element: layoutElement }">
                </ng-container>
              }
              <app-load-with-idle
                [delayUntilIdle]="!$first"
                [shouldEmitWhenLoaded]="$last && element.parent === null"
                (isContentLoaded)="isLoading.set(false)"
                [template]="horizontalTemplate"
                [data]="{
                  elements: layoutElement.elements,
                  parent: layoutElement,
                }">
              </app-load-with-idle>
            </div>
          </div>
        }
        @case ('content') {
          <div class="layout-element content-element" cdkDrag [cdkDragData]="layoutElement">
            <!--<div *cdkDragPlaceholder [ngStyle]="{'min-height.px':dragDropCurrentElementHeight}">
            <ng-template [ngTemplateOutlet]="content" [ngTemplateOutletContext]="element"></ng-template>
          </div>-->
            <div class="drag-placeholder drag-placeholder-content" *cdkDragPlaceholder>
              {{ layoutElement.secondaryContentType || layoutElement.contentType | layoutContentName | translate }}
            </div>
            <div class="drag-preview drag-preview-content layout-element content-element" *cdkDragPreview>
              <ng-template [ngTemplateOutlet]="content" [ngTemplateOutletContext]="element"></ng-template>
            </div>
            <ng-template [ngTemplateOutlet]="content" [ngTemplateOutletContext]="element"></ng-template>
            <ng-template #content>
              <div class="element-head">
                <div class="element-type">
                  @if (domainKey === 'vilaggazdasag' && layoutElement.contentType === 'text-box') {
                    {{ 'CMS.Layouts.layout-content-type-knowledge-box' | translate }}
                  } @else {
                    {{ layoutElement.secondaryContentType || layoutElement.contentType | layoutContentName | translate }}
                  }
                </div>
                @if (!isReadonly) {
                  <div class="element-controls">
                    <a><i nz-icon nzType="drag" nzTheme="outline" nz-tooltip
                          [nzTooltipTitle]="'CMS.Layouts.layout-drag-content-tooltip' | translate"></i></a>
                    <a (click)="swapChildElements($event, element.parent, $index, $index - 1)"
                       [class.disabled]="$first">
                      <i nz-icon nzType="arrow-up" nzTheme="outline"></i>
                    </a>
                    <a (click)="swapChildElements($event, element.parent, $index, $index + 1)" [class.disabled]="$last">
                      <i nz-icon nzType="arrow-down" nzTheme="outline"></i>
                    </a>
                    <a (click)="onDelete(layoutElement, element.parent)"><i nz-icon nzType="delete" nzTheme="fill"></i></a>
                    <a (click)="onConfigButtonClick(layoutElement.contentType, layoutElement, element.parent)">
                      <i nz-icon nzType="setting" nzTheme="fill"></i>
                    </a>
                  </div>
                }
              </div>
              <div class="element-body wrapper {{ domainKey }}">
                @if (layoutElement?.withBlockTitle) {
                  <ng-container
                    [ngTemplateOutlet]="blockTitle"
                    [ngTemplateOutletContext]="{ element: layoutElement }"
                  ></ng-container>
                }
                @if (layoutElement?.showHeader) {
                  <ng-container
                    [ngTemplateOutlet]="elementHeader"
                    [ngTemplateOutletContext]="{
                    $implicit: {
                      element: layoutElement,
                    },
                  }"
                  ></ng-container>
                }
                <div class="preview-images">
                  @if (layoutElement?.useComponent) {
                    <koponyeg-detection-cards-masonry
                      *onlyFor="'koponyeg'"
                      [autoCols]="isAutoColsMasonry(layoutElement)"
                      [data]="getKoponyegMasonryMockData() | slice: 0 : layoutElement?.contentLength"
                    >
                    </koponyeg-detection-cards-masonry>
                  } @else {
                    @if (layoutElement?.firstPreviewImage) {
                      <img
                        class="preview-image multipart"
                        [src]="layoutElement.firstPreviewImage"
                        alt=""
                      />
                    }

                    @for (i of getIteratorArray(layoutElement); track i) {
                      @if (layoutElement.contentType === contentType.AD) {
                        <app-advertisement-placeholder
                          [bannerName]="layoutElement.bannerName"
                          [medium]="layoutElement.medium">
                        </app-advertisement-placeholder>
                      } @else {
                        @if (!layoutElement?.previewImages) {
                          <img
                            class="preview-image"
                            [src]="layoutElement.previewImage"
                            alt=""
                            [class.multipart]="layoutElement?.firstPreviewImage"
                          />
                        } @else {
                          <img
                            class="preview-image"
                            [class.multipart]="layoutElement?.firstPreviewImage"
                            [src]="layoutElement?.previewImages[i]?.previewImage"
                            alt=""
                          />
                        }
                      }
                    }
                    @if (layoutElement?.lastviewImage) {
                      <img
                        class="preview-image multipart"
                        [src]="layoutElement.lastviewImage"
                        alt=""
                      />
                    }
                  }
                </div>
                @if (layoutElement?.manualListOrder || layoutElement?.mobileOrder) {
                  <div class="element-bottom">
                    @if (layoutElement?.manualListOrder) {
                      <div class="element-type">
                      <span>
                        {{ layoutElement.manualListOrder }}#{{ layoutElement.manualList?.title ?? '(névtelen lista)' }}
                      </span>
                      </div>
                    }
                    @if (layoutElement?.mobileOrder) {
                      <div class="element-type right">
                        <span> {{ layoutElement.mobileOrder }} mobil </span>
                      </div>
                    }
                  </div>
                }
              </div>
            </ng-template>
          </div>
        }
      }
    }
  </div>
</ng-template>

<!-- horizontal recursive template -->
<ng-template #horizontalTemplate let-element>
  <div
    class="element-children row"
    cdkDropList
    appDragAndDropLayout
    cdkDropListOrientation="horizontal"
    [cdkDropListDisabled]="isLoading()"
    (cdkDropListDropped)="dropped($event)"
    [cdkDropListData]="element"
  >
    @for (layoutElement of element.elements; track layoutElement.id) {
      @switch (layoutElement.type) {
        @case ('column') {
          <div [ngClass]="'col-' + layoutElement.widthDesktop" cdkDrag [cdkDragData]="layoutElement">
            <div class="drag-placeholder drag-placeholder-column" [ngClass]="'col-' + layoutElement.widthDesktop"
                 *cdkDragPlaceholder></div>
            <div
              class="layout-element column-element"
              [class.active]="activeElement?.id === layoutElement.id"
              (click)="selectElement($event, layoutElement)"
              (drop)="onDrop($event, layoutElement)"
              [class.has-left-column-border]="layoutElement.hasLeftMargin"
              [class.has-right-column-border]="layoutElement.hasRightMargin"
              [class]="'column-border-color-' + layoutElement.marginBorderColor"
            >
              <div class="element-head">
                @if (layoutElement.widthDesktop > 3) {
                  <div class="element-type">Oszlopos elrendezés</div>
                }
                @if (!isReadonly) {
                  <div class="element-controls" [ngClass]="{ small: layoutElement.widthDesktop < 4 }">
                    <!-- TODO: replace anchor with button? -->
                    <!-- <a href=""><i nz-icon nzType="drag" nzTheme="outline"></i></a> -->
                    <a (click)="swapChildElements($event, element.parent, $index, $index - 1)"
                       [class.disabled]="$first">
                      <i nz-icon nzType="arrow-left" nzTheme="outline"></i>
                    </a>
                    <a (click)="swapChildElements($event, element.parent, $index, $index + 1)" [class.disabled]="$last">
                      <i nz-icon nzType="arrow-right" nzTheme="outline"></i>
                    </a>
                    <a (click)="onDelete(layoutElement, element.parent)"><i nz-icon nzType="delete" nzTheme="fill"></i></a>
                    <a (click)="onConfigButtonClick(layoutElement.type, layoutElement, element.parent)">
                      <i nz-icon nzType="setting" nzTheme="fill"></i>
                    </a>

                    <!-- Shows only in case of VG -->
                    @if ((domainKey === 'vilaggazdasag' && layoutElement?.mobileOrder) || layoutElement?.mobileOrder === 0) {
                      <span class="column-mobile-order">{{ layoutElement.mobileOrder }} mobil</span>
                    }
                  </div>
                }
              </div>

              <div class="element-body">
                @if (layoutElement?.withBlockTitle) {
                  <ng-container
                    [ngTemplateOutlet]="blockTitle"
                    [ngTemplateOutletContext]="{ element: layoutElement }"
                  ></ng-container>
                }

                <ng-template
                  [ngTemplateOutlet]="verticalTemplate"
                  [ngTemplateOutletContext]="{
                  $implicit: {
                    elements: layoutElement.elements,
                    parent: layoutElement
                    },
                  }"
                >
                </ng-template>
              </div>
            </div>
          </div>
        }
        @case ('content') {
          <div class="col-12">
            <div class="layout-element content-element" cdkDrag [cdkDragPreviewClass]="'layout-drag-preview'"
                 [cdkDragData]="layoutElement">
              <div class="drag-preview drag-preview-content layout-element content-element" *cdkDragPreview>
                <ng-template [ngTemplateOutlet]="content" [ngTemplateOutletContext]="element"></ng-template>
              </div>
              <div class="drag-placeholder drag-placeholder-content" *cdkDragPlaceholder></div>
              <ng-template [ngTemplateOutlet]="content" [ngTemplateOutletContext]="element"></ng-template>

              <ng-template #content>
                <div class="element-head">
                  <div class="element-type">
                    {{ layoutElement.secondaryContentType || layoutElement.contentType | layoutContentName | translate }}
                  </div>
                  @if (!isReadonly) {
                    <div class="element-controls">
                      <a><i nz-icon nzType="drag" nzTheme="outline" nz-tooltip
                            [nzTooltipTitle]="'CMS.Layouts.layout-drag-content-tooltip' | translate"></i></a>
                      <a (click)="onDelete(layoutElement, element.parent)"><i nz-icon nzType="delete"
                                                                              nzTheme="fill"></i></a>
                      <a (click)="onConfigButtonClick(layoutElement.contentType, layoutElement, element.parent)">
                        <i nz-icon nzType="setting" nzTheme="fill"></i>
                      </a>
                    </div>
                  }
                </div>
                <div class="element-body">
                  @if (layoutElement?.withBlockTitle) {
                    <ng-container
                      [ngTemplateOutlet]="blockTitle"
                      [ngTemplateOutletContext]="{ element: layoutElement }"
                    ></ng-container>
                  }
                  @if (layoutElement?.showHeader) {
                    <ng-container
                      [ngTemplateOutlet]="elementHeader"
                      [ngTemplateOutletContext]="{
                      $implicit: {
                        element: layoutElement,
                      },
                    }"
                    ></ng-container>
                  }

                  <div class="row preview-images">
                    @for (i of getIteratorArray(layoutElement); track i) {
                      <div [ngClass]="'col-' + getContentElementColumnRate(layoutElement)">
                        @if (layoutElement.contentType === contentType.AD) {
                          <app-advertisement-placeholder
                            [bannerName]="layoutElement.bannerName"
                            [medium]="layoutElement.medium"
                          >
                          </app-advertisement-placeholder>
                        }
                        @if (layoutElement?.firstPreviewImage) {
                          <img
                            class="preview-image multipart"
                            [src]="layoutElement.firstPreviewImage"
                            alt=""
                          />
                        }
                        <img class="preview-image" [src]="layoutElement.previewImage" alt=""
                             [class.multipart]="layoutElement?.firstPreviewImage" />
                        @if (layoutElement?.lastviewImage) {
                          <img
                            class="preview-image multipart"
                            [src]="layoutElement.lastviewImage"
                            alt=""
                          />
                        }
                      </div>
                    }
                  </div>
                </div>
                @if (layoutElement?.manualOrder || layoutElement?.mobileOrder) {
                  <div class="element-bottom">
                    @if (layoutElement?.manualOrder) {
                      <div class="element-type">
                        <span> {{ layoutElement.manualOrder }}#{{ layoutElement.manualList }} </span>
                      </div>
                    }
                    @if (layoutElement?.mobileOrder) {
                      <div class="element-type right">
                        <span> {{ layoutElement.mobileOrder }} mobil </span>
                      </div>
                    }
                  </div>
                }
              </ng-template>
            </div>
          </div>
        }
      }
    }
  </div>
</ng-template>

<ng-template #blockTitle let-element="element">
  <div class="row">
    <div class="col-12">
      @switch (domainKey) {
        @case ('vilaggazdasag') {
          <img src="./assets/images/layout-frames/vg/block-title.png" alt="Block title" />
        }
        @case ('bors') {
          <img src="./assets/images/layout-frames/bors/block-title01.png" alt="Block title" />
        }
        @case ('koponyeg') {
          <img src="./assets/images/layout-frames/koponyeg/block-title.png" alt="Block title" />
        }
        @case ('nso') {
          <img class="block-title" src="./assets/images/layout-frames/nso/block-title01.png" alt="Block title" />
        }
        @case ('szabadfold') {
          <img class="block-title" src="./assets/images/layout-frames/szabadfold/block-title.png" alt="Block title" />
        }
        @case ('magyarNemzet') {
          @if (type === 'Sidebar') {
            <img src="./assets/images/layout-frames/mno/block-title-sidebar.png" alt="Block title" />
          } @else {
            <img src="./assets/images/layout-frames/mno/block-title.png" alt="Block title" />
          }
        }
        @case ('ripost') {
          @if (type === 'Sidebar') {
            <img src="./assets/images/layout-frames/ripost/block-title-sidebar.png" alt="Block title" />
          } @else {
            <img src="./assets/images/layout-frames/ripost/block-title01.png" alt="Block title" />
          }
        }
        @case ('metropol') {
          @if (type === 'Sidebar') {
            <img src="./assets/images/layout-frames/metropol/block-title-sidebar01.png" alt="Block title" />
          } @else {
            <img src="./assets/images/layout-frames/metropol/block-title01.png" alt="Block title" />
          }
        }
        @case ('mandiner') {
          @if (type === 'Sidebar') {
            <img src="./assets/images/layout-frames/mandiner/block-title-sidebar01.png" alt="Block title" />
          } @else {
            <img src="./assets/images/layout-frames/mandiner/block-title01.png" alt="Block title" />
          }
        }
        @case ('szegedma') {
          <img class="block-title" src="./assets/images/layout-frames/szegedma/block-title.png" alt="Block title" />
        }
        @case ('life') {
          @if (element?.blockTitle?.sponsored) {
            <img
              class="block-title"
              src="./assets/images/layout-frames/life/block-title-sponsor.jpg"
              alt="Block title"
            />
          } @else {
            <img class="block-title" src="./assets/images/layout-frames/life/block-title.jpg" alt="Block title" />
          }
        }
        @case ('mindmegette') {
          <img class="block-title" src="./assets/images/layout-frames/mme/block-title.png" alt="Block title" />
        }
      }
      <img *onlyForMegyeiLapok src="./assets/images/layout-frames/megyeilapok/block-title.png" alt="Block title" />
    </div>
  </div>
</ng-template>

<ng-template #elementHeader let-element>
  <div class="row">
    @switch (element?.element?.contentType) {
      @case ('ingatlanbazar-configurable') {
        <img src="./assets/images/layout-frames/metropol/ingatlanbazar-header-white.png" alt="Ingatlanbazár header"
             style="width: 100%" />
      }
    }
  </div>
</ng-template>
