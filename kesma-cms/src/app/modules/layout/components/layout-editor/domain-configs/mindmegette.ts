import { LayoutEditorSettings } from '../../../layout-editor-configuration.definitions';
import { LayoutElementContentType, LayoutPageType } from '../../../layout-core.definitions';
import { PortalConfigSetting } from '@shared/definitions/portal-config';

export const settings: LayoutEditorSettings[] = [
  // TODO: kon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> be<PERSON>í<PERSON>a story-k alapján
  {
    canAddColumn: true,
    canAddRow: true,
    enabledContentTypes: [
      LayoutElementContentType.ARTICLE,
      LayoutElementContentType.QUIZ,
      LayoutElementContentType.VOTE,
      LayoutElementContentType.MOST_VIEWED,
      LayoutElementContentType.PR_BLOCK,
      LayoutElementContentType.NEWSLETTER_BLOCK,
      LayoutElementContentType.RECIPE_CATEGORY_SELECT,
      LayoutElementContentType.ARTICLE_SLIDER,
      LayoutElementContentType.AD,
      LayoutElementContentType.AUTHOR,
      LayoutElementContentType.TURPI_BOX,
      LayoutElementContentType.TURPI_CARD,
      LayoutElementContentType.INGREDIENT,
      LayoutElementContentType.RECIPE,
      LayoutElementContentType.RECIPE_SWIPER,
      LayoutElementContentType.GUARANTEE_BOX,
      LayoutElementContentType.OFFER_BOX,
      LayoutElementContentType.BRANDING_BOX_EX,
      LayoutElementContentType.MAESTRO_BOX,
      LayoutElementContentType.DAILY_MENU,
      LayoutElementContentType.SELECTION,
      LayoutElementContentType.WEEKLY_MENU,
      LayoutElementContentType.GASTRO_EXPERIENCE_OCCASION,
      LayoutElementContentType.TOP_RANKING_GLOSSARY,
      {
        type: LayoutElementContentType.CONFIGURABLE_SPONSORED_BOX,
        requiredPortalConfigs: [PortalConfigSetting.CONTENT_SPONSORSHIP_SPONSORED_BOX],
      },
      {
        type: LayoutElementContentType.EXPERIENCE_GIFT,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_GASTRO_EXPERIENCE],
      },
      {
        type: LayoutElementContentType.GASTRO_OCCASION_RECOMMENDER,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_GASTRO_EXPERIENCE],
      },
      {
        type: LayoutElementContentType.GASTRO_EXPERIENCE_RECOMMENDATION,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_GASTRO_EXPERIENCE],
      },
      {
        type: LayoutElementContentType.EVENT_CALENDAR,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_GASTRO_EXPERIENCE],
      },
      {
        type: LayoutElementContentType.GASTRO_THEMATIC_RECOMMENDER,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_GASTRO_EXPERIENCE],
      },
      {
        type: LayoutElementContentType.GASTRO_EXPERIENCE_OCCASION_SWIPER,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_GASTRO_EXPERIENCE],
      },
      {
        type: LayoutElementContentType.SECRET_DAYS_CALENDAR,
        requiredPortalConfigs: [PortalConfigSetting.MENU_TYPE_SECRET_DAYS_CALENDAR],
      },
      /*{
        type: LayoutElementContentType.SPONSORED_QUIZ,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_AVAILABLE_QUIZZES],
      },*/
    ],
    pageType: LayoutPageType.HOME,
    layoutWidth: 1200,
  },
  {
    canAddColumn: true,
    canAddRow: true,
    enabledContentTypes: [
      LayoutElementContentType.ARTICLE,
      LayoutElementContentType.QUIZ,
      LayoutElementContentType.VOTE,
      LayoutElementContentType.MOST_VIEWED,
      LayoutElementContentType.PR_BLOCK,
      LayoutElementContentType.NEWSLETTER_BLOCK,
      LayoutElementContentType.RECIPE_CATEGORY_SELECT,
      LayoutElementContentType.WEEKLY_MENU,
      LayoutElementContentType.ARTICLE_SLIDER,
      LayoutElementContentType.TEXT_BOX,
      LayoutElementContentType.AD,
      LayoutElementContentType.TURPI_BOX,
      LayoutElementContentType.AUTHOR,
      LayoutElementContentType.TURPI_CARD,
      LayoutElementContentType.INGREDIENT,
      LayoutElementContentType.RECIPE,
      LayoutElementContentType.RECIPE_SWIPER,
      LayoutElementContentType.GUARANTEE_BOX,
      LayoutElementContentType.OFFER_BOX,
      LayoutElementContentType.MAESTRO_BOX,
      LayoutElementContentType.DAILY_MENU,
      LayoutElementContentType.GASTRO_EXPERIENCE_OCCASION,
      LayoutElementContentType.TOP_RANKING_GLOSSARY,
      {
        type: LayoutElementContentType.CONFIGURABLE_SPONSORED_BOX,
        requiredPortalConfigs: [PortalConfigSetting.CONTENT_SPONSORSHIP_SPONSORED_BOX],
      },
      {
        type: LayoutElementContentType.EXPERIENCE_GIFT,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_GASTRO_EXPERIENCE],
      },
      {
        type: LayoutElementContentType.GASTRO_OCCASION_RECOMMENDER,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_GASTRO_EXPERIENCE],
      },
      {
        type: LayoutElementContentType.GASTRO_EXPERIENCE_RECOMMENDATION,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_GASTRO_EXPERIENCE],
      },
      {
        type: LayoutElementContentType.EVENT_CALENDAR,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_GASTRO_EXPERIENCE],
      },
      {
        type: LayoutElementContentType.GASTRO_THEMATIC_RECOMMENDER,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_GASTRO_EXPERIENCE],
      },
      {
        type: LayoutElementContentType.GASTRO_EXPERIENCE_OCCASION_SWIPER,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_GASTRO_EXPERIENCE],
      },
      {
        type: LayoutElementContentType.SECRET_DAYS_CALENDAR,
        requiredPortalConfigs: [PortalConfigSetting.MENU_TYPE_SECRET_DAYS_CALENDAR],
      },
      /*{
        type: LayoutElementContentType.SPONSORED_QUIZ,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_AVAILABLE_QUIZZES],
      },*/
    ],
    pageType: LayoutPageType.COLUMN,
    layoutWidth: 1200,
  },
  {
    canAddColumn: false,
    canAddRow: false,
    enabledContentTypes: [
      LayoutElementContentType.ARTICLE,
      LayoutElementContentType.QUIZ,
      LayoutElementContentType.VOTE,
      LayoutElementContentType.MOST_VIEWED,
      LayoutElementContentType.PR_BLOCK,
      LayoutElementContentType.NEWSLETTER_BLOCK,
      LayoutElementContentType.ARTICLE_SLIDER,
      LayoutElementContentType.AD,
      LayoutElementContentType.TURPI_CARD,
      LayoutElementContentType.INGREDIENT,
      LayoutElementContentType.RECIPE,
      LayoutElementContentType.RECIPE_SWIPER,
      LayoutElementContentType.GUARANTEE_BOX,
      LayoutElementContentType.OFFER_BOX,
      LayoutElementContentType.DAILY_MENU,
      LayoutElementContentType.GASTRO_EXPERIENCE_OCCASION,
      LayoutElementContentType.TOP_RANKING_GLOSSARY,
      {
        type: LayoutElementContentType.CONFIGURABLE_SPONSORED_BOX,
        requiredPortalConfigs: [PortalConfigSetting.CONTENT_SPONSORSHIP_SPONSORED_BOX],
      },
      {
        type: LayoutElementContentType.EXPERIENCE_GIFT,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_GASTRO_EXPERIENCE],
      },
      {
        type: LayoutElementContentType.GASTRO_EXPERIENCE_RECOMMENDATION,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_GASTRO_EXPERIENCE],
      },
      {
        type: LayoutElementContentType.EVENT_CALENDAR,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_GASTRO_EXPERIENCE],
      },
      {
        type: LayoutElementContentType.GASTRO_THEMATIC_RECOMMENDER,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_GASTRO_EXPERIENCE],
      },
      {
        type: LayoutElementContentType.GASTRO_EXPERIENCE_OCCASION_SWIPER,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_GASTRO_EXPERIENCE],
      },
      /*{
        type: LayoutElementContentType.SPONSORED_QUIZ,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_AVAILABLE_QUIZZES],
      },*/
      {
        type: LayoutElementContentType.SPONSORED_VOTE,
        requiredPortalConfigs: [PortalConfigSetting.VOTING_EXTENDED_WITH_ADOCEAN],
      },
      {
        type: LayoutElementContentType.SECRET_DAYS_CALENDAR,
        requiredPortalConfigs: [PortalConfigSetting.MENU_TYPE_SECRET_DAYS_CALENDAR],
      },
      {
        type: LayoutElementContentType.DID_YOU_KNOW,
        requiredPortalConfigs: [PortalConfigSetting.VARIABLE_SPONSORED_CONTENT_FROM_DID_YOU_KNOW],
      },
    ],
    pageType: LayoutPageType.SIDEBAR,
    layoutWidth: 300,
  },
  {
    canAddColumn: false,
    canAddRow: false,
    enabledContentTypes: [
      LayoutElementContentType.ARTICLE,
      LayoutElementContentType.QUIZ,
      LayoutElementContentType.VOTE,
      LayoutElementContentType.MOST_VIEWED,
      LayoutElementContentType.PR_BLOCK,
      LayoutElementContentType.NEWSLETTER_BLOCK,
      LayoutElementContentType.ARTICLE_SLIDER,
      LayoutElementContentType.AD,
      LayoutElementContentType.TURPI_CARD,
      LayoutElementContentType.INGREDIENT,
      LayoutElementContentType.RECIPE,
      LayoutElementContentType.RECIPE_SWIPER,
      LayoutElementContentType.GUARANTEE_BOX,
      LayoutElementContentType.OFFER_BOX,
      LayoutElementContentType.DAILY_MENU,
      LayoutElementContentType.GASTRO_EXPERIENCE_OCCASION,
      LayoutElementContentType.TOP_RANKING_GLOSSARY,
      {
        type: LayoutElementContentType.CONFIGURABLE_SPONSORED_BOX,
        requiredPortalConfigs: [PortalConfigSetting.CONTENT_SPONSORSHIP_SPONSORED_BOX],
      },
      {
        type: LayoutElementContentType.EXPERIENCE_GIFT,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_GASTRO_EXPERIENCE],
      },
      {
        type: LayoutElementContentType.GASTRO_EXPERIENCE_RECOMMENDATION,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_GASTRO_EXPERIENCE],
      },
      {
        type: LayoutElementContentType.EVENT_CALENDAR,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_GASTRO_EXPERIENCE],
      },
      {
        type: LayoutElementContentType.GASTRO_THEMATIC_RECOMMENDER,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_GASTRO_EXPERIENCE],
      },
      {
        type: LayoutElementContentType.GASTRO_EXPERIENCE_OCCASION_SWIPER,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_GASTRO_EXPERIENCE],
      },
      /*{
        type: LayoutElementContentType.SPONSORED_QUIZ,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_AVAILABLE_QUIZZES],
      },*/
      {
        type: LayoutElementContentType.SPONSORED_VOTE,
        requiredPortalConfigs: [PortalConfigSetting.VOTING_EXTENDED_WITH_ADOCEAN],
      },
      {
        type: LayoutElementContentType.SECRET_DAYS_CALENDAR,
        requiredPortalConfigs: [PortalConfigSetting.MENU_TYPE_SECRET_DAYS_CALENDAR],
      },
      {
        type: LayoutElementContentType.DID_YOU_KNOW,
        requiredPortalConfigs: [PortalConfigSetting.VARIABLE_SPONSORED_CONTENT_FROM_DID_YOU_KNOW],
      },
    ],
    pageType: LayoutPageType.COLUMNSIDEBAR,
    layoutWidth: 300,
  },
  {
    canAddColumn: true,
    canAddRow: true,
    enabledContentTypes: [
      LayoutElementContentType.ARTICLE,
      LayoutElementContentType.QUIZ,
      LayoutElementContentType.MOST_VIEWED,
      LayoutElementContentType.PR_BLOCK,
      LayoutElementContentType.NEWSLETTER_BLOCK,
      LayoutElementContentType.RECIPE_CATEGORY_SELECT,
      LayoutElementContentType.WEEKLY_MENU,
      LayoutElementContentType.ARTICLE_SLIDER,
      LayoutElementContentType.TEXT_BOX,
      LayoutElementContentType.AD,
      LayoutElementContentType.AUTHOR,
      LayoutElementContentType.TURPI_CARD,
      LayoutElementContentType.INGREDIENT,
      LayoutElementContentType.RECIPE,
      LayoutElementContentType.RECIPE_SWIPER,
      LayoutElementContentType.GUARANTEE_BOX,
      LayoutElementContentType.OFFER_BOX,
      LayoutElementContentType.HIGHLIGHTED_SELECTION,
      LayoutElementContentType.DAILY_MENU,
      LayoutElementContentType.BRANDING_BOX_EX,
      LayoutElementContentType.MAESTRO_BOX,
      LayoutElementContentType.TURPI_BOX,
      LayoutElementContentType.SELECTION,
      LayoutElementContentType.VOTE,
      LayoutElementContentType.TOP_RANKING_GLOSSARY,
      {
        type: LayoutElementContentType.CONFIGURABLE_SPONSORED_BOX,
        requiredPortalConfigs: [PortalConfigSetting.CONTENT_SPONSORSHIP_SPONSORED_BOX],
      },
      {
        type: LayoutElementContentType.EVENT_CALENDAR,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_GASTRO_EXPERIENCE],
      },
      LayoutElementContentType.GASTRO_EXPERIENCE_OCCASION,
      {
        type: LayoutElementContentType.EXPERIENCE_GIFT,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_GASTRO_EXPERIENCE],
      },
      {
        type: LayoutElementContentType.GASTRO_EXPERIENCE_RECOMMENDATION,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_GASTRO_EXPERIENCE],
      },
      {
        type: LayoutElementContentType.GASTRO_OCCASION_RECOMMENDER,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_GASTRO_EXPERIENCE],
      },
      {
        type: LayoutElementContentType.GASTRO_THEMATIC_RECOMMENDER,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_GASTRO_EXPERIENCE],
      },
      {
        type: LayoutElementContentType.GASTRO_EXPERIENCE_OCCASION_SWIPER,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_GASTRO_EXPERIENCE],
      },
      {
        type: LayoutElementContentType.SECRET_DAYS_CALENDAR,
        requiredPortalConfigs: [PortalConfigSetting.MENU_TYPE_SECRET_DAYS_CALENDAR],
      },
      /*{
        type: LayoutElementContentType.SPONSORED_QUIZ,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_AVAILABLE_QUIZZES],
      },*/
    ],
    pageType: LayoutPageType.CUSTOM_BUILT_PAGE,
    layoutWidth: 1200,
  },
];
