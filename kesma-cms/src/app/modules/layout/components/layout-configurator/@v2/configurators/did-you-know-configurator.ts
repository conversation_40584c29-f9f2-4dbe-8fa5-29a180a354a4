import { ContentElementItemType } from '@modules/layout/layout-core.definitions';
import { inject, Injectable } from '@angular/core';
import { IHttpOptions } from '@external/http';
import { map } from 'rxjs/operators';
import { LayoutConfiguratorDef, LayoutElementDynamicConfig } from '@modules/layout/components/layout-configurator/@v2/layout-configurator-definitions';
import { ApiService } from '@core/services/api.service';

@Injectable()
export class DidYouKnowConfigurator implements LayoutConfiguratorDef {
  protected readonly apiService = inject(ApiService);

  title: string = 'Változó tartalmú branding box konfigurálása';
  contentElementItemType: ContentElementItemType = 'selectedDidYouKnowBox' as any;
  cannotModifyContentLength: boolean = true;
  contentElementsAreRequired: boolean = true;

  initialConfig(): LayoutElementDynamicConfig {
    return {
      selectedDidYouKnowBox: [null],
    };
  }

  sourceRequest(options: IHttpOptions) {
    return this.apiService.didYouKnow.getAll(options).pipe(
      map(({ data, meta }) => {
        return {
          data: data.map((box) => ({
            id: box.id,
            title: box.title || box.boxName,
          })),
          meta,
        };
      })
    );
  }

  beforeClose = (config: LayoutElementDynamicConfig): LayoutElementDynamicConfig => {
    return config;
  };
}
