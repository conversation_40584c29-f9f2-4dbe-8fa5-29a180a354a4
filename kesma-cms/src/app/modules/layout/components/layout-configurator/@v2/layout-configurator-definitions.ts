import { ApiResult } from '@trendency/kesma-ui';
import { IHttpOptions } from '@external/http';
import { BehaviorSubject, Observable } from 'rxjs';
import { DomainKey } from '@core/modules/admin/admin.definitions';
import { PortalConfigSetting } from '@shared/definitions/portal-config';
import { ContentElementItemType, LayoutElementContent, LayoutElementContentType } from '@modules/layout/layout-core.definitions';
import { BaseLayoutConfiguratorComponent, LayoutConfiguratorBuilderComponent } from '@modules/layout/components/layout-configurator/@v2/components';
import { Type } from '@angular/core';

export type LayoutConfigurator<Config extends LayoutConfiguratorDef = LayoutConfiguratorDef> = {
  configuratorType: Type<Config>;
  instance?: Config;
  customComponent?: Type<BaseLayoutConfiguratorComponent>;
  overwriteCustomComponent?: Type<BaseLayoutConfiguratorComponent>;
  overwriteType?: Type<LayoutConfiguratorOverwriteDef>;
  overwriteInstance?: LayoutConfiguratorOverwriteDef;
  allowFullOverwrite?: boolean;
  supportedContentTypes: LayoutElementContentType[];
};

export type LayoutConfiguratorOverwriteDef = Omit<LayoutConfiguratorDef, 'initialConfig'>;

export type LayoutConfiguratorDef = {
  title: string; // Title of the nzModal.
  width?: number; // Width of the nzModal, default 900.
  initialConfig: (content: LayoutElementContent) => LayoutElementDynamicConfig;
  initialConfigAsync?: (configuration: LayoutElementDynamicConfig, content: LayoutElementContent) => void;
  /**
   * It works like OnInit.
   */
  beforeOpen?: (config?: LayoutElementDynamicConfig, content?: LayoutElementContent) => void;
  /**
   * We use this function to reset the custom states declared in the configurator service to their default values,
   * but we can also modify the configuration structure by overriding it with the value passed as a parameter.
   * For example: {@link AuthorConfigurator}
   *
   * Also, useful for any interaction required before closing the modal window.
   */
  beforeClose?: (config?: LayoutElementDynamicConfig, content?: LayoutElementContent) => LayoutElementDynamicConfig;
  /**
   * By providing the formInfo, we can individually add any other fields that may affect the appearance of our component.
   * @example {@link ArticleConfigurator}
   */
  formInfo?: LayoutConfiguratorFormInfo[];
  contentElementItemType?: ContentElementItemType;
  contentElementItemDisplayedTitle?: (config: LayoutElementDynamicConfig) => string;
  contentElementsAreRequired?: boolean;
  contentLengthMin?: number; // By default 1.
  contentLengthMax?: number; // By default 15.
  secondaryContentElementItemType?: ContentElementItemType;
  /**
   * Required if contentElementItemType is provided.
   */
  sourceRequest?: <T>(options: IHttpOptions, element?: LayoutElementContent, config?: LayoutElementDynamicConfig) => Observable<ApiResult<T[]>>;
  viewRequest?: (contentId: string, selectedContent: LayoutConfigurationResult) => Observable<unknown>;
  canTiming?: boolean; // For articles.
  cannotModifyContentLength?: boolean;
  keepOriginalWithSelection?: boolean; // !!! Backward compatibility !!! Do not use in the future.
  forceHideAutoFill?: boolean;
  useColumnFilterIfExists?: boolean;
  isArraySizeAffected?: boolean;
  contentSelectDisplayOptions?: {
    showThumbnail?: boolean;
    thumbnailProp?: (item: LayoutElementDynamicConfig) => string;
  };
  /**
   * Sets initialConfig in the configurator even if configurable: false.
   */
  shouldForceInitialConfig?: boolean;
};

export type LayoutConfiguratorFormInfo = Readonly<{
  fieldType: LayoutConfiguratorFormFieldType;
  label: string;
  subLabel?: string;
  displayProperty?: string; // Title is the default, works only with LayoutConfiguratorFormFieldType.Select.
  sourceRequest?: <T>(options: IHttpOptions) => Observable<ApiResult<T[]>>; // Works only with LayoutConfiguratorFormFieldType.Select.
  explicitSource?: BehaviorSubject<unknown[]>; // Works only with LayoutConfiguratorFormFieldType.Select.
  bindValue?: (data: any) => any; // Works only with LayoutConfiguratorFormFieldType.Select.
  onlyFor?: DomainKey[];
  hideFor?: DomainKey[];
  showIf?: (content: LayoutElementContent & { styleId?: number }, config: LayoutElementDynamicConfig) => boolean;
  requiredPortalConfig?: PortalConfigSetting;
  tooltip?: string;
  multiSelect?: boolean; // Works only with LayoutConfiguratorFormFieldType.Select.
  bindingProperty?: string; // Property name from initialConfig to two-way binding.
  required?: boolean;
  minLength?: number; // By default 1.
  maxLength?: number; // By default 15.
  initialValue?: (config: LayoutElementDynamicConfig) => unknown | string | number;
  triggerFn?: (inputValue: unknown, builderComponent: LayoutConfiguratorBuilderComponent) => void;
  explicitColors?: string[] | Observable<string[]>; // Works only with LayoutConfiguratorFormFieldType.Color.
}>;

export type LayoutConfigurationResult = LayoutConfiguratorTiming & {
  id: string;
  title: string;
  overwrite?: Omit<LayoutElementDynamicConfig, 'autoFill'>;
  original?: Omit<LayoutElementDynamicConfig, 'autoFill'>;
  foregroundColor?: string;
  backgroundColor?: string;
};

export enum LayoutConfiguratorFormFieldType {
  Text = 'text',
  TextArea = 'textarea',
  Number = 'number',
  Checkbox = 'checkbox',
  Select = 'select',
  DateTimeLocal = 'datetimelocal',
  Color = 'color',
  Image = 'image',
  Label = 'label', // To label an input group.
  Alignment = 'alignment',
}

export type LayoutConfiguratorTiming = {
  visibleFrom?: string;
  visibleUntil?: string;
};

export type LayoutElementDynamicConfig = {
  [key: string]: any;
  autoFill?: AutoFillOptions;
} & {
  [key in ContentElementItemType]?: any | any[];
};

export type AutoFillOptions = {
  filterColumns?: AutoFillOptionItem[];
  filterTags?: AutoFillOptionItem[];
  filterSponsorships?: AutoFillOptionItem[];
  filterPriorities?: AutoFillOptionItem[];
  filterAuthors?: AutoFillOptionItem[]; // Public Authors
  filterArticleNetworkSlots?: AutoFillOptionItem[];
  filterDossierNetworkSlots?: AutoFillOptionItem[];
  filterVotingNetworkSlots?: AutoFillOptionItem[];
  filterRegions?: AutoFillOptionItem[];
  filterSports?: AutoFillOptionItem[];
  filterPrTags?: AutoFillOptionItem[];
  filterTypes?: AutoFillOptionItem[];
  filterLocations?: AutoFillOptionItem[];
  filterBrandingBoxTypes?: AutoFillOptionItem[];
  orderPriority?: AutoFillOrderPriority;
  filterNotOpinion?: boolean;
  filterNotPodcast?: boolean;
  filterNotSponsorship?: boolean;
  newest?: boolean;
  filterPodcast?: boolean;
  hasVideo?: boolean;
  hasImage?: boolean;
  hasSecondaryImage?: boolean;
  filterHasGallery?: boolean;
  /** MME specific */
  filterExperienceCategories?: AutoFillOptionItem[];
  filterExperience?: AutoFillOptionItem[];
  filterRecipeCategories?: AutoFillOptionItem[];
  filterSelections?: AutoFillOptionItem[];
  filterOnlyMmeWarranty?: boolean;
  /** /MME specific */
};

export type AutoFillOrderPriority = 'date' | 'popularity' | 'popularity24' | 'popularity48' | 'orderByCommentsCount';

export type AutoFillOptionItem = {
  id: string;
  title: string;
};
