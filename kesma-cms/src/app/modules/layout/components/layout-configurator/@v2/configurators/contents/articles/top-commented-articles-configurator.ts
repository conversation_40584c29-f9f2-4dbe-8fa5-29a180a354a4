import { Injectable } from '@angular/core';
import { LayoutConfiguratorDef, LayoutElementDynamicConfig } from '@modules/layout/components/layout-configurator/@v2/layout-configurator-definitions';

@Injectable()
export class TopCommentedArticlesConfigurator implements LayoutConfiguratorDef {
  title: string = 'Legtöbbet kommentelt cikkek konfigurálása';

  shouldForceInitialConfig: boolean = true;

  initialConfig(): LayoutElementDynamicConfig {
    return {
      selectedArticles: Array(6).fill(null),
      doAutoFill: true,
      autoFill: {
        newest: false,
        orderPriority: 'orderByCommentsCount',
      },
    };
  }
}
