import { Injectable } from '@angular/core';
import { ContentResponse } from 'src/app/core/api.definitons';
import { ISeoScoreCalculationInput } from '../definitions/seo-score-calculation-input';
import { ISeoScoreResult } from '../definitions/seo-score-result';
import { SeoScoreRule } from '../definitions/seo-score-rule';
import { mapContentResponse } from '../mappers/map-content-response';
import * as SeoBlocks from '../utils/seo-score-common-blocks';
import { DomainService } from '@core/modules/admin/services/domain.service';
import { UtilsService } from '@external/utils';
import { SeoPortalTitleService } from './seo-portal-title.service';
import { SeoScoreResultRating } from '../definitions/seo-score-result-rating';
import { SeoScoreConfig, SeoScoreRuleConfig } from '../definitions/seo-score-config';
import { SeoTypeEnum } from '../enums/seo-type.enum';
import { TranslateService } from '@ngx-translate/core';

@Injectable({ providedIn: 'root' })
export class SeoScoreService {
  constructor(
    private readonly domainService: DomainService,
    private readonly utils: UtilsService,
    private readonly portalTitleService: SeoPortalTitleService,
    private readonly translateService: TranslateService
  ) {
    this.initSeoRules();
  }

  lastMappedContent?: ISeoScoreCalculationInput;
  private _seoType: SeoTypeEnum = SeoTypeEnum.ARTICLE;
  set seoType(type: SeoTypeEnum) {
    this._seoType = type;
    this.initSeoRules();
  }

  seoRules: SeoScoreRule[] = [];

  defaultSeoRules = [
    new SeoScoreRule(1, 'focusedKeywordNotEmpty', [SeoBlocks.fieldNotEmpty('focusedKeyword')], 'Fő kulcsszó kitöltve'),
    new SeoScoreRule(
      1,
      'focusedKeywordIsTitleFirstWord',
      [SeoBlocks.fieldNotEmpty('focusedKeyword'), SeoBlocks.seoTitleContainsFocusedKeyword()],
      'Fő kulcsszó a SEO cím első szava'
    ),
    new SeoScoreRule(
      1,
      'focusedKeywordInTitle',
      [SeoBlocks.fieldNotEmpty('focusedKeyword'), SeoBlocks.seoTitleContainsFocusedKeyword()],
      'Fő kulcsszó szerepel a címben'
    ),
    new SeoScoreRule(
      1,
      'focusedKeywordInLeadSecondOrLaterSentence',
      [SeoBlocks.fieldNotEmpty('focusedKeyword'), SeoBlocks.focusedKeywordInLeadSentencesAfter(2)],
      'Fő kulcsszó a lead második vagy további mondatában'
    ),
    new SeoScoreRule(
      1,
      'focusedKeywodInBodyFirstParagraph',
      [SeoBlocks.fieldNotEmpty('focusedKeyword'), SeoBlocks.firstParagraphContainsFocusedKeyword()],
      'Cikktörzs első bekezdése tartalmaz fő kulcsszót',
      true
    ),
    new SeoScoreRule(
      1,
      'imageCaptionContainsFocusedKeyword',
      [SeoBlocks.fieldNotEmpty('focusedKeyword'), SeoBlocks.bodyDomElementContainsFocusedKeyword('figcaption')],
      'Képaláírás tartalmaz fő kulcsszót',
      true
    ),
    new SeoScoreRule(
      1,
      'imageAltContainsFocusedKeyword',
      [SeoBlocks.fieldNotEmpty('focusedKeyword'), SeoBlocks.isImageAltTagHasFocusedKeyword()],
      'Kiemelt kép alt tagja tartalmaz fő kulcsszót',
      true
    ),
    new SeoScoreRule(
      1,
      'h2ContainsFocusedKeyword',
      [SeoBlocks.fieldNotEmpty('focusedKeyword'), SeoBlocks.bodyDomElementContainsFocusedKeyword('h2')],
      'Bekezdéscím (Címsor 1) fő kulcsszót tartalmaz',
      true
    ),
    new SeoScoreRule(1, 'keywordsFilled', [SeoBlocks.fieldNotEmpty('keywords')], 'További kulcsszavak kitöltve'),
    new SeoScoreRule(1, 'longtailsFilled', [SeoBlocks.fieldNotEmpty('longtails')], 'Long-tail kulcsszavak kitöltve'),
    new SeoScoreRule(
      1,
      'allKeywordUsed',
      [SeoBlocks.fieldNotEmpty('focusedKeyword'), SeoBlocks.fieldNotEmpty('keywords'), SeoBlocks.isEveryKeywordUsed()],
      'Minden kulcsszó felhasználásra került'
    ),
    new SeoScoreRule(
      1,
      'keywordCountValid',
      [
        SeoBlocks.or(
          SeoBlocks.every(SeoBlocks.fieldNotEmpty('focusedKeyword'), SeoBlocks.bodyCharCountLessThan(1400)),
          SeoBlocks.every(SeoBlocks.totalKeywordCountAtLeast(3), SeoBlocks.bodyCharCountMoreThanEqual(1400), SeoBlocks.bodyCharCountLessThan(3199)),
          SeoBlocks.every(SeoBlocks.totalKeywordCountAtLeast(5), SeoBlocks.bodyCharCountMoreThanEqual(3200), SeoBlocks.bodyCharCountLessThan(7999)),
          SeoBlocks.every(SeoBlocks.totalKeywordCountAtLeast(6), SeoBlocks.fieldNotEmpty('longtails'), SeoBlocks.bodyCharCountMoreThanEqual(8000))
        ),
      ],
      'Kulcsszó darabszám megfelelő'
    ),
    new SeoScoreRule(
      1,
      'imageAfterFirstParagraph',
      [SeoBlocks.isStartsWithOnlyOnePararaph(), SeoBlocks.imageSecondInFirstWysiwygComponent()],
      'Kép: Kép az első bekezdés után.',
      true
    ),
    new SeoScoreRule(
      1,
      'imageAfterFirstParagraphAltTagContainsFocusedKeyword',
      [SeoBlocks.fieldNotEmpty('focusedKeyword'), SeoBlocks.imageSecondInFirstWysiwygComponentAltContainsFocusedKeyword()],
      'A kiemelt kép (első bekezdés utáni kép) ALT attribútuma tartalmazza a fő kulcsszót',
      true
    ),
    /*new SeoScoreRule(
      1,
      'videoAtArticleEnd',
      [SeoBlocks.or(SeoBlocks.containsComponent('Media.Video.Video'), SeoBlocks.containsEmbeddedVideo()), SeoBlocks.videoAtBodyEnd()],
      'Videó: A cikk végén.',
      true
    ),*/
    new SeoScoreRule(
      1,
      'containsVideo',
      [SeoBlocks.or(SeoBlocks.containsComponent('Media.Video.Video'), SeoBlocks.containsEmbeddedVideo())],
      'Videó: A cikk tartalmaz videót',
      true
    ),
    new SeoScoreRule(
      1,
      'linkInternal',
      [SeoBlocks.containsInternalAnchor(this.domainService.currentDomain.info.previewUrl)],
      'Link: Tartalmaz belső linket',
      true
    ),
    new SeoScoreRule(
      1,
      'linkExternal',
      [SeoBlocks.containsExternalAnchor(this.domainService.currentDomain.info.previewUrl)],
      'Link: Tartalmaz külső linket',
      true
    ),
    new SeoScoreRule(
      1,
      'slugLengthValid',
      [SeoBlocks.fieldNotEmpty('slug'), SeoBlocks.moreThanEqual(50, SeoBlocks.slugCharCount()), SeoBlocks.lessThanEqual(55, SeoBlocks.slugCharCount())],
      'URL 50-55 karakter, megfelelően rövid'
    ),
    new SeoScoreRule(
      1,
      'slugStartsWithFocusedKeyword',
      [SeoBlocks.fieldNotEmpty('slug'), SeoBlocks.slugStartsWithFocusedKeyword(this.utils.generateSlug)],
      'URL első szava a fő kulcsszó'
    ),
    new SeoScoreRule(
      1,
      'portalTitleLengthValid',
      [SeoBlocks.moreThanEqual(30, SeoBlocks.portalTitleCharCount()), SeoBlocks.lessThanEqual(155, SeoBlocks.portalTitleCharCount())],
      'SEO cím megfelelően hosszú, max 155 karakter'
    ),
    new SeoScoreRule(1, 'seoDescriptionNotEmpty', [SeoBlocks.fieldNotEmpty('seoDescription')], 'SEO leírás kitöltve'),
  ];

  ratings: SeoScoreResultRating[] = <SeoScoreResultRating[]>[
    {
      minScore: 0,
      color: '#EB0F00',
      icon: 'close-circle',
      title: 'Gyenge SEO',
      hint: 'Ez nem elég, ezen még dolgozni kellene.',
      type: 'weak', // Gyenge
    },
    {
      minLevel: 0,
      levelScoreCorrelation: 'and',
      color: '#08a455',
      icon: 'check-circle',
      title: 'Közepes SEO',
      hint: 'Már majdnem jó, de még csiszolható.',
      type: 'medium', // Közepes
    },
    {
      minLevel: 1,
      levelScoreCorrelation: 'and',
      color: '#8d05e3',
      icon: 'like',
      title: 'Kiváló SEO!',
      hint: 'Gratulálunk, szép munka!',
      higherLevelRulesToPass: 1, //Needs one rule from level 2 (or higher) to pass (legalább 1 bónusz szükséges a Kiváló SEO minősítéshez)
      type: 'excellent', // Kíváló
    },
  ];

  /**
   * Individual rule levels for each config.
   * 0: Kötelező
   * 1. normal
   * 2. Bónusz
   */
  scoreConfigRuleLevels = {
    noWysiwyg: [0, 2, 0, 2, 1, 2, 0, 0, 0, 0, 0],
    level1: [0, 0, 0, 2, 0, 1, 0, 0, 1, 2, 0, 0, 0, 1, 2, 0, 1, 0, 0, 0, 0],
    level2: [0, 0, 0, 2, 0, 1, 0, 0, 1, 2, 0, 0, 0, 1, 2, 0, 1, 0, 0, 0, 0],
    level3: [0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 2, 0, 0, 0, 0, 0, 0, 2],
    level4: [0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 2, 0, 0, 0, 0, 0, 0, 2],
  };

  get scoreConfigs(): SeoScoreConfig[] {
    return [
      {
        //8000 feletti cikktörzs
        canUse: (input) => 8000 <= input.bodyCharCount,
        label: '>8000',
        minScores: { weak: 0, medium: 18, excellent: 21 },
        rules: [
          ...this.seoRules,
          new SeoScoreRule(
            1,
            'containsList',
            [SeoBlocks.or(SeoBlocks.containsOrderedList(), SeoBlocks.containsUnorderedList())],
            'Tartalmaz pontokba szedett vagy számozott listát'
          ),
        ].map((rule, index) => ({ level: this.scoreConfigRuleLevels.level4[index], rule: rule })),
      },
      {
        //3200-7999 közötti cikktörzs
        canUse: (input) => 3200 <= input.bodyCharCount && input.bodyCharCount <= 7999,
        label: '3200-7999',
        minScores: { weak: 0, medium: 18, excellent: 21 },
        rules: [
          ...this.seoRules,
          new SeoScoreRule(
            1,
            'containsList',
            [SeoBlocks.or(SeoBlocks.containsOrderedList(), SeoBlocks.containsUnorderedList())],
            'Tartalmaz pontokba szedett vagy számozott listát'
          ),
        ].map((rule, index) => ({ level: this.scoreConfigRuleLevels.level3[index], rule: rule })),
      },
      {
        //1400-3199 közötti cikktörzs
        canUse: (input) => 1400 <= input.bodyCharCount && input.bodyCharCount <= 3199,
        label: '1400-7999',
        minScores: { weak: 0, medium: 14, excellent: 19 },
        rules: this.seoRules.map((rule, index) => ({ level: this.scoreConfigRuleLevels.level2[index], rule: rule })),
      },
      {
        // seo module for entity without wysiwyg editor
        canUse: (input: ISeoScoreCalculationInput) => !input.hasWysiwygEditor,
        label: 'withoutWysiwyg',
        rules: this.seoRules
          .filter((rule) => !rule.onlyWithWysiwygEditor)
          .map((rule, index) => ({ level: this.scoreConfigRuleLevels.noWysiwyg[index], rule: rule })),
      },
      {
        canUse: () => true,
        label: 'default',
        minScores: { weak: 0, medium: 14, excellent: 19 },
        rules: this.seoRules.map((rule, index) => ({ level: this.scoreConfigRuleLevels.level1[index], rule: rule })),
      },
    ];
  }

  calculateSeoScore(data: ContentResponse, options?: { isFirstCalculation?: boolean; hasWysiwygEditor?: boolean }) {
    options = { isFirstCalculation: false, hasWysiwygEditor: true, ...options };
    try {
      const seoInput: ISeoScoreCalculationInput = mapContentResponse(
        this._seoType,
        data,
        { bodyCharCountField: options.isFirstCalculation ? 'bodyCharacterCount' : 'charCountBody', hasWysiwygEditor: options.hasWysiwygEditor },
        (title) => this.portalTitleService.generatePortalTitle(title)
      );
      this.lastMappedContent = seoInput;
      const config = this.scoreConfigs.find((config) => config.canUse(seoInput));
      return this.calculateScore(config, seoInput);
    } catch (e) {
      console.error('SEO Score: Mapping failed: ', e);
    }
  }

  calculateScore(config: SeoScoreConfig, seoInput: ISeoScoreCalculationInput): ISeoScoreResult {
    seoInput.bodyWysiwygDocumentFragments = this.parseWysiwygComponents(seoInput);
    seoInput.bodyKeywords = this.getBodyContainedKeywords(seoInput);

    const passedRules: SeoScoreRuleConfig[] = [];
    const failedRules: SeoScoreRuleConfig[] = [];

    let score: number = 0;

    config.rules &&
      config.rules.forEach((ruleConfig) => {
        try {
          const ruleResult: number = this.calculateRule(ruleConfig.rule, seoInput);
          score += ruleResult;

          if (ruleResult) {
            passedRules.push(ruleConfig);
          } else {
            failedRules.push(ruleConfig);
          }
        } catch (e) {
          console.error('SEO Score: Rule throwed an error: ', e);
          failedRules.push(ruleConfig);
        }
      });

    return <ISeoScoreResult>{
      finalScore: score,
      maxScore: config.rules.reduce((count, ruleConfig) => count + ruleConfig.rule.score, 0),
      rating: this.getRatingForResultScoreAndRules(score, config, passedRules, failedRules),
      passedRules: passedRules,
      failedRules: failedRules,
    };
  }

  private getRatingForResultScoreAndRules(
    score: number,
    config: SeoScoreConfig,
    passedRules: SeoScoreRuleConfig[],
    failedRules: SeoScoreRuleConfig[]
  ): SeoScoreResultRating | null {
    let result: SeoScoreResultRating | null = null;

    if (this._seoType === SeoTypeEnum.ARTICLE && config?.minScores) {
      const hasRequiredFailedRule = failedRules.findIndex(({ level }) => level === 0);
      if (hasRequiredFailedRule !== -1) {
        return this.ratings.find((rating) => rating.type === 'weak');
      }
      const { excellent, medium } = config.minScores;
      const type = score >= excellent ? 'excellent' : score >= medium ? 'medium' : 'weak';
      return this.ratings.find((rating) => rating.type === type);
    }

    this.ratings.forEach((rating) => {
      let scorePassed = true;
      let levelPassed = true;
      let higherLevelPassedRules = 0;
      if (rating.minScore && score < rating.minScore) {
        scorePassed = false;
      }
      if (!isNaN(rating.minLevel)) {
        levelPassed = config.rules.every((ruleConfig) => {
          const isRulePassed = passedRules.includes(ruleConfig);
          if (isRulePassed && ruleConfig.level > rating.minLevel) {
            higherLevelPassedRules++;
          }
          if (!isRulePassed) {
            return ruleConfig.level > rating.minLevel;
          }
          return true;
        });
      }
      if (rating.higherLevelRulesToPass && higherLevelPassedRules < rating.higherLevelRulesToPass) {
        levelPassed = false;
      }

      if (rating.levelScoreCorrelation === 'or' && (levelPassed || scorePassed)) {
        result = rating;
        return;
      }

      if (rating.levelScoreCorrelation === 'xor' && (levelPassed || scorePassed) && !(levelPassed && scorePassed)) {
        result = rating;
        return;
      }
      if (levelPassed && scorePassed) {
        result = rating;
        return;
      }
    });
    return result;
  }

  private getBodyContainedKeywords(input: ISeoScoreCalculationInput) {
    const keywords = [...(input.focusedKeyword ? [input.focusedKeyword] : []), ...input.keywords, ...input.longtails];
    return keywords.filter((tag) =>
      input.bodyWysiwygDocumentFragments.some((fragment) => {
        return this.removeAccents(fragment?.textContent).includes(this.removeAccents(tag));
      })
    );
  }

  private removeAccents(text: string): string {
    if (!text) {
      return '';
    }
    return text
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .toLowerCase();
  }

  /** Calculates all the blocks in the specified rule.
   * If all blocks pass, returns the score associated to the rule. */
  private calculateRule(rule: SeoScoreRule, seoInput: ISeoScoreCalculationInput): number {
    let pass = false;

    if (!rule.blocks || rule.blocks.length === 0) {
      return 0;
    }

    pass = rule.blocks.every((ruleBlock) => ruleBlock(seoInput));

    return pass ? rule.score : 0;
  }

  /** Creates a DocumentFragment from the Wysiwyg components
   * and stores them in an array with their index in the body content.
   * There will be empty indexes in places where no Wysiwyg component exists.
   */
  private parseWysiwygComponents(seoInput: ISeoScoreCalculationInput): DocumentFragment[] {
    const fragments = [];
    seoInput.body?.map((item) => {
      if (item.type === 'Basic.Wysiwyg.Wysiwyg') {
        var template = document.createElement('template');
        template.innerHTML = item.details[0].value;
        fragments.push(template.content);
      } else {
        fragments.push(undefined);
      }
    });
    return fragments;
  }

  getPortalTitle() {
    return this.lastMappedContent?.portalTitle || '';
  }

  getPreviewDescription() {
    return this.lastMappedContent?.previewDescription || '';
  }

  private initSeoRules() {
    this.seoRules = this.defaultSeoRules.map((seoRule) => {
      const keyPrefix = `seo.rules.${seoRule.key}`;
      const uniqueLabelTranslateKey = `${keyPrefix}.${this._seoType}`;
      const uniqueLabel = this.translateService.instant(uniqueLabelTranslateKey);
      if (uniqueLabel !== uniqueLabelTranslateKey) {
        seoRule.label = uniqueLabel;
      }
      return seoRule;
    });
  }
}
