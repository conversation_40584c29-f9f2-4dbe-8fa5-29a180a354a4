import { getCharacterCount } from 'src/app/shared/utils/text-utils';
import { SeoScoreCalculationBlock, SeoScoreCalculationSubBlock } from '../definitions/seo-calculation-block';
import { ISeoScoreCalculationInput } from '../definitions/seo-score-calculation-input';
import { AvailableComponentContent } from '../../../core/api.definitons';

export const TEXT_BLOCK_TAG_NAMES = ['P', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6', 'UL', 'OL', 'DIV'];

/*
  Keyword related blocks
*/
function getContainedKeywords(key: string, input: ISeoScoreCalculationInput): string[] {
  if (!Object.keys(input).includes(key)) {
    return [];
  }
  return input.keywords.filter((tag) => input[key]?.toLocaleLowerCase().includes(tag.toLocaleLowerCase()));
}

/** Checks if a given key from the ISeoScoreCalculationInput contains at least one keyword. */
export function totalKeywordCountAtLeast(min: number): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    const checkedLength = input.keywords.length + input.longtails.length + (input.focusedKeyword.length > 0 ? 1 : 0);
    return min <= checkedLength;
  };
}

/** Checks if a given key from the ISeoScoreCalculationInput contains at least one keyword. */
export function containsKeyword(key: string): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    const keywords = getContainedKeywords(key, input);
    return keywords && keywords.length > 0;
  };
}

/** Checks if a given key from the ISeoScoreCalculationInput contains at least a given ammount of keywords. */
export function containsKeywordAtLeast(key: string, number: number): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    const keywords = getContainedKeywords(key, input);
    return keywords && keywords.length >= number;
  };
}

export function isEveryKeywordUsed(): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    const keywords = [...(input.focusedKeyword ? [input.focusedKeyword] : []), ...input.keywords, ...input.longtails];
    const containedKeywords = [];

    keywords.forEach((keyword: string) => {
      const keywordLowerCase = keyword.toLowerCase();
      if (
        (input.bodyKeywords?.includes(keyword) || //BODY text, heading, image descriptions or any text in body contains it.
          input.title?.toLowerCase().includes(keywordLowerCase) || //LEAD CONTAINS
          input.lead?.toLowerCase().includes(keywordLowerCase)) && //TITLE CONTAINS
        !containedKeywords.includes(keyword)
      ) {
        containedKeywords.push(keyword);
      }
    });
    const totalKeywordsCount = input.keywords.length + input.longtails.length + (input.focusedKeyword.length > 0 ? 1 : 0);
    console.log('KEYS: ', {
      keywords: input.keywords,
      longtail: input.longtails,
      focused: input.focusedKeyword,
      total: totalKeywordsCount,
      contained: containedKeywords,
    });
    return totalKeywordsCount <= containedKeywords.length;
  };
}

/*
  Field related blocks.
*/
/** Checks if the lead contains more than the given number of characters. */
export function leadCharCountMoreThan(number: number): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    return input.leadCharCount > number;
  };
}

//** Checks if the title contains more than given number of characters. */
export function titleCharCountMoreThan(number: number): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    return input.titleCharCount > number;
  };
}

export function fieldNotEmpty(fieldName: string): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    return input[fieldName]?.length > 0;
  };
}

/*
  Focused keyword related blocks
 */
function filterArticles(inputString: string): string {
  const HUN_ARTICLES = ['egy ', 'ez ', 'az ', 'a ', 'e '];
  // Iterate through the array of words to filter
  for (const word of HUN_ARTICLES) {
    // Check if the input string starts with the current word (case-insensitive)
    if (inputString.toLowerCase().startsWith(word.toLowerCase())) {
      // Remove the matching word from the beginning of the input string
      inputString = inputString.slice(word.length).trim();
    }
  }
  return inputString;
}
//If title contains the focused keyword.
export function titleStartsWithFocusedKeyword(): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    return filterArticles(input.title?.toLowerCase() || '').startsWith(input.focusedKeyword.toLowerCase());
  };
}

export function seoTitleStartsWithFocusedKeyword(): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    return filterArticles(input.seoTitle?.toLowerCase() || '').startsWith(input.focusedKeyword.toLowerCase());
  };
}
export function titleContainsFocusedKeyword(): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    return input.title?.toLowerCase().includes(input.focusedKeyword.toLowerCase());
  };
}

export function seoTitleContainsFocusedKeyword(): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    return input.seoTitle?.toLowerCase().includes(input.focusedKeyword.toLowerCase());
  };
}

//If the given or subsequent sentences contains focused keyword.
export function focusedKeywordInLeadSentencesAfter(sentenceNum: number): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    const sentences = input.lead?.split?.(/[.;?!\r\n]+/);
    if (!sentences) {
      return;
    }
    for (let i = sentenceNum - 1; i < sentences.length; i++) {
      if (sentences[i].toLowerCase().includes(input.focusedKeyword.toLowerCase())) {
        return true;
      }
    }
    return false;
  };
}

//Article body first paragraph contains focused keyword.
export function firstParagraphContainsFocusedKeyword(): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    const firstFilledChild = Array.from(input.bodyWysiwygDocumentFragments[0]?.childNodes || [])?.filter(
      (node) => node.textContent.trim() !== '' && node.nodeName !== 'UL' && node.nodeName !== 'OL'
    )?.[0];
    return firstFilledChild && firstFilledChild.nodeName === 'P' && firstFilledChild.textContent.toLowerCase().includes(input.focusedKeyword.toLowerCase());
  };
}

//Is there any Image caption that contains a focused keyword?
export function isImageAltTagHasFocusedKeyword(): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    const fragments = input.bodyWysiwygDocumentFragments;
    return fragments.some((fragment) => {
      return fragment && Array.from(fragment.querySelectorAll('img')).some((image) => image.alt.toLowerCase().includes(input.focusedKeyword.toLowerCase()));
    });
  };
}

//Is there any Image caption that contains a focused keyword?
export function bodyDomElementContainsFocusedKeyword(selector: string): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    const fragments = input.bodyWysiwygDocumentFragments;
    return fragments.some((fragment) => {
      return (
        fragment &&
        Array.from(fragment.querySelectorAll(selector)).some((element) => {
          return element.textContent.toLowerCase().includes(input.focusedKeyword.toLowerCase());
        })
      );
    });
  };
}

//Is there is image after the first paragraph and its alt tag contains focused keyword.
export function imageSecondInFirstWysiwygComponentAltContainsFocusedKeyword(): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    const childNodes = Array.from(input.bodyWysiwygDocumentFragments[0]?.childNodes || [])?.filter(
      (node) => (node?.textContent.trim() !== '' || node?.nodeName === 'FIGURE') && node?.nodeName !== 'UL' && node?.nodeName !== 'OL'
    );
    if (childNodes?.length >= 2) {
      return (
        childNodes[1].nodeName.toUpperCase() === 'figure'.toUpperCase() &&
        childNodes[1].childNodes[0].nodeName.toUpperCase() === 'img'.toUpperCase() &&
        (childNodes[1].childNodes[0] as HTMLImageElement).alt.toLowerCase().includes(input.focusedKeyword.toLowerCase())
      );
    }
    return false;
  };
}

/*
  Body related blocks
*/
/** Checks if the body has more characters (but not equal) than the given number. */
export function bodyCharCountMoreThan(number: number): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    return input.bodyCharCount > number;
  };
}

/** Checks if the body has more characters or equal than the given number. */
export function bodyCharCountMoreThanEqual(number: number): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    return input.bodyCharCount >= number;
  };
}

/** Checks if the body has lass characters (but not equal) than the given number. */
export function bodyCharCountLessThan(number: number): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    return !input.hasWysiwygEditor || input.bodyCharCount < number;
  };
}

/** Checks if the body has less characters or equal than the given number. */
export function bodyCharCountLessThanEqual(number: number): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    return !input.hasWysiwygEditor || input.bodyCharCount <= number;
  };
}

/** Checks if the body contains at least one keyword. */
export function bodyContainsKeyword(): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    return input.bodyKeywords.length > 0;
  };
}

/** Checks if the body contains at least the given number of keywords. */
export function bodyContainsKeywordAtLeast(number: number = 1): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    return input.bodyKeywords.length > number;
  };
}

/*
  Custom component blocks. This is for added components to CKEditor, not Wysiwyg elements like images and links
*/
/** Checks if the body has at least the given number of components added from a given component type. */
export function hasAtLeastComponent(componentType: string, number: number = 1): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    return input.componentsDistinctCount.filter((item) => item[0] === componentType).length >= number;
  };
}

/** Checks if the body has a component from a given component type. */
export function containsComponent(componentType: string): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    return input.componentsDistinctCount.find((item) => item[0] === componentType) !== undefined;
  };
}

/** Checks if a component is at a specific positon (one-based array index) */
export function isComponentAt(componentType: string, position: number): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    return input.body.length >= position - 1 && input.body[position - 1]?.type === componentType;
  };
}

/*
  Wysiwyg blocks
*/

/** Checks if the body starts with a Wysiwyg paragraph (p tag) item. */
export function isStartsWithPararaph(): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    return (
      input.bodyWysiwygDocumentFragments.length > 0 &&
      input.bodyWysiwygDocumentFragments[0]?.childNodes?.length > 0 &&
      input.bodyWysiwygDocumentFragments[0]?.childNodes[0].nodeName === 'P'
    );
  };
}

/** Check is the body starts with Wysiwyg component and has only ONE paragraph item (p tag). */
export function isStartsWithOnlyOnePararaph(): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    return (
      input.bodyWysiwygDocumentFragments.length > 0 &&
      input.bodyWysiwygDocumentFragments[0]?.childNodes?.length > 0 &&
      TEXT_BLOCK_TAG_NAMES.includes(input.bodyWysiwygDocumentFragments[0]?.childNodes[0].nodeName)
    );
  };
}

function charCountForWysiwyg(input: ISeoScoreCalculationInput, index: number) {
  let charCount = 0;
  Array.from(input.bodyWysiwygDocumentFragments[index].children).forEach((element) => {
    charCount += getCharacterCount(element.textContent.trim());
  });
  return charCount;
}

function calculateCharsBeforeElement(element) {
  let charCount = 0;

  if (element === null || element === undefined) {
    return 0;
  }
  if (element && element.previousElementSibling) {
    let checkedElement = element.previousElementSibling;
    do {
      charCount += getCharacterCount(checkedElement.textContent.trim());
      checkedElement = checkedElement.previousElementSibling;
    } while (checkedElement);
  }
  if (element && element.parentElement) {
    charCount += calculateCharsBeforeElement(element.parentElement);
  }
  return charCount;
}

function _slugWordCount(slug: string) {
  return slug.split('-').length;
}

export function slugStartsWithFocusedKeyword(slugify: (string) => string): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    return input.slug.startsWith(slugify(input.focusedKeyword));
  };
}

export function slugWordCount(): SeoScoreCalculationSubBlock<number> {
  return (input: ISeoScoreCalculationInput): number => {
    return _slugWordCount(input.slug);
  };
}

export function slugCharCount(): SeoScoreCalculationSubBlock<number> {
  return (input: ISeoScoreCalculationInput): number => {
    return input.slug.length;
  };
}

export function portalTitleCharCount(): SeoScoreCalculationSubBlock<number> {
  return (input: ISeoScoreCalculationInput): number => {
    return input.portalTitle.length;
  };
}

export function moreThan(condition: number, seoScoreCalculationSubBlock: SeoScoreCalculationSubBlock<number>): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    return seoScoreCalculationSubBlock(input) > condition;
  };
}

export function lessThan(condition: number, seoScoreCalculationSubBlock: SeoScoreCalculationSubBlock<number>): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    return seoScoreCalculationSubBlock(input) < condition;
  };
}

export function lessThanEqual(condition: number, seoScoreCalculationSubBlock: SeoScoreCalculationSubBlock<number>): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    return seoScoreCalculationSubBlock(input) <= condition;
  };
}

export function moreThanEqual(condition: number, seoScoreCalculationSubBlock: SeoScoreCalculationSubBlock<number>): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    return seoScoreCalculationSubBlock(input) >= condition;
  };
}

export function hasEmbeddedVideoByElements(elements: Element[]): boolean {
  return elements
    .filter(Boolean)
    .some(
      (elem) =>
        (elem.tagName.toLowerCase() === 'iframe' && isUrlVideo(elem.getAttribute('src'))) ||
        (elem.tagName.toLowerCase() === 'oembed' && isUrlVideo(elem.getAttribute('url'))) ||
        (elem.tagName.toLowerCase() === 'blockquote' && isUrlVideo(elem.getAttribute('data-instgrm-permalink'))) ||
        (elem.tagName.toLowerCase() === 'blockquote' && elem.hasAttribute('data-video-id')) ||
        (elem.tagName.toLowerCase() === 'iframe' && elem?.id?.startsWith('videoplayer')) ||
        elem.classList.contains('fb-video')
    );
}

export function isUrlVideo(src: string) {
  return (
    /^(http(?:s?):)?\/\/(?:www\.)?((youtube.com)|(videa.hu)|(youtu.be)|(tv2play.hu)|(twitter.com)|(instagram.com\/reel)|(video.hirtv.hu))\/.*/.test(src) ||
    /^https?:\/\/(?:www\.)?facebook\.com(?:\/(plugins\/video|reel))?.*/.test(src) ||
    /^https?:\/\/(?:www\.)?dailymail.co.uk\/embed\/video?.*/.test(src)
  );
}

function getEmbeddedVideoElement(fragment: DocumentFragment) {
  const query = Array.from(fragment.querySelectorAll('figure.media > oembed, iframe, blockquote, p, h2, h3, h4, h5, h6, ul, ol, div') || []);
  const elements = query.filter((elem: Element) => {
    if (TEXT_BLOCK_TAG_NAMES.includes(elem.tagName) && !elem.classList.contains('fb-video')) {
      return (elem as any).innerText.trim() !== '';
    }
    return true;
  });
  if (!elements || elements.length === 0) {
    return undefined;
  }
  return hasEmbeddedVideoByElements(elements);
}

export function containsEmbeddedVideo(): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    for (let i = 0; i < input.bodyWysiwygDocumentFragments.length; i++) {
      if (input.bodyWysiwygDocumentFragments[i]) {
        if (getEmbeddedVideoElement(input.bodyWysiwygDocumentFragments[i])) {
          return true;
        }
      }
    }
    return false;
  };
}

export function videoAtBodyEnd(): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    const lastIndex = getLastBodyContentBlockIndex(input.body);
    if (lastIndex < 0) {
      return false;
    }
    if (input.body[lastIndex].type === 'Media.Video.Video') {
      return true;
    }
    if (input.body[lastIndex].type === 'Basic.Wysiwyg.Wysiwyg') {
      return Boolean(getEmbeddedVideoElement(input.bodyWysiwygDocumentFragments[lastIndex]));
    }
    return false;
  };
}

function getFirstBodyContentBlockIndex(body: AvailableComponentContent[]) {
  let firstIndex = 0;

  while (firstIndex <= body.length && !isBodyContentBlockValidable(body[firstIndex])) {
    ++firstIndex;
  }
  return firstIndex;
}

/**
 * Tries to find the last body component that could be relevant to content check.
 * This means that we skip ads and empty wysiwyg blocks.
 */
function getLastBodyContentBlockIndex(body: AvailableComponentContent[]) {
  let lastIndex = body.length - 1;

  while (lastIndex > 0 && !isBodyContentBlockValidable(body[lastIndex])) {
    --lastIndex;
  }
  return lastIndex;
}

function isBodyContentBlockValidable(component: AvailableComponentContent) {
  if (component.type === 'Eadvert.Eadvert') {
    return false;
  }
  if (component.type === 'Basic.Wysiwyg.Wysiwyg') {
    const value = component.details[0].value;
    if (value.length === 0 || value === '<p></p>' || value === '<p>&nbsp;</p>') {
      return false;
    }
  }
  return true;
}

export function containsUnorderedList(): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    return input.bodyWysiwygDocumentFragments.some((fragment) => fragment && Array.from(fragment.querySelectorAll('ul')).length > 0);
  };
}

export function containsOrderedList(): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    return input.bodyWysiwygDocumentFragments.some((fragment) => fragment && Array.from(fragment.querySelectorAll('ol')).length > 0);
  };
}

// Check if the body contains internal anchors (relative links or absolute links that start with the site base url.
// For example: https://vg.hu
export function containsInternalAnchor(siteBaseUrl: string): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    return input.bodyWysiwygDocumentFragments.some(
      (fragment) =>
        fragment &&
        Array.from(fragment.querySelectorAll('a')).some((anchor) => Boolean(anchor.href.startsWith(siteBaseUrl)) || anchor.host === window.location.host)
    );
  };
}

export function containsExternalAnchor(siteBaseUr: string): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    return input.bodyWysiwygDocumentFragments.some(
      (fragment) => fragment && Array.from(fragment.querySelectorAll('a')).some((anchor) => !Boolean(anchor.href.startsWith(siteBaseUr)))
    );
  };
}

/** Checks if there is an image after a paragraph in the Wysiwyg component. */
export function itemSecondInFirstWysiwygComponent(nodeName: string): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    if (input.bodyWysiwygDocumentFragments[0]?.childNodes?.length >= 2) {
      const nodeList = input.bodyWysiwygDocumentFragments[0]?.childNodes;
      return nodeList[1].nodeName.toUpperCase() === nodeName.toUpperCase();
    }
    return false;
  };
}

/** Checks if there is an image (figure > img) right after the first element in the Wysiwyg component. */
export function imageSecondInFirstWysiwygComponent(): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    const childNodes = Array.from(input.bodyWysiwygDocumentFragments[0]?.childNodes).filter(
      (node) => (node?.textContent.trim() !== '' || node?.nodeName === 'FIGURE') && node?.nodeName !== 'UL' && node?.nodeName !== 'OL'
    );
    if (childNodes.length >= 2) {
      return childNodes[1].nodeName.toUpperCase() === 'figure'.toUpperCase() && childNodes[1].childNodes[0].nodeName.toUpperCase() === 'img'.toUpperCase();
    }
    return false;
  };
}

function getImageFromDocumentFragments(fragments: DocumentFragment[]): Node | undefined {
  for (let i = 0; i < fragments.length; i++) {
    for (let j = 0; j < fragments[i].childNodes.length; j++) {
      const node = fragments[i].childNodes[j];

      if (node.nodeName === 'FIGURE' && node.hasChildNodes() && node.childNodes[0].nodeName === 'IMG') {
        return node.childNodes[0];
      }
      if (node.nodeName === 'IMG') {
        return node;
      }
    }
  }
  return undefined;
}

/** Runs a QuerySelector on all the Wysiwyg component contents and returns if the given query is succesfull. */
export function isQuerySelectorInWysiwyg(query: string): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    const fragments = input.bodyWysiwygDocumentFragments;
    return fragments.some((fragment) => fragment && fragment.querySelector(query) !== null);
  };
}

/** Checks if the Wysiwyg components contains at least a given number from the parameter. */
export function isQuerySelectorInWysiwygMoreThan(htmlTag: string, min: number): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    let items = [];
    const fragments = input.bodyWysiwygDocumentFragments;
    for (let i = 0; i < fragments.length; i++) {
      if (fragments[i]) {
        Array.from(fragments[i].querySelectorAll(htmlTag)).map((element) => items.push(element));
      }
      if (items.length > min) {
        return true;
      }
    }
    return false;
  };
}

/** Checks if the Wysiwyg component contains at least one image with an alt attribute. */
export function isImageWithAlt(): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    const img = getImageFromDocumentFragments(input.bodyWysiwygDocumentFragments);
    return img && (img as Element).getAttribute('alt')?.length > 0;
  };
}

/** Checks if the ALL the images in the Wysiwyg component has alt attribute. */
export function isAllImageWithAlt(): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    const fragments = input.bodyWysiwygDocumentFragments;
    return fragments.every((fragment) => Array.from(fragment.querySelectorAll('img')).every((element) => element.getAttribute('alt')?.length > 0));
  };
}

/*
  Operators
*/
/** Negate the given block's result. */
export function not(block: SeoScoreCalculationBlock): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    return !block(input);
  };
}

/** And calculation performed on multiple blocks. All blocks has to resolve to true */
export function every(...blocks: SeoScoreCalculationBlock[]): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    let failed = false;
    for (let i = 0; i < blocks.length; i++) {
      if (failed) {
        break;
      }
      !blocks[i](input) ? (failed = true) : null;
    }
    return !failed;
  };
}

/** Or calculation performed on multiple blocks. If at least one calculates to true, the result will be also true. */
export function or(...blocks: SeoScoreCalculationBlock[]): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    let result = false;
    for (let i = 0; i < blocks.length; i++) {
      if (result) {
        break;
      }
      blocks[i](input) ? (result = true) : null;
    }
    return result;
  };
}

/** Performs a XOR operation on all the arguments. Only one can be true. In all other cases the result will be false. */
export function xor(...blocks: SeoScoreCalculationBlock[]): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    let result = false;
    for (let i = 0; i < blocks.length; i++) {
      if (blocks[i](input)) {
        if (result) {
          return false;
        } else {
          result = true;
        }
      }
    }
    return result;
  };
}

/** Short circuit */
export function returnFalse(): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    return false;
  };
}

export function returnTrue(): SeoScoreCalculationBlock {
  return (input: ISeoScoreCalculationInput): boolean => {
    return true;
  };
}
