import { Component, inject, OnInit, signal, ViewChild } from '@angular/core';
import { FormGeneratorComponent } from '@shared/modules/form-generator/form-generator.component';
import { ContentData } from '@shared/modules/form-generator/form-generator.definitions';
import { ActivatedRoute, Router } from '@angular/router';
import { FormGenerator2Module } from '@shared/modules/form-generator2/form-generator2.module';
import { NzButtonComponent } from 'ng-zorro-antd/button';
import { ApiService } from '@core/services/api.service';
import { SharedService } from '@shared/services/shared.service';
import { Location, NgIf } from '@angular/common';
import { PageEditorService } from '../../../../content-page-editor/services/page-editor.service';
import { BaseContentVariant } from '@core/api.definitons';
import { CalendarDayFormControlsMapPipe } from '../../../../content-page-editor/pipes/calendar-day-form-controls.map.pipe';

@Component({
  selector: 'app-calendar-day-edit',
  imports: [FormGenerator2Module, NzButtonComponent, NgIf, CalendarDayFormControlsMapPipe],
  templateUrl: './calendar-day-edit.component.html',
  styleUrl: './calendar-day-edit.component.scss',
})
export class CalendarDayEditComponent implements OnInit {
  @ViewChild('formTemplate') fromTemplate: FormGeneratorComponent;

  formInfo = signal<ContentData>(null);
  loading = signal<boolean>(false);
  bodyContentData = signal<ContentData>(null);
  descriptionContentData = signal<BaseContentVariant>(null);

  protected readonly route = inject(ActivatedRoute);
  protected readonly router = inject(Router);
  protected readonly apiService = inject(ApiService);
  protected readonly sharedService = inject(SharedService);
  protected readonly pageEditorService = inject(PageEditorService);
  private readonly location = inject(Location);

  ngOnInit(): void {
    this.formInfo.set(this.route.snapshot.data.formInfo);
    const initialFormInfo = this.formInfo();
    this.bodyContentData.set(this.pageEditorService.getContentDataFragment(this.pageEditorService.bodyFilter, initialFormInfo));
  }

  onSave(): void {
    this.loading.set(true);
    const id = this.formInfo()?.meta?.id;
    const formWithDescription = this.addDescriptionValueToForm();
    this.apiService.secretDaysCalendarDay.update(id, formWithDescription).subscribe(
      (data) => {
        this.formInfo.set(data);
        this.loading.set(false);
        this.sharedService.showNotification('success', 'Nap módosítása sikeres.');
      },
      (error) => {
        this.sharedService.showNotification('error', 'Nap módosítása sikertelen.');
        console.error(error);
        this.loading.set(false);
      }
    );
  }

  onFormControlChanged(formInfo: ContentData): void {
    this.formInfo.set(formInfo);
  }

  onBodyValueChange(contentData: any): void {
    const descriptionContent = contentData?.updatedContentData.data?.[0];
    this.descriptionContentData.set(descriptionContent);
  }

  addDescriptionValueToForm(): ContentData {
    const formData = this.formInfo();
    const target = formData.data.find((item) => item.key === 'description');
    if (target) {
      target.value = this.descriptionContentData()?.value ?? this.bodyContentData().data[0]?.value ?? [];
    }
    return formData;
  }

  navigateBack(): void {
    this.location.back();
  }
}
