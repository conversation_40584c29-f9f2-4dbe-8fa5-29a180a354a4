<div class="header">
  <h3>Kalendárium szerkesztése</h3>
  <div class="header-buttons">
    <button nz-button nzType="default" [routerLink]="['/admin', 'calendars']">Vissza</button>
    <button nz-button nzType="primary" (click)="onSave()" [nzLoading]="loading()"><PERSON><PERSON><PERSON></button>
  </div>
</div>
<app-form-generator-basic
  #formTemplate
  (autosave)="onFormControlChanged($event)"
  [formControls]="formInfo() | fromContentDataToFormControls"
  [originalContentData]="formInfo()"
></app-form-generator-basic>
<h3>Napok</h3>
<nz-table #days [nzData]="formInfo()?.meta?.days" [nzPageSize]="100">
  <thead>
  <tr>
    <th>{{ 'order' | translate }}</th>
    <th>{{ 'CMS.name' | translate }}</th>
    <th>{{ 'CMS.date' | translate }}</th>
    <th>M<PERSON><PERSON>etek</th>
  </tr>
  </thead>
  <tbody>
  <tr *ngFor="let data of days.data; let i = index">
    <td>{{ data.daysOrder ?? 'Nincs megadva' }}</td>
    <td>{{ data.name ?? 'Nincs megadva'}}</td>
    <td>{{ (data?.dateOfDay?.date | dateFormat) ?? 'Nincs megadva' }}</td>
    <td class="actions">
      <button (click)="onDayEdit(data.id)" nz-button nzType="primary">Szerkesztés</button>
      <!--<button nz-button (click)="onDayDelete(data.id)">
        <i nz-icon nzType="delete" nzTheme="fill"></i>
      </button>-->
    </td>
  </tr>
  </tbody>
</nz-table>
