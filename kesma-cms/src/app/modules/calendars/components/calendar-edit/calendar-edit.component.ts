import { Component, inject, OnInit, signal, ViewChild } from '@angular/core';
import { BasicItemEditorModule } from '../../../../item-editor-page/component/basic-item-editor/basic-item-editor.module';
import { DataTableModule } from '@shared/modules/data-table/data-table.module';
import { FormGeneratorComponent } from '@shared/modules/form-generator/form-generator.component';
import { ContentData } from '@shared/modules/form-generator/form-generator.definitions';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { SharedService } from '@shared/services/shared.service';
import { FormGenerator2Module } from '@shared/modules/form-generator2/form-generator2.module';
import { NgForOf } from '@angular/common';
import { NzTableComponent } from 'ng-zorro-antd/table';
import { TranslatePipe } from '@ngx-translate/core';
import { NzButtonComponent } from 'ng-zorro-antd/button';
import { ApiService } from '@core/services/api.service';
import { SharedModule } from '@shared/shared.module';
import { Observable } from 'rxjs';
import { switchMap } from 'rxjs/operators';
import { NzModalService } from 'ng-zorro-antd/modal';

@Component({
  selector: 'app-calendar-edit',
  imports: [
    BasicItemEditorModule,
    DataTableModule,
    FormGenerator2Module,
    FormGenerator2Module,
    NzTableComponent,
    NgForOf,
    TranslatePipe,
    NzButtonComponent,
    SharedModule,
    RouterLink,
  ],
  templateUrl: './calendar-edit.component.html',
  styleUrl: './calendar-edit.component.scss',
})
export class CalendarEditComponent implements OnInit {
  @ViewChild('formTemplate') fromTemplate: FormGeneratorComponent;

  formInfo = signal<ContentData>(null);
  loading = signal<boolean>(false);
  numberOfDaysInitialValue = signal<number>(0);

  protected readonly route = inject(ActivatedRoute);
  protected readonly router = inject(Router);
  protected readonly sharedService = inject(SharedService);
  protected readonly apiService = inject(ApiService);
  protected readonly modal = inject(NzModalService);

  ngOnInit(): void {
    this.formInfo.set(this.route.snapshot.data.formInfo);
    this.setNumberOfDaysInitialValue();
  }

  onDayEdit(id: string): void {
    this.router.navigate(['/', 'admin', 'calendars', 'day-edit', id]);
  }

  /*
  Jelenleg nem tud ilyet az api.

  onDayDelete(id: string): void {
    this.apiService.secretDaysCalendarDay.delete(id);
  }*/

  onSave(): void {
    const numberOfDaysValue = this.formInfo()?.data?.find((item) => item.key === 'numberOfDays').value;
    if (numberOfDaysValue < this.numberOfDaysInitialValue()) {
      this.modal.warning({
        nzTitle: 'Napok száma csökkent',
        nzContent: 'A napok száma az előző mentett értéknél kevesebb lett, így lehetséges, hogy bizonyos napok elvesznek!',
        nzClosable: true,
        nzOnOk: () => {
          this.saveDataAndUpdateDays();
        },
      });
    } else {
      this.saveDataAndUpdateDays();
    }
  }

  onFormControlChanged(formInfo: ContentData): void {
    this.formInfo.set(formInfo);
  }

  saveData(id: string): Observable<any> {
    return this.apiService.secretDaysCalendar.update(id, this.formInfo());
  }

  fetchUpdatedDaysData(id: string): Observable<any> {
    return this.apiService.secretDaysCalendar.get(id);
  }

  setNumberOfDaysInitialValue(): void {
    const numberOfDays = this.formInfo()?.data?.find((item) => item.key === 'numberOfDays');
    if (numberOfDays) {
      this.numberOfDaysInitialValue.set(numberOfDays.value);
    }
  }

  private saveDataAndUpdateDays(): void {
    this.loading.set(true);
    const id = this.formInfo()?.meta?.id;
    this.saveData(id)
      .pipe(switchMap(() => this.fetchUpdatedDaysData(id)))
      .subscribe(
        (data) => {
          this.formInfo.set(data);
          this.setNumberOfDaysInitialValue();
          this.loading.set(false);
          this.sharedService.showNotification('success', 'Kalendárium módosítása sikeres.');
        },
        (error) => {
          this.sharedService.showNotification('error', 'Kalendárium módosítása sikertelen.');
          console.error(error);
          this.loading.set(false);
        }
      );
  }
}
