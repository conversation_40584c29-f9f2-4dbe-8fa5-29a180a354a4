import { AfterViewInit, Component, ElementRef, Input, OnInit, Renderer2, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { NzMessageService } from 'ng-zorro-antd/message';
import { DomainKey } from '../../../core/modules/admin/admin.definitions';
import { DomainService } from 'src/app/core/modules/admin/services/domain.service';
import { ApiService } from 'src/app/core/services/api.service';
import { ActivatedRoute, Router } from '@angular/router';
import { StorageService } from '@external/utils';
import { ArticleMediaType } from 'src/app/core/definitions/article.definitions';
import { ArticleEditorService } from '../../services/article-editor.service';
import { addHours, addMinutes, differenceInCalendarDays } from 'date-fns';
import { DisabledTimeFn, NzDatePickerComponent } from 'ng-zorro-antd/date-picker';
import { Observable } from 'rxjs';
import { convertDateToBackendUtcDate } from '@trendency/kesma-ui';

const SESSION_STORAGE_KEY = 'domain';
const EMOJI_PICKER_COLOUR = '#1890ff';

@Component({
  selector: 'app-facebook-share',
  templateUrl: './facebook-share.component.html',
  styleUrls: ['./facebook-share.component.scss'],
  standalone: false,
})
export class FacebookShareComponent implements OnInit, AfterViewInit {
  public selectedArticleMediaType: ArticleMediaType;
  allChecked = false;
  facePages = [
    { label: 'bama.hu', value: 'https://www.bama.hu', checked: false },
    { label: 'baon.hu', value: 'https://www.baon.hu', checked: false },
    { label: 'beol.hu', value: 'https://www.beol.hu', checked: false },
    { label: 'boon.hu', value: 'https://www.boon.hu', checked: false },
    { label: 'delmagyar.hu', value: 'https://www.delmagyar.hu', checked: false },
    { label: 'duol.hu', value: 'https://www.duol.hu', checked: false },
    { label: 'feol.hu', value: 'https://www.feol.hu', checked: false },
    { label: 'haon.hu', value: 'https://www.haon.hu', checked: false },
    { label: 'heol.hu', value: 'https://www.heol.hu', checked: false },
    { label: 'kemma.hu', value: 'https://www.kemma.hu', checked: false },
    { label: 'kisalfold.hu', value: 'https://www.kisalfold.hu', checked: false },
    { label: 'nool.hu', value: 'https://www.nool.hu', checked: false },
    { label: 'sonline.hu', value: 'https://www.sonline.hu', checked: false },
    { label: 'szoljon.hu', value: 'https://www.szoljon.hu', checked: false },
    { label: 'szon.hu', value: 'https://www.szon.hu', checked: false },
    { label: 'teol.hu', value: 'https://www.teol.hu', checked: false },
    { label: 'vaol.hu', value: 'https://www.vaol.hu', checked: false },
    { label: 'veol.hu', value: 'https://www.veol.hu', checked: false },
    { label: 'zaol.hu', value: 'https://www.zaol.hu', checked: false },
  ];

  public id: string;
  public contentData;
  public isEmojiPickerVisible = false;
  public emojiPickerTranslation;
  public emojiPickerColour = EMOJI_PICKER_COLOUR;
  public isMegyeilap;
  public currentDate: Date = null;

  validateForm!: UntypedFormGroup;

  @Input() articleId: string;
  @Input() domain: DomainKey;
  @Input() isKpi: boolean;
  @Input() isOnlineArticle: boolean;
  @Input() isRecipe: boolean;
  @Input() publishDate$: Observable<Date>;

  private publishDate: Date;

  @ViewChild('emojiMart') emojiMart: ElementRef<HTMLDivElement>;
  @ViewChild('emojiPickerToggle') emojiPickerToggle: ElementRef<HTMLButtonElement>;
  @ViewChild('datePickerComponent') datePickerComponent: NzDatePickerComponent;

  public disabledDate = (current: Date): boolean => {
    return differenceInCalendarDays(this.publishDate, current) > 0;
  };

  public disabledDateTime: DisabledTimeFn = () => ({
    nzDisabledHours: () => this.getDisabledDateTime('hours'),
    nzDisabledMinutes: () => this.getDisabledDateTime('minutes'),
    nzDisabledSeconds: () => this.getDisabledDateTime('seconds'),
  });

  constructor(
    private msg: NzMessageService,
    private fb: UntypedFormBuilder,
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private storageService: StorageService,
    private domainService: DomainService,
    private readonly articleEditorService: ArticleEditorService,
    readonly apiService: ApiService,
    private renderer2: Renderer2
  ) {}

  ngOnInit(): void {
    this.isKpi = this.getDomain() === 'kpi';
    this.isMegyeilap = this.domainService.isMegyeiLap(this.getDomain());
    if (this.isMegyeilap) {
      this.initEmojiPickerTranslation();
      this.initEmojiMartClickListener();
    }
    this.isOnlineArticle = true;
    this.route.params.subscribe((params) => {
      this.id = params.id !== 'new' ? params.id : null;
      const routeSnapshot = this.route.snapshot;
      const responseData = routeSnapshot.data.data;

      if (!responseData) {
        return; // Recipes don't have this data, but they can't be print anyway, so we skip
      }

      const path = routeSnapshot.root.firstChild.firstChild.routeConfig.path;
      this.contentData = responseData.contentData;
      for (const element of this.contentData.data) {
        if (element.key === 'isPrint') {
          if (element.value === true) {
            this.isOnlineArticle = false;
          }
        }
      }
    });
    this.validateForm = this.fb.group({
      facebookDesc: [null],
      datePickerTime: [null],
    });
  }

  ngAfterViewInit() {
    // A datepicker legkisebb értéke a kiválasztott publikálási dátum + 3 perc. (KESMA-3922)
    this.publishDate$.subscribe((date) => {
      this.publishDate = addHours(addMinutes(new Date(date), 3), 2);
      if (!this.datePickerComponent?.inputValue) {
        //To not throw error when the input is hidden
        return;
      }
      if (this.disabledDate(new Date(this.datePickerComponent.inputValue))) {
        this.currentDate = null;
      }
    });
  }

  submitForm(): void {
    for (const i in this.validateForm.controls) {
      if (this.validateForm.controls.hasOwnProperty(i)) {
        this.validateForm.controls[i].updateValueAndValidity();
      }
    }
    const requestData = this.isRecipe
      ? {
          fbPostId: this.id,
          fbDateTime: convertDateToBackendUtcDate(this.validateForm.value.datePickerTime)?.replace('T', ' '),
          fbText: this.validateForm.value.facebookDesc,
        }
      : {
          ...this.validateForm.value,
          domain: this.getDomain(),
          articleId: this.id,
          facePages: this.facePages,
        };

    this.apiService.sendFacebookPostRequest(requestData, this.isRecipe).subscribe((res: any) => {
      this.msg.info(res?.data?.message || 'Facebbok poszt elküldve!'); // With recipes, res is empty
    });
  }

  updateAllChecked(): void {
    this.facePages = this.facePages.map((item) => ({
      ...item,
      checked: this.allChecked,
    }));
  }

  getDomain(): any {
    return this.domainService.currentDomain.key;
  }

  addEmoji(event) {
    const currentValue = this.validateForm.get('facebookDesc').value;
    const newValue = currentValue ? `${currentValue}${event.emoji.native}` : `${event.emoji.native}`;
    this.validateForm.get('facebookDesc').patchValue(newValue);
  }

  private initEmojiMartClickListener(): void {
    this.renderer2.listen('window', 'click', (event) => {
      const isClickEmojiPickerButton = event.target === this.emojiPickerToggle?.nativeElement;
      if (isClickEmojiPickerButton) {
        this.isEmojiPickerVisible = !this.isEmojiPickerVisible;
      } else {
        if (this.isEmojiPickerVisible) {
          const isEmojiMart = event.path.find((element) => element === this.emojiMart?.nativeElement);
          if (!isEmojiMart) {
            this.isEmojiPickerVisible = false;
          }
        }
      }
    });
  }

  onChange(): void {
    this.allChecked = !this.facePages?.some((value) => !value.checked);
  }

  private initEmojiPickerTranslation(): void {
    this.emojiPickerTranslation = {
      search: 'Keresés',
      emojilist: 'Emoji lista',
      notfound: 'Nincs találat',
      clear: 'Törlés',
      categories: {
        search: 'Keresés eredménye',
        recent: 'Gyakran használt',
        people: 'Hangulatok és Emberek',
        nature: 'Állatok és Természet',
        foods: 'Étel és Ital',
        activity: 'Tevékenységek',
        places: 'Utazás és Helyek',
        objects: 'Tárgyak',
        symbols: 'Szimbólumok',
        flags: 'Zászlók',
        custom: 'Egyedi',
      },
      skintones: {
        1: 'Alapértelmezett bőrtónus',
        2: 'Világos bőrtónus',
        3: 'Közepesen világos bőrtónus',
        4: 'Közepes bőrtónus',
        5: 'Közepesen sötét bőrtónus',
        6: 'Sötét bőrtónus',
      },
    };
  }

  private hasDifferenceInDays(): boolean {
    if (!this.datePickerComponent.inputValue) {
      return;
    }
    return differenceInCalendarDays(new Date(this.datePickerComponent.inputValue), this.publishDate) === 0;
  }

  private getDisabledDateTime(type: string) {
    if (!this.hasDifferenceInDays()) {
      return [];
    }

    const date = new Date(this.datePickerComponent?.inputValue);

    const hours = this.publishDate?.getHours() - 1;
    const minutes = date?.getHours() > hours ? 0 : this.publishDate?.getMinutes();
    const seconds = date.getMinutes() > minutes ? 0 : this.publishDate?.getSeconds();

    switch (type) {
      case 'hours':
        return [...Array(hours)?.keys()];
      case 'minutes':
        return [...Array(minutes)?.keys()];
      case 'seconds':
        return [...Array(seconds)?.keys()];
    }
  }
}
