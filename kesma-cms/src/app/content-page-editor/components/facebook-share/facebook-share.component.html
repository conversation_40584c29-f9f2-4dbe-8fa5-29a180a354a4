<div *ngIf="isOnlineArticle" class="isOnlineArticleContainer">
  <hr />

  <h3 class="fbTitle">Megosztás Facebookon</h3>

  <label
    *ngIf="isKpi"
    class="allcheck"
    nz-checkbox
    [(ngModel)]="allChecked"
    (ngModelChange)="updateAllChecked()"
  >
    Minden oldal kiválasztása
  </label>

  <br />

  <div *ngIf="isKpi" class="checkboxWrapper">
    @for (facePage of facePages; track facePage.label) {
      <label nz-checkbox [(ngModel)]="facePage.checked" (ngModelChange)="onChange()">
        {{ facePage.label }}
      </label>
    }
  </div>

  <form class="mt-4" nz-form [nzLayout]="'vertical'" [formGroup]="validateForm" (ngSubmit)="submitForm()">
    <div class="sharedWrapper">
      <div class="row">
        <nz-form-item class="col-md-6">
          <nz-form-control nzErrorTip="Kérjünk adjon meg leírást a megosztáshoz">
            <nz-input-group>
              <nz-form-label [nzSm]="8" [nzXs]="24">Facebook poszt szövege</nz-form-label>
              <div class="facebook-description ant-input">
                <input formControlName="facebookDesc" nz-input placeholder="Leírás" />
                <ng-container *ngIf="isMegyeilap">
                  <button #emojiPickerToggle [ngClass]="{ active: isEmojiPickerVisible }" type="button"
                          class="emoji-picker-toggle">😀
                  </button>
                  <div #emojiMart [ngClass]="{ visible: isEmojiPickerVisible }" class="emoji-mart">
                    <emoji-mart
                      (emojiSelect)="addEmoji($event)"
                      [showPreview]="false"
                      [enableSearch]="false"
                      [i18n]="emojiPickerTranslation"
                      [color]="emojiPickerColour"
                      [set]="'facebook'"
                    ></emoji-mart>
                  </div>
                </ng-container>
              </div>
            </nz-input-group>
          </nz-form-control>
        </nz-form-item>
        <nz-form-item class="col-md-4">
          <nz-form-control [nzSm]="16" [nzXs]="24">
            <nz-input-group>
              <nz-form-label [nzSm]="8" [nzXs]="24">Élesítés ideje</nz-form-label>
              <nz-date-picker
                #datePickerComponent
                [(ngModel)]="currentDate"
                [nzDisabledTime]="disabledDateTime"
                [nzDisabledDate]="disabledDate"
                nzShowTime
                formControlName="datePickerTime"
              >
              </nz-date-picker>
            </nz-input-group>
          </nz-form-control>
        </nz-form-item>
      </div>
      <nz-form-item>
        <nz-form-control>
          <button nz-button nzType="primary" [disabled]="!validateForm.valid">Megosztás</button>
        </nz-form-control>
      </nz-form-item>
    </div>
  </form>

  <hr />
</div>
