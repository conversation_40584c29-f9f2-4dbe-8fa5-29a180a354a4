@use 'src/scss/shared.scss' as *;

.fbTitle {
  margin-bottom: 20px;
}

.sharedWrapper {
  margin-top: 15px;
}

.allcheck {
  margin-bottom: 20px;
}

.facebook-description {
  display: flex;
  padding: 0;

  &:focus-within {
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  .emoji-mart {
    visibility: hidden;
    position: absolute;
    right: -355px;
    top: -180px;
    z-index: 1;

    &.visible {
      // Workaround for emoji loading on slow connection
      visibility: visible;
    }
  }

  input {
    border: none;
    box-shadow: none;
  }

  .emoji-picker-toggle {
    filter: grayscale(100%);
    padding: 2px;
    width: 29px;
    height: 29px;
    box-sizing: border-box;
    border-radius: 50%;
    align-self: center;
    cursor: pointer;

    &:hover {
      filter: none;
    }

    &.active {
      color: black;
      background-color: $primary-color;
      filter: none;
    }
  }
}

nz-form-label {
  display: block;
}

.checkboxWrapper {
  display: grid;

  .ant-checkbox-wrapper + .ant-checkbox-wrapper {
    margin: 0;
  }
}
