import { inject, Pipe, PipeTransform } from '@angular/core';
import { DatetimeFormControl, GeneratedFormControl } from '@shared/modules/form-generator2/definitions/form-generator-adapters.definitions';
import { differenceInMilliseconds } from 'date-fns';
import { SharedService } from '@shared/services/shared.service';
import { FormControlListener } from 'src/app/shared/modules/form-generator2/definitions/form-control-interaction.definitions';
import { FormControlInteractionDescription } from 'src/app/shared/modules/form-generator2/definitions/form-control-interaction.definitions';

@Pipe({
  name: 'calendarDayFormControlsMap',
})
export class CalendarDayFormControlsMapPipe implements PipeTransform {
  private readonly sharedService = inject(SharedService);

  transform(formControls: GeneratedFormControl[]): GeneratedFormControl[] {
    return formControls.map((formControl) => {
      return {
        ...formControl,
        interactionListeners: this.getInteractionListenersToFormControl(formControl, formControls),
      };
    });
  }

  getInteractionListenersToFormControl(formControl: GeneratedFormControl, formControls: GeneratedFormControl[]): FormControlListener[] {
    const interactions: FormControlInteractionDescription[] = [
      {
        formControlNames: ['visibilityEnd'],
        listeners: [
          {
            dependantFormControlNames: 'visibilityStart',
            triggeredBy: ['value'],
            action: (formControl: DatetimeFormControl, dependantFormControl: DatetimeFormControl, updateFormControl) => {
              const endDate = dependantFormControl.value ? new Date(dependantFormControl.value) : null;
              const startDate = formControl.value ? new Date(formControl.value) : null;
              if (differenceInMilliseconds(endDate, startDate) > 0 && endDate && startDate) {
                this.sharedService.showNotification('warning', 'A láthatóság kezdeti dátuma későbbi, mint a vég dátum!');
                const newFormControl: DatetimeFormControl = {
                  ...formControl,
                  value: formControl.initialValue ?? null,
                };
                updateFormControl(newFormControl);
              }
            },
          },
        ],
      },
      {
        formControlNames: ['visibilityStart'],
        listeners: [
          {
            dependantFormControlNames: 'visibilityEnd',
            triggeredBy: ['value'],
            action: (formControl: DatetimeFormControl, dependantFormControl: DatetimeFormControl, updateFormControl) => {
              const endDate = formControl.value ? new Date(formControl.value) : null;
              const startDate = dependantFormControl.value ? new Date(dependantFormControl.value) : null;
              if (differenceInMilliseconds(endDate, startDate) > 0 && endDate && startDate) {
                this.sharedService.showNotification('warning', 'A láthatóság kezdeti dátuma későbbi, mint a vég dátum!');
                const newFormControl: DatetimeFormControl = {
                  ...formControl,
                  value: formControl.initialValue ?? null,
                };
                updateFormControl(newFormControl);
              }
            },
          },
        ],
      },
      {
        formControlNames: ['openableEnd'],
        listeners: [
          {
            dependantFormControlNames: 'openableStart',
            triggeredBy: ['value'],
            action: (formControl: DatetimeFormControl, dependantFormControl: DatetimeFormControl, updateFormControl) => {
              const endDate = dependantFormControl.value ? new Date(dependantFormControl.value) : null;
              const startDate = formControl.value ? new Date(formControl.value) : null;
              if (differenceInMilliseconds(endDate, startDate) > 0 && endDate && startDate) {
                this.sharedService.showNotification('warning', 'A nyithatóság kezdeti dátuma későbbi, mint a vég dátum!');
                const newFormControl: DatetimeFormControl = {
                  ...formControl,
                  value: formControl.initialValue ?? null,
                };
                updateFormControl(newFormControl);
              }
            },
          },
        ],
      },
      {
        formControlNames: ['openableStart'],
        listeners: [
          {
            dependantFormControlNames: 'openableEnd',
            triggeredBy: ['value'],
            action: (formControl: DatetimeFormControl, dependantFormControl: DatetimeFormControl, updateFormControl) => {
              const endDate = formControl.value ? new Date(formControl.value) : null;
              const startDate = dependantFormControl.value ? new Date(dependantFormControl.value) : null;
              if (differenceInMilliseconds(endDate, startDate) > 0 && endDate && startDate) {
                this.sharedService.showNotification('warning', 'A nyithatóság kezdeti dátuma későbbi, mint a vég dátum!');
                const newFormControl: DatetimeFormControl = {
                  ...formControl,
                  value: formControl.initialValue ?? null,
                };
                updateFormControl(newFormControl);
              }
            },
          },
        ],
      },
    ];
    return [...interactions]
      .filter((interaction) => {
        return Array.isArray(interaction.formControlNames)
          ? interaction.formControlNames.includes(formControl.key)
          : interaction.formControlNames === formControl.key;
      })
      .map((interaction) => interaction.listeners)
      .flat()
      .map((listener) => ({ ...listener, formControls }));
  }
}
