@use 'shared' as *;

.newsletter {
  border: 1px solid var(--kui-black-o10);
  padding: 30px;
  margin: 100px auto;
  max-width: 450px;

  @include media-breakpoint-down(sm) {
    padding: 0;
    border: none;
    margin: 20px auto 60px;
    max-width: calc(100% - 30px);
  }

  .page-title {
    font-family: var(--kui-font-condensed);
    font-size: 40px;
    line-height: 44px;
    font-weight: normal;
  }

  &-prompt {
    margin: 20px 0 30px;
    color: var(--kui-gray-800);
    font-size: 16px;
    line-height: normal;

    @include media-breakpoint-down(sm) {
      margin: 20px 0;
    }
  }

  .ps-form-row {
    margin-bottom: 30px;

    @include media-breakpoint-down(sm) {
      margin-bottom: 20px;
    }

    a {
      font-weight: 600;
      color: var(--kui-turquoise-500);
      text-decoration: underline;

      &:hover {
        color: var(--kui-turquoise-500-o90);
      }
    }
  }

  .automizy-form-button-box {
    margin-top: 30px;
  }

  .automizy-form-button {
    cursor: pointer;
    display: block;
    width: 100%;
    padding: 16px 22px;
    border-radius: 10px;
    font-family: var(--kui-font-primary);
    font-size: 14px;
    font-weight: 500;
    line-height: 120%;
    background-color: var(--kui-orange-700);
    color: var(--kui-white);
    box-shadow:
      0 2px 0 0 var(--kui-white-o10) inset,
      0 -2px 0 0 var(--kui-black-o05) inset;

    &:not(:disabled):hover {
      color: var(--kui-white);
      background: linear-gradient(0deg, var(--kui-black-o05) 0%, var(--kui-black-o05) 100%), var(--kui-orange-700);
      box-shadow:
        0 2px 0 0 var(--kui-white-o10) inset,
        0 -2px 0 0 var(--kui-white-o05) inset;
    }

    &:disabled {
      background: var(--kui-gray-800-o70);
    }
  }

  .automizy-privacy {
    margin: 34px 0 24px;
    color: var(--kui-gray-800);
    font-size: 16px;
    line-height: normal;

    @include media-breakpoint-down(md) {
      margin: 24px 0;
    }

    a {
      font-weight: 600;
      color: var(--kui-turquoise-500);
      text-decoration: underline;

      &:hover {
        color: var(--kui-turquoise-500-o90);
      }
    }
  }
}
