import { ChangeDetectionStrategy, Component, computed, inject, signal, Signal } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { toSignal } from '@angular/core/rxjs-interop';
import { ApiResponseMetaList, ApiResult, createCanonicalUrlForPageablePage, GalleryData, LimitableMeta } from '@trendency/kesma-ui';
import { map, tap } from 'rxjs/operators';
import { SeoService, StorageService } from '@trendency/kesma-core';
import { ArticleCardComponent, ArticleCardType, createPSTitle, defaultMetaInfo, PagerComponent, PagerUtils, UrlService } from '../../shared';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';

@Component({
  selector: 'app-galleries',
  templateUrl: './galleries.component.html',
  styleUrl: './galleries.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ArticleCardComponent, PagerComponent, SidebarComponent],
  host: { ngSkipHydration: 'true' },
})
export class GalleriesComponent {
  private readonly route = inject(ActivatedRoute);
  private readonly seoService = inject(SeoService);
  private readonly storageService = inject(StorageService);
  private readonly urlService = inject(UrlService);

  readonly ArticleCardType = ArticleCardType;

  readonly isUserAdultChoice = signal<boolean>(false);

  readonly resolverData = toSignal(
    this.route.data.pipe(
      map(({ data }) => data as ApiResult<GalleryData[], ApiResponseMetaList>),
      tap(() => {
        this.urlService.setPreviousUrl('/galeriak');
        this.setMetaData();
        this.isUserAdultChoice.set(this.storageService.getSessionStorageData('isAdultChoice', false) ?? false);
      })
    )
  );
  readonly galleries: Signal<GalleryData[]> = computed(() => this.resolverData()?.data ?? []);
  readonly limitable: Signal<LimitableMeta | undefined> = computed(() => this.resolverData()?.meta.limitable);

  readonly maxDisplayedPages = inject(PagerUtils).maxDisplayedPages;

  private setMetaData(): void {
    const title = createPSTitle('Galériák');
    const canonical = createCanonicalUrlForPageablePage('galeriak', this.route.snapshot);
    canonical && this.seoService.updateCanonicalUrl(canonical);
    this.seoService.setMetaData({
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    });
  }
}
