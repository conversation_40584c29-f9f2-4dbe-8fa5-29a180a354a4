@use 'shared' as *;

:host {
  display: flex;
  position: fixed;
  z-index: 1001;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: var(--kui-black-o80);
  overflow-y: auto;

  @include media-breakpoint-down(sm) {
    top: 10px;
  }

  .backdrop {
    width: 100%;
    height: 100%;
    z-index: 1;
    position: absolute;
  }

  .card {
    width: 446px;
    margin: auto;
    border-radius: 10px;
    border: 1px solid var(--kui-black-o10);
    background: var(--kui-white);
    box-shadow: 0 2px 4px 0 var(--kui-gray-700-o10);
    padding: 30px;
    z-index: 2;
    position: relative;

    @media screen and (max-height: 700px) {
      margin-bottom: 32px;
    }

    @include media-breakpoint-down(md) {
      width: 100%;
      min-width: 345px;
      max-width: 378px;
      padding: 20px;
    }

    @include media-breakpoint-down(sm) {
      width: 345px;
    }

    &-title {
      font-family: var(--kui-font-secondary);
      font-size: 36px;
      font-weight: 400;
      line-height: 36px;
      margin-bottom: 23px;
    }

    .form {
      display: flex;
      flex-direction: column;
      gap: 14px;

      @include media-breakpoint-up(md) {
        gap: 30px;
      }

      &-error {
        color: var(--kui-orange-800);
        font-weight: 600;
      }

      &-buttons {
        display: flex;
        flex-direction: row;
        gap: 20px;
        margin-top: 6px;

        @include media-breakpoint-down(md) {
          flex-direction: column;
          gap: 15px;
        }

        app-simple-button::ng-deep {
          .btn-primary,
          .btn-secondary {
            width: 182px;

            @include media-breakpoint-down(md) {
              width: 100%;
            }
          }
        }

        &-text {
          white-space: nowrap;
        }
      }
    }

    .profile-delete {
      margin: 40px auto 0;
      &-alert {
        &-title {
          font-weight: 700;
          margin-bottom: 10px;
        }

        p {
          font-size: 14px;
        }
      }

      &-text {
        font-weight: 700;
        line-height: 22px;
      }
    }
  }
}
