@if (vm(); as vm) {
  <kesma-layout
    [breakingNews]="vm.breakingNews"
    [configuration]="vm.configuration"
    [layoutType]="vm.layoutType"
    [structure]="vm.structure"
    [adPageType]="vm.adPageType"
    [blockTitleRef]="blockTitles"
    [contentComponentsRef]="contentComponents"
    [contentComponentWrapperRef]="contentComponentsWrapper()"
    [contentComponentInnerWrapperRef]="contentComponentsInnerWrapper()"
    [blockTitleWrapperRef]="blockTitleWrapper()"
    [editorFrameSize]="editorFrameSize()"
  >
  </kesma-layout>
}

<ng-template #blockTitles let-layoutType="layoutType" let-layoutElement="layoutElement">
  <app-block-title [data]="layoutElement?.blockTitle"></app-block-title>
</ng-template>

<ng-template #contentComponents let-layoutElement="layoutElement" let-desktopWidth="desktopWidth" let-index="index" let-extractor="extractor">
  @if (layoutElement?.config || layoutElement?.configurable === false) {
    @switch (layoutElement.contentType) {
      @case (LayoutElementContentType.Ad) {
        @if (layoutElement.ad) {
          <kesma-advertisement-adocean
            [ad]="layoutElement.ad"
            [isHidden]="layoutElement.contentType !== LayoutElementContentType.Ad && !layoutElement.ad"
          ></kesma-advertisement-adocean>
        }
      }
      @case (LayoutElementContentType.Article) {
        @if (layoutElement.extractorData?.[index]; as data) {
          <app-article-card
            [class.has-horizontal-separator]="layoutElement.withHorizontalSeparator"
            [class.multiple-article]="layoutElement?.contentLength > 1"
            [data]="data"
            [styleID]="layoutElement.styleId"
            [titleBackgroundColor]="data?.titleBackgroundColor"
          ></app-article-card>
        }
      }
      @case (LayoutElementContentType.GALLERY_ARTICLE_LIST) {
        @if (layoutElement.extractorData; as data) {
          <app-article-block [data]="data" [isGalleryBlock]="true" [listPageDetails]="{ name: 'Több galéria', url: '/galeriak' }"></app-article-block>
        }
      }
      @case (LayoutElementContentType.ARTICLES_WITH_VIDEO_CONTENT) {
        @if (layoutElement.extractorData; as data) {
          <app-article-block
            [data]="data"
            [isVideoBlock]="true"
            [isSidebar]="layoutType() === LayoutPageType.SIDEBAR"
            [listPageDetails]="{ name: 'Több videó', url: '/videok' }"
          ></app-article-block>
        }
      }
      @case (LayoutElementContentType.ARTICLES_WITH_PODCAST_CONTENT) {
        @if (layoutElement.extractorData; as data) {
          <app-article-block
            [data]="data"
            [isPodcastBlock]="true"
            [isSidebar]="layoutType() === LayoutPageType.SIDEBAR"
            [listPageDetails]="{ name: 'Több podcast', url: layoutElement?.btnUrl || '/pstv-podcast' }"
          ></app-article-block>
        }
      }
      @case (LayoutElementContentType.MinuteToMinute) {
        @if (layoutElement.extractorData?.[index]; as data) {
          <app-article-card [data]="data" [styleID]="layoutElement.styleId" [isMinuteToMinute]="true"></app-article-card>
        }
      }
      @case (LayoutElementContentType.Opinion) {
        @if (layoutElement.extractorData?.[index]; as data) {
          <app-article-card [class.multiple-article]="layoutElement?.contentLength > 1" [data]="data" [styleID]="layoutElement.styleId"></app-article-card>
        }
      }
      @case (LayoutElementContentType.PrBlock) {
        @if (layoutElement.extractorData?.[index]; as data) {
          <app-article-card [data]="data" [styleID]="layoutElement.styleId"></app-article-card>
        }
      }
      @case (LayoutElementContentType.Wysiwyg) {
        @if (layoutElement.extractorData; as data) {
          <app-wysiwyg-box [htmlArray]="data"></app-wysiwyg-box>
        }
      }
      @case (LayoutElementContentType.Dossier) {
        @if (layoutElement.extractorData; as data) {
          <app-dossier-card [data]="data?.[0]"></app-dossier-card>
        }
      }
      @case (LayoutElementContentType.Image) {
        @if (layoutElement.extractorData; as data) {
          <app-image [data]="data"></app-image>
        }
      }
      @case (LayoutElementContentType.Vote) {
        @if (layoutElement.extractorData; as extractorData) {
          @if ((voteCache[layoutElement?.details?.[0]?.value?.id ?? ''] | async) || extractorData; as voteData) {
            <app-voting [data]="voteData.data" [voteId]="voteData.votedId" (vote)="onVotingSubmit($event, voteData)" />
          }
        }
      }
      @case (LayoutElementContentType.Breaking) {
        @if (layoutElement.config?.selectedBreakings?.[index]; as data) {
          <app-breaking-card [data]="data"></app-breaking-card>
        }
      }
      @case (LayoutElementContentType.HtmlEmbed) {
        @if (index === 0 && layoutElement?.config?.htmlContent; as data) {
          <kesma-html-embed [data]="data" ngSkipHydration></kesma-html-embed>
        }
      }
      @case (LayoutElementContentType.FreshBlock) {
        @if (layoutElement.extractorData?.[index]; as data) {
          <app-fresh-article-card [data]="data"></app-fresh-article-card>
        }
      }
      @case (LayoutElementContentType.BrandingBoxEx) {
        @if (layoutElement.extractorData; as data) {
          <app-branding-box-ex [brand]="layoutElement.brand" [articleLimit]="data.articleLimit" [disabledColumns]="data.disabledColumns"></app-branding-box-ex>
        }
      }
      @case (LayoutElementContentType.IngatlanbazarSearch) {
        <kesma-real-estate-bazaar-search-block
          [showBudapestLocations]="layoutElement.config.showBudapestLocations"
          [showCountyLocations]="layoutElement.config.showBudapestLocations"
          [showOtherLocations]="layoutElement.config.showOtherLocations"
          [showNewBuildButton]="layoutElement.config.showNewBuildButton"
          [showAdvertiseButton]="layoutElement.config.showAdvertiseButton"
          [defaultLocation]="layoutElement.config.defaultLocation"
          [defaultType]="layoutElement.config.defaultType"
          [utmSource]="layoutElement.config.utmSource"
        >
        </kesma-real-estate-bazaar-search-block>
      }
      @case (LayoutElementContentType.NewsletterBlock) {
        <app-newsletter-block [desktopWidth]="desktopWidth" [isSidebar]="layoutType() === LayoutPageType.SIDEBAR"></app-newsletter-block>
      }
    }
  }
  @if (layoutElement.contentType === LayoutElementContentType.PODCAST_APP_RECOMMENDER) {
    <app-podcast-app-recommender [desktopWidth]="desktopWidth"></app-podcast-app-recommender>
  }
  @if (layoutElement.contentType === LayoutElementContentType.IngatlanbazarConfigurable) {
    <kesma-real-estate-bazaar-block
      (initEvent)="handleRealEstateInitEvent()"
      [showHeader]="layoutElement.showHeader"
      [itemsToShow]="layoutElement.itemsToShow"
      [data]="vm().realEstateData"
    >
    </kesma-real-estate-bazaar-block>
  }
  @if (layoutElement.contentType === LayoutElementContentType.INGATLANBAZAR) {
    <kesma-real-estate-bazaar-block
      (initEvent)="handleRealEstateInitEvent()"
      [showHeader]="true"
      [itemsToShow]="1"
      [data]="vm().realEstateData"
    ></kesma-real-estate-bazaar-block>
  }
</ng-template>
