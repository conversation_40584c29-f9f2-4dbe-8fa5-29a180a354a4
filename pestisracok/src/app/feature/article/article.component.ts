import { AsyncPipe, DOCUMENT, NgTemplateOutlet } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  computed,
  DestroyRef,
  effect,
  ElementRef,
  inject,
  OnDestroy,
  OnInit,
  Signal,
  signal,
  viewChild,
  WritableSignal,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { FormatDatePipe, SchemaOrgService, SeoService, UtilService } from '@trendency/kesma-core';
import {
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  AnalyticsService,
  ApiResponseMetaList,
  Article,
  ArticleBody,
  ArticleBodyDetails,
  ArticleBodyType,
  ArticleCard,
  ArticleResolverData,
  ArticleRouteParams,
  ArticleVideoComponent,
  AutoArticleBodyAdService,
  backendDateToDate,
  DossierData,
  GalleryData,
  GalleryElementData,
  getEmailShareUrl,
  getFacebookShareUrl,
  getLinkedinShareUrl,
  getMessengerShareUrl,
  getStructuredDataForArticle,
  getTwitterShareUrl,
  MinuteToMinuteBlock,
  MinuteToMinuteState,
  PAGE_TYPES,
  previewBackendArticleToArticleCard,
  RecommendationsData,
  ScrollPositionService,
  SearchBotService,
  SponsoredTag,
  VoteDataWithAnswer,
  VoteService,
} from '@trendency/kesma-ui';
import { combineLatest, forkJoin, Observable } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import { GalleryService } from 'src/app/shared/services/gallery.service';
import { environment } from 'src/environments/environment';
import {
  AdultComponent,
  AdultService,
  ArticleCardComponent,
  ArticleCardType,
  ArticleSliderGalleryComponent,
  defaultMetaInfo,
  DossierCardComponent,
  mapArticleBodyDossierToDossierData,
  PromotionBoxComponent,
  QuizComponent,
  SliderGalleryFullscreenLayerClickedEvent,
  SocialShareComponent,
  SponsoredTagBoxComponent,
  VotingComponent,
  WysiwygBoxComponent,
} from '../../shared';
import { BackendArticleSocial } from '../comments/api/comment.definitions';
import { CommentService } from '../comments/api/comment.service';
import { CommentSectionComponent } from '../comments/components/comment-section/comment-section.component';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { ArticlePageHeaderComponent } from './components/article-page-header/article-page-header.component';
import { MinuteToMinuteEventBlockComponent } from './components/minute-to-minute-event-block/minute-to-minute-event-block.component';

@Component({
  selector: 'app-article',
  templateUrl: './article.component.html',
  styleUrl: './article.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    SidebarComponent,
    WysiwygBoxComponent,
    AdvertisementAdoceanComponent,
    VotingComponent,
    CommentSectionComponent,
    AdultComponent,
    ArticlePageHeaderComponent,
    SocialShareComponent,
    ArticlePageHeaderComponent,
    NgTemplateOutlet,
    RouterLink,
    ArticleVideoComponent,
    DossierCardComponent,
    QuizComponent,
    ArticleCardComponent,
    ArticleSliderGalleryComponent,
    MinuteToMinuteEventBlockComponent,
    PromotionBoxComponent,
    SponsoredTagBoxComponent,
    AsyncPipe,
  ],
  providers: [AutoArticleBodyAdService, FormatDatePipe, CommentService],
})
export class ArticleComponent implements OnInit, AfterViewInit, OnDestroy {
  private readonly route = inject(ActivatedRoute);
  private readonly destroyRef = inject(DestroyRef);
  private readonly articleBodyAdService = inject(AutoArticleBodyAdService);
  private readonly seoService = inject(SeoService);
  private readonly schemaService = inject(SchemaOrgService);
  private readonly analyticsService = inject(AnalyticsService);
  private readonly searchBotService = inject(SearchBotService);
  private readonly formatDate = inject(FormatDatePipe);
  private readonly commentService = inject(CommentService);
  private readonly utils = inject(UtilService);
  private readonly voteService = inject(VoteService);
  private readonly router = inject(Router);
  private readonly galleryService = inject(GalleryService);
  private readonly document = inject(DOCUMENT);
  private readonly adStoreAdo = inject(AdvertisementAdoceanStoreService);
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly utilsService = inject(UtilService);
  private readonly scrollPositionService = inject(ScrollPositionService);
  readonly adultService = inject(AdultService);

  readonly article = signal<Article>({} as Article);
  readonly articleSocials = signal<BackendArticleSocial | null>(null);
  readonly articleMeta = signal<ApiResponseMetaList | null>(null);
  readonly recommendations = signal<RecommendationsData | null>(null);
  readonly minuteToMinuteBlocks = signal<MinuteToMinuteBlock[]>([]);
  readonly adPageType = signal<string>(PAGE_TYPES.all_articles_and_sub_pages);
  readonly interrupter = signal<AdvertisementsByMedium | null>(null);
  readonly canonicalUrl = signal<string>('');
  readonly isSearchBot = signal<boolean>(false);
  readonly galleries = signal<Record<string, GalleryData>>({});

  readonly facebookLink = signal<string>('');
  readonly messengerLink = signal<string>('');
  readonly xLink = signal<string>('');
  readonly linkedInLink = signal<string>('');
  readonly mailLink = signal<string>('');
  readonly copyLink = signal<string>('');
  readonly dataTrigger = viewChild<ElementRef<HTMLDivElement>>('dataTrigger');
  readonly adverts = signal<AdvertisementsByMedium | null>(null);
  isExceptionAdvertEnabled = false;

  sponsoredTag: WritableSignal<SponsoredTag | null> = signal<SponsoredTag | null>(null);

  readonly isThumbnailOrLeadVideoFree = computed(
    () => (!this.article()?.videoLead?.video && !this.article()?.thumbnail) || (this.article().hideThumbnailFromBody && !this.article()?.videoLead?.video)
  );
  readonly dossierData: Signal<DossierData | undefined> = computed(() => {
    const dossier = this.article()?.body?.find((bodyPart) => bodyPart.type === ArticleBodyType.SubsequentDossier);

    if (!dossier) {
      return;
    }

    return mapArticleBodyDossierToDossierData(dossier.details?.[0]?.value);
  });
  readonly isMinuteToMinute = computed(() => this.article().minuteToMinute !== MinuteToMinuteState.NOT);
  readonly tenArticleRecommender = signal<{ title?: string; articles?: ArticleCard[] }>({});
  readonly ArticleCardType = ArticleCardType;
  readonly ArticleBodyType = ArticleBodyType;

  readonly voteCache = this.voteService.voteCache;

  constructor() {
    effect(() => {
      const dataTrigger = this.dataTrigger()?.nativeElement;
      if (!dataTrigger) {
        return;
      }
      if (!this.utilsService.isBrowser()) {
        return;
      }
      if ('IntersectionObserver' in window) {
        this.observeArticleEnd();
      }
    });
  }

  ngOnInit(): void {
    this.route.data
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        map(({ data }) => data)
      )
      .subscribe(({ article: articleResponse, recommendations, url }) => {
        this.articleBodyAdService.init(articleResponse.data.body);
        const articleBody = this.articleBodyAdService.autoAd();
        const article = articleResponse.data;

        this.article.set({
          ...articleResponse.data,
          body: this.#prepareArticleBody(articleBody),
        });
        this.sponsoredTag?.set(articleResponse?.meta?.['sponsoredTag']);
        this.recommendations.set(recommendations.data);
        this.articleMeta.set(articleResponse.meta);
        this.minuteToMinuteBlocks.set(
          article?.minuteToMinuteBlocks?.map((block: MinuteToMinuteBlock) => ({
            ...block,
            date: typeof block?.date === 'string' ? backendDateToDate(block?.date) : block?.date,
          }))
        );

        this.adPageType.set(`column_${article?.primaryColumn?.parent?.slug || article?.primaryColumn?.slug}`);
        this.canonicalUrl.set(article?.seo?.seoCanonicalUrl || article?.canonicalUrl || (url ? `${this.seoService.hostUrl}/${url}` : this.seoService.hostUrl));
        this.adultService.isAdultArticle.set(article?.isAdultsOnly ?? false);

        this.voteService.initArticleVotes(this.article());
        this.loadEmbeddedGalleries();
        this.getTenArticleRecommender();
        this.getShareUrl();
        this.initComments();
        this.setMetaData();
        this.setStructuredSchema();
        this.sendPageView();
      });
    (
      combineLatest([
        this.route.data as Observable<{
          data: ArticleResolverData;
        }>,
        this.adStoreAdo.isAdult.asObservable(),
      ]) as Observable<any>
    )
      .pipe(takeUntilDestroyed(this.destroyRef))
      .pipe(
        map<[{ data: ArticleResolverData }, boolean], boolean | undefined>(
          ([
            {
              data: { article },
            },
            _isAdult,
          ]) => {
            // RESET ADOCEAN META TAGS
            // ngOnDestroy WILL NOT BE CALLED WHEN WE NAVIGATE TO ANOTHER ARTICLE FROM ARTICLE!!
            const advertMeta = this.adStoreAdo.advertMeta$.getValue();
            this.adStoreAdo.advertMeta$.next({
              vars: advertMeta.vars,
              keys: '',
            });
            this.isExceptionAdvertEnabled = article?.data?.isExceptionAdvertEnabled;
            return article?.data?.withoutAds;
          }
        ),

        switchMap((withoutAds) => {
          this.resetAds();
          withoutAds ? this.adStoreAdo.disableAds() : this.adStoreAdo.enableAds();
          return this.adStoreAdo.advertisemenets$;
        })
      )
      .subscribe((adsCollection): void => {
        this.adverts.set(this.adStoreAdo.separateAdsByMedium(adsCollection, this.adPageType()));
        this.adStoreAdo.onArticleLoaded();
        this.cdr.detectChanges();
      });
  }

  ngAfterViewInit(): void {
    this.route.fragment.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((fragment) => {
      // Scrolling is only possible in the browser.
      if (this.utils.isBrowser() && fragment) {
        const elem = this.document.getElementById(fragment);
        if (elem) {
          window.requestAnimationFrame(() => {
            elem.scrollIntoView({ block: 'start' });
          });
        }
      }
    });
  }

  getShareUrl(): void {
    const fbAppId = environment.facebookAppId || '';
    const articleUrl = this.seoService.currentUrl;

    this.facebookLink.set(getFacebookShareUrl(articleUrl));
    this.messengerLink.set(getMessengerShareUrl(fbAppId, articleUrl));
    this.xLink.set(getTwitterShareUrl(articleUrl));
    this.linkedInLink.set(getLinkedinShareUrl(articleUrl));
    this.mailLink.set(getEmailShareUrl(articleUrl, this.article()?.title));
    this.copyLink.set(articleUrl); // Todo: Minute to minute link
  }

  private initComments(): void {
    if (!this.utils.isBrowser() || !this.article().id) {
      // No comments in SSR
      return;
    }

    this.commentService
      .getArticleSocials(this.article().id)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((articleSocials) => {
        this.articleSocials.set(articleSocials);
      });
  }

  private loadEmbeddedGalleries(): void {
    const bodyElements = this.article()?.body ?? [];
    const gallerySubs = ((bodyElements ?? []) as GalleryElementData[])
      .filter(({ type }) => type === ArticleBodyType.Gallery)
      .map((bodyElem: GalleryElementData) => this.galleryService.getGalleryDetails(bodyElem.details?.[0]?.value?.slug));

    forkJoin(gallerySubs)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((galleries) => {
        galleries.forEach((gallery) => {
          const galleryData: GalleryData = {
            ...gallery,
            highlightedImageUrl: gallery.highlightedImage.url,
          } as any as GalleryData;
          this.galleries.update((galleries) => ({ ...galleries, [gallery.id]: galleryData }));
        });
        this.scrollPositionService.restoreScrollPosition();
      });
  }

  openGalleryDedicatedRouteLayer({ gallery, selectedImageIndex }: SliderGalleryFullscreenLayerClickedEvent): void {
    if (!gallery || !this.utils.isBrowser()) {
      return;
    }

    const url = location.pathname;
    const galleryUrl = ['/', 'galeria', gallery?.slug, ...(selectedImageIndex || selectedImageIndex === 0 ? [selectedImageIndex + 1] : [])];

    this.router.navigate(galleryUrl, { state: { referrerArticle: url } }).then();
  }

  private getTenArticleRecommender(): void {
    const tenArticles = this.article()?.body?.find((bodyPart) => bodyPart.type === ArticleBodyType.TenArticleRecommender);
    if (!tenArticles) {
      return;
    }
    const { details } = tenArticles;
    const result: { title: string; articles: ArticleCard[] } = {
      title: '',
      articles: [],
    };

    details?.forEach((detailItem) => {
      if (detailItem?.key === 'title') {
        result.title = detailItem.value || '';
      }
      if (detailItem?.key?.includes('article') && detailItem?.value) {
        const card = previewBackendArticleToArticleCard(detailItem.value);
        result.articles.push(card);
      }
    });

    this.tenArticleRecommender.set(result);
  }

  private sendPageView(): void {
    const lastUpdatedAt = (this.article()?.lastUpdated || this.article().publishDate) as Date;
    setTimeout(() => {
      this.analyticsService.sendPageView({
        pageCategory: this.article()?.primaryColumn?.parent?.slug || this.article()?.primaryColumn?.slug || '',
        customDim2: this.article()?.topicLevel1,
        customDim1: this.article()?.aniCode,
        title: this.article().title,
        articleSource: this.article()?.articleSource || 'no source',
        publishDate: this.formatDate.transform(this.article()?.publishDate as Date, 'dateTime'),
        lastUpdatedDate: this.formatDate.transform(lastUpdatedAt, 'dateTime'),
      });
    });
    this.isSearchBot.set(this.searchBotService.isSearchBot());
    this.article()?.isAdultsOnly ? this.searchBotService.insertAdultMetaTag() : this.searchBotService.removeAdultMetaTag();
  }

  onVotingSubmit(votedId: string, voteData: VoteDataWithAnswer): void {
    this.voteService.onVotingSubmit(votedId, voteData).subscribe(() => this.cdr.detectChanges());
  }

  private setMetaData(): void {
    const { lead, thumbnail, publicAuthor, publishDate, alternativeTitle } = this.article() || {};
    if (!this.article()) {
      return;
    }

    const finalTitle = alternativeTitle || this.article()?.seo?.seoTitle || this.article()?.title;
    const finalOgTitle = alternativeTitle || this.article()?.title;

    this.seoService.setMetaData({
      title: finalTitle,
      description: this.article()?.seo?.seoDescription || lead || defaultMetaInfo.description,
      ogTitle: finalOgTitle,
      ogImage: thumbnail,
      ogType: 'article',
      articleAuthor: publicAuthor,
      author: publicAuthor,
      articlePublishedTime: publishDate?.toISOString(),
      robots: this.article()?.seo?.seoRobotsMeta || 'index, follow, max-image-preview:large',
    });

    //By default, the updateCanonicalUrl prefixes the url with the host, so we pass a second parameter to force absolute url.
    this.seoService.updateCanonicalUrl(this.canonicalUrl(), { addHostUrl: false, skipSeoMetaCheck: true });
  }

  private setStructuredSchema(): void {
    if (this.article()) {
      this.schemaService.removeStructuredData();
      this.schemaService.insertSchema(
        getStructuredDataForArticle(this.article(), this.seoService.currentUrl, environment?.siteUrl ?? '', { hasAuthorPageSlug: true })
      );
    }
  }

  setAdMetaAndPageType(articleResponse: Article): void {
    this.adPageType.set(`column_${articleResponse?.primaryColumn?.slug}`);
    this.adStoreAdo.setArticleParentCategory(this.adPageType());
    this.adStoreAdo.getAdvertisementMeta(articleResponse?.tags, articleResponse?.isAdultsOnly);
  }

  ngOnDestroy(): void {
    this.adStoreAdo.onArticleDestroy();
  }

  #prepareArticleBody(body: ArticleBody[]): ArticleBody[] {
    let advertIndex = 1;
    return body.map((bodyPart: ArticleBody) => ({
      ...bodyPart,
      details: (bodyPart.details ?? []).map((detail: ArticleBodyDetails) => ({
        ...detail,
        ...this.#prepareArticleBodyDetail(detail, bodyPart.type),
      })),
      ...(bodyPart.type === ArticleBodyType.Advert && {
        adverts: {
          mobile: `mobilinterrupter_${advertIndex}`,
          desktop: `desktopinterrupter_${advertIndex++}`,
        },
      }),
    }));
  }

  #prepareArticleBodyDetail(detail: ArticleBodyDetails, type: ArticleBodyType): ArticleBodyDetails {
    let newDetail: ArticleBodyDetails;
    switch (type) {
      case ArticleBodyType.Article:
        newDetail = { ...detail, value: previewBackendArticleToArticleCard(detail.value) };
        break;
      default:
        newDetail = { ...detail };
    }
    return newDetail;
  }
  private observeArticleEnd(): void {
    const elem = this.dataTrigger()?.nativeElement;
    if (!elem) {
      return;
    }

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(({ isIntersecting }) => {
        if (isIntersecting) {
          this.sendEcommerceEvent();
          if (elem) {
            observer.unobserve(elem);
          }
        }
      });
    });
    observer.observe(elem);
  }

  private sendEcommerceEvent(): void {
    const routeParams: ArticleRouteParams = this.route.snapshot.params as ArticleRouteParams;
    const article = this.article();
    if (!article) {
      return;
    }
    this.analyticsService.sendEcommerceEvent({
      id: `T${article.id}`,
      title: article.title,
      articleSlug: routeParams.articleSlug ? routeParams.articleSlug : 'cikk-elonezet',
      category: article.columnTitle ?? '',
      articleSource: article.articleSource ? article.articleSource : 'no source',
      publishDate: this.formatDate.transform(article.publishDate as Date, 'dateTime'),
      lastUpdatedDate: this.formatDate.transform((article.lastUpdated ? article.lastUpdated : article.publishDate) as Date, 'dateTime'),
    });
  }
  private resetAds(): void {
    this.adverts.set(null);
    this.cdr.detectChanges();
  }
}
