@if (adultService.showAdultLayer()) {
  <app-adult (isUserAdult)="adultService.onIsUserAdultChoose($event)" />
} @else {
  <section class="article" [class.without-pr]="!article()?.sponsorship && !isMinuteToMinute()">
    @if (article()?.sponsorship; as sponsorship) {
      @if (sponsorship?.thumbnailUrl; as sponsorImgUrl) {
        <div class="wrapper">
          <!-- Empty div because of the design -->
          <div class="left-column hide-on-mobile social-share-container"></div>
          <div class="center">
            <a class="article-pr-link" [class.is-minute-to-minute]="isMinuteToMinute()" [href]="sponsorship?.url" target="_blank">
              <img [src]="sponsorImgUrl" alt="Szponzorált tartalom" class="article-pr-img" loading="eager" />
            </a>
          </div>
        </div>
      }
    }

    @if (isMinuteToMinute()) {
      <div class="wrapper">
        <h2 class="article-minute-to-minute-title">Percről-percre</h2>
      </div>
    }

    <div class="wrapper" [class.with-aside]="isThumbnailOrLeadVideoFree()" [class.two-sided]="isThumbnailOrLeadVideoFree()">
      <div class="left-column hide-on-mobile social-share-container">
        <app-social-share
          [facebookLink]="facebookLink()"
          [messengerLink]="messengerLink()"
          [xLink]="xLink()"
          [linkedInLink]="linkedInLink()"
          [mailtoLink]="mailLink()"
          [copyLink]="copyLink()"
        ></app-social-share>
      </div>
      <section class="center">
        <app-article-page-header [data]="article()"></app-article-page-header>

        @if (adverts()?.desktop?.leaderboard_1; as ad) {
          <div class="leaderboard-1-ad">
            <kesma-advertisement-adocean [isExceptionAdvertEnabled]="isExceptionAdvertEnabled" [ad]="ad"></kesma-advertisement-adocean>
          </div>
        }

        <div [class.inner-wrapper]="!isThumbnailOrLeadVideoFree()">
          <div>
            @if (article()?.lead || article()?.excerpt) {
              <p class="article-lead">{{ article()?.lead || article()?.excerpt }}</p>
            }
            @if (adverts()?.desktop?.roadblock_1; as ad) {
              <kesma-advertisement-adocean [isExceptionAdvertEnabled]="isExceptionAdvertEnabled" [ad]="ad"></kesma-advertisement-adocean>
            }
            @if (adverts()?.mobile?.mobilrectangle_1; as ad) {
              <kesma-advertisement-adocean [isExceptionAdvertEnabled]="isExceptionAdvertEnabled" [ad]="ad"></kesma-advertisement-adocean>
            }
            @for (element of article()?.body; track element.id) {
              @switch (element.type) {
                @case (ArticleBodyType.Wysywyg) {
                  @for (wysiwygDetail of element?.details; track wysiwygDetail) {
                    <app-wysiwyg-box [html]="wysiwygDetail?.value"></app-wysiwyg-box>
                  }
                }
                @case (ArticleBodyType.Advert) {
                  @if (interrupter()?.mobile?.[$any(element).adverts.mobile]; as ad) {
                    <kesma-advertisement-adocean [isExceptionAdvertEnabled]="isExceptionAdvertEnabled" [ad]="ad"></kesma-advertisement-adocean>
                  }
                  @if (interrupter()?.desktop?.[$any(element).adverts.desktop]; as ad) {
                    <kesma-advertisement-adocean [isExceptionAdvertEnabled]="isExceptionAdvertEnabled" [ad]="ad"></kesma-advertisement-adocean>
                  }
                }
                @case (ArticleBodyType.Voting) {
                  @if (voteCache[element?.details?.[0]?.value?.id] | async; as voteData) {
                    <app-voting (vote)="onVotingSubmit($event, voteData)" [data]="voteData?.data" [voteId]="voteData?.votedId" />
                  }
                }
                @case (ArticleBodyType.SubsequentDossier) {
                  <app-dossier-card [data]="dossierData()" [isArticlePage]="true" [articleLimit]="3"></app-dossier-card>
                }
                @case (ArticleBodyType.Quiz) {
                  <app-quiz [data]="element?.details[0]?.value"></app-quiz>
                }
                @case (ArticleBodyType.MediaVideo) {
                  <kesma-article-video class="block" [data]="element?.details[0]?.value"></kesma-article-video>
                }
                @case (ArticleBodyType.Gallery) {
                  @if (galleries()?.[element?.details[0]?.value?.id]; as gallery) {
                    <app-article-slider-gallery
                      [isInsideAdultArticleBody]="article()?.isAdultsOnly ?? false"
                      (fullscreenLayerClicked)="openGalleryDedicatedRouteLayer($event)"
                      [data]="gallery"
                      [highlightedImageId]="element?.details[1]?.value?.id"
                    ></app-article-slider-gallery>
                  }
                }
                @case (ArticleBodyType.TenArticleRecommender) {
                  @if (tenArticleRecommender()?.title; as title) {
                    <h2 class="ten-article-recommendation-title">{{ title }}</h2>
                  }
                  @if (tenArticleRecommender()?.articles; as articles) {
                    <div class="ten-article-recommendation">
                      @for (article of articles; track article?.id) {
                        <app-article-card [data]="article" [styleID]="ArticleCardType.TenArticleRecommendation"></app-article-card>
                      }
                    </div>
                  }
                }
                @case (ArticleBodyType.Promotion) {
                  <app-promotion-box [data]="element?.details[0]?.value"></app-promotion-box>
                }
              }
            }
            @if (isMinuteToMinute() && minuteToMinuteBlocks()?.length) {
              <app-minute-to-minute-event-block [minuteToMinuteBlocks]="minuteToMinuteBlocks()"></app-minute-to-minute-event-block>
            }

            @if (article()) {
              <div #dataTrigger></div>
            }
            <ng-container *ngTemplateOutlet="tagTemplate; context: { hideClass: 'hide-on-desktop' }"></ng-container>
            @if (sponsoredTag(); as sponsoredTag) {
              <app-sponsored-tag-box [sponsoredTag]="sponsoredTag" [excludedSlug]="article()?.slug || ''" />
            }

            @if (articleSocials() && !articleSocials()?.disableComments) {
              <app-comment-section [articleId]="article().id" [commentCount]="articleSocials()?.commentCount ?? 0" />
            }

            @if (adverts()?.desktop?.roadblock_2; as ad) {
              <kesma-advertisement-adocean [isExceptionAdvertEnabled]="isExceptionAdvertEnabled" [ad]="ad"></kesma-advertisement-adocean>
            }
            @if (adverts()?.mobile?.mobilrectangle_2; as ad) {
              <kesma-advertisement-adocean [isExceptionAdvertEnabled]="isExceptionAdvertEnabled" [ad]="ad"></kesma-advertisement-adocean>
            }
          </div>
          @if (!isThumbnailOrLeadVideoFree()) {
            <ng-container *ngTemplateOutlet="asideTemplate"></ng-container>
          }
        </div>
      </section>
      @if (isThumbnailOrLeadVideoFree()) {
        <ng-container *ngTemplateOutlet="asideTemplate"></ng-container>
      }
    </div>
  </section>
}

<ng-template #asideTemplate>
  <aside>
    <ng-container *ngTemplateOutlet="tagTemplate; context: { hideClass: 'hide-on-mobile' }"></ng-container>
    <app-sidebar
      [articleId]="article()?.id"
      [articleSlug]="article()?.slug"
      [categorySlug]="article()?.primaryColumn?.slug"
      [adPageType]="adPageType()"
    ></app-sidebar>
  </aside>
</ng-template>

<ng-template #tagTemplate let-hideClass="hideClass">
  @if (article()?.tags?.length > 0) {
    <div class="article-tags" [class]="hideClass">
      <div class="tags-container-title">Kapcsolódó témák</div>
      <div class="article-tags-container">
        @for (tag of article()?.tags; track tag.id) {
          <a class="article-tag" [routerLink]="['/', 'cimke', tag?.slug]">
            {{ tag?.title }}
          </a>
        }
      </div>
    </div>
  }
</ng-template>
