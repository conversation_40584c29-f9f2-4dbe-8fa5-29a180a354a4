import { Routes } from '@angular/router';
import { AuthGuard, BaseComponent, CheckRedirectBefore404, initResolver } from './shared';
import { PageValidatorGuard } from '@trendency/kesma-ui';

export const routes: Routes = [
  {
    path: 'layout-editor',
    loadChildren: () => import('./feature/layout-editor/layout-editor.routing').then((m) => m.layoutEditorRoutes),
  },
  {
    path: '',
    component: BaseComponent,
    resolve: { data: initResolver },
    children: [
      {
        path: '',
        pathMatch: 'full',
        loadChildren: () => import('./feature/home/<USER>').then((m) => m.HOME_ROUTES),
      },
      // "short-circuit" - ha a file nem létezik, az SSR lefut - ami file lehet, azt küldjük 404-re
      {
        path: 'assets/:file',
        redirectTo: '404',
      },
      {
        path: 'assets/:dir/:file',
        redirectTo: '404',
      },
      {
        path: 'script/:file',
        redirectTo: '404',
      },
      {
        path: 'rovat',
        loadChildren: () => import('./feature/category/category.routing').then((m) => m.CATEGORY_ROUTES),
        runGuardsAndResolvers: 'paramsOrQueryParamsChange',
        data: {
          omitGlobalPageView: true,
        },
      },
      {
        path: 'cimke',
        loadChildren: () => import('./feature/tags-page/tags-page.routing').then((m) => m.TAGS_PAGE_ROUTES),
        runGuardsAndResolvers: 'paramsOrQueryParamsChange',
      },
      {
        path: 'galeriak',
        pathMatch: 'full',
        loadChildren: () => import('./feature/galleries/galleries.routing').then((m) => m.GALLERIES_ROUTES),
      },
      {
        path: 'galeria/:gallerySlug',
        loadChildren: () => import('./feature/gallery-layer/gallery-layer.routing').then((m) => m.GALLERY_LAYER_ROUTES),
      },
      {
        path: 'impresszum',
        loadChildren: () => import('./feature/imprint/imprint.routes').then((m) => m.IMPRINT_ROUTES),
      },
      {
        path: 'szerzo',
        loadChildren: () => import('./feature/authors/authors.routing').then((m) => m.AUTHORS_PAGE_ROUTES),
      },
      {
        path: 'szerzok',
        loadChildren: () => import('./feature/authors/authors.routing').then((m) => m.AUTHORS_PAGE_ROUTES),
      },
      {
        path: 'kereses',
        loadChildren: () => import('./feature/search/search.routing').then((m) => m.SEARCH_ROUTES),
      },
      {
        path: 'bejelentkezes',
        loadChildren: () => import('./feature/login/login.routing').then((m) => m.loginRouting),
      },
      {
        path: 'regisztracio',
        loadChildren: () => import('./feature/registration/registration.routing').then((m) => m.registrationRouting),
      },
      {
        path: 'elfelejtett-jelszo',
        loadChildren: () => import('./feature/forgot-password/forgot-password.routing').then((m) => m.forgotPasswordRouting),
      },
      {
        path: 'hirlevel',
        loadChildren: () => import('./feature/newsletter/newsletter.routing').then((m) => m.newsletterRoutes),
      },
      {
        path: 'dosszie',
        loadChildren: () => import('./feature/dossiers/dossiers.routing').then((m) => m.DOSSIERS_ROUTES),
      },
      {
        path: 'videok',
        loadChildren: () => import('./feature/videos-page/videos-page.routes').then((m) => m.VIDEOS_PAGE_ROUTES),
      },

      {
        path: 'elrendezes-elonezet/:layoutHash',
        loadChildren: () => import('./feature/layout-preview/layout-preview.routing').then((m) => m.LAYOUT_PREVIEW_ROUTES),
        data: {
          omitGlobalPageView: true,
        },
      },
      {
        path: 'cikk-elonezet/:previewHash',
        loadChildren: () => import('./feature/article/article.routes').then((m) => m.ARTICLE_ROUTES),
        data: {
          skipSeoMetaCheck: true,
          omitGlobalPageView: true,
        },
      },
      {
        path: 'cikk-elonezet/:previewHash/:previewType',
        loadChildren: () => import('./feature/article/article.routes').then((m) => m.ARTICLE_ROUTES),
        data: {
          skipSeoMetaCheck: true,
          omitGlobalPageView: true,
        },
      },
      {
        path: 'cikk-elonezet/:previewHash/:previewType',
        loadChildren: () => import('./feature/article/article.routes').then((m) => m.ARTICLE_ROUTES),
        data: {
          skipSeoMetaCheck: true,
          omitGlobalPageView: true,
        },
      },
      {
        path: 'profil',
        loadComponent: () => import('./feature/profile/profile.component').then((m) => m.ProfileComponent),
        canActivate: [PageValidatorGuard, AuthGuard],
      },
      {
        path: 'kijelentkezes',
        canActivate: [AuthGuard],
        loadChildren: () => import('./feature/logout/logout.routing').then((m) => m.LOGOUT_ROUTES),
      },
      {
        path: '404',
        loadComponent: () => import('./shared/components/404/404.component').then((m) => m.Error404Component),
        canActivate: [CheckRedirectBefore404],
      },
      {
        path: ':categorySlug/:year/:month/:articleSlug',
        loadChildren: () => import('./feature/article/article.routes').then((m) => m.ARTICLE_ROUTES),
        data: {
          skipSeoMetaCheck: true,
          showRecommendations: true,
          omitGlobalPageView: true,
        },
      },
      {
        path: ':slug',
        loadChildren: () => import('./feature/static-page/static-page.routing').then((m) => m.STATIC_PAGE_ROUTES),
        data: {
          omitGlobalPageView: true,
        },
      },
      {
        path: '**',
        redirectTo: '404',
      },
    ],
  },
];
