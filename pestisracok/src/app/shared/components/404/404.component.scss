@use 'shared' as *;

:host {
  @include media-breakpoint-down(md) {
    height: 90%;
  }

  .main-wrapper {
    --size: clamp(96px, 15vw, 250px);
    // To calculate pixel values to relative values using the size variable, use the following formula: {size in pixel / 250} * var(--size)
    padding: var(--size) 30px 30px;
    aspect-ratio: 21/9;
    margin-bottom: 130px;
    position: relative;
    justify-content: center;
    @extend %flex;

    @include media-breakpoint-down(lg) {
      padding-top: calc(var(--size) * 1.5);
    }

    @include media-breakpoint-down(md) {
      aspect-ratio: unset;
      padding-top: calc(var(--size) * 1);
    }

    @include media-breakpoint-down(sm) {
      aspect-ratio: unset;
      padding-top: calc(var(--size) * 0.5);
    }

    &-title {
      font-size: var(--size);
      font-weight: 700;
      line-height: 100%;
      font-family: var(--kui-font-primary);
    }

    &-inner {
      position: relative;

      .crumbled-paper {
        position: absolute;
        width: var(--size);
        transform: rotate(-127.318deg);
        flex-shrink: 0;
        left: calc(var(--size) * -0.85);
        bottom: calc(var(--size) * -0.1);
        z-index: 1;

        &-shadow {
          position: absolute;
          width: calc(var(--size) * 1.5);
          left: calc(var(--size) * -1);
          bottom: calc(var(--size) * -0.3);
          z-index: 0;
        }
      }

      .decor {
        position: absolute;
        width: calc(var(--size) * 0.672);
        height: calc(var(--size) * 0.596);
        right: calc(var(--size) * -0.36);
        top: calc(var(--size) * -0.5);
      }
    }

    &-description {
      font-family: var(--kui-font-primary);
      font-size: 24px;
      line-height: 150%;
      font-weight: 400;
      text-align: center;
      max-width: 650px;

      @include media-breakpoint-down(md) {
        text-align: left;
        font-size: 16px;
        margin-left: 0;
      }

      &-container {
        position: relative;
        gap: 40px;
        z-index: 1;
        margin-top: 10px;
        @extend %flex;

        @include media-breakpoint-down(md) {
          gap: 20px;
          margin-top: 20px;
        }
      }
    }
  }
}

.home-btn {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  line-height: 120%;

  @include media-breakpoint-down(md) {
    width: 100%;
  }
}

%flex {
  display: flex;
  flex-direction: column;
  align-items: center;
}
