import { ChangeDetectionStrategy, Component, computed, DestroyRef, HostBinding, inject, input, OnInit, signal } from '@angular/core';
import {
  ArticleCard,
  backendDateToDate,
  BaseComponent,
  buildArticleUrl,
  buildColumnUrl,
  IconComponent,
  MinuteToMinuteState,
  FocusPointDirective,
  LimitStringPipe,
  toBool,
} from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';
import { ArticleCardIconTypes, ArticleCardType, UncoloredBackgroundTitleVariants } from '../../definitions/article-card.definitions';
import { NgTemplateOutlet } from '@angular/common';
import { DateFnsModule } from 'ngx-date-fns';
import { hu } from 'date-fns/locale';
import { AvatarInitialsComponent } from '../avatar-initials/avatar-initials.component';
import { BreakpointObserver } from '@angular/cdk/layout';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { map } from 'rxjs';

const MOBILE_BREAKPOINT = '(max-width: 767.98px)';

@Component({
  selector: 'app-article-card',
  templateUrl: './article-card.component.html',
  styleUrl: './article-card.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, NgTemplateOutlet, IconComponent, DateFnsModule, AvatarInitialsComponent, FocusPointDirective, LimitStringPipe],
})
export class ArticleCardComponent extends BaseComponent<ArticleCard> implements OnInit {
  @HostBinding('class') hostClass?: string;
  @HostBinding('attr.style') hostStyle = '';

  private readonly destroyRef = inject(DestroyRef);
  private readonly breakpointObserver = inject(BreakpointObserver);

  isMobile = toSignal(
    this.breakpointObserver.observe([MOBILE_BREAKPOINT]).pipe(
      map(({ matches }) => matches),
      takeUntilDestroyed(this.destroyRef)
    )
  );

  readonly styleID = input.required<ArticleCardType, ArticleCardType>({
    transform: (value: ArticleCardType) => {
      this.styleId.set(value);
      this.hostClass = `style-${value}`;

      if (!this.hasUncoloredTitleBackground()) {
        this.setTitleColor(this.data?.titleBackgroundColor);
      }

      return value;
    },
  });

  readonly titleBackgroundColor = input<string, string>('', {
    transform: (value: string) => {
      if (!this.hasUncoloredTitleBackground()) {
        this.setTitleColor(this.data?.titleBackgroundColor);
      }
      return value;
    },
  });

  readonly hasTitleUnderline = input<boolean>(false); // cím aláhúzás dossziéhez
  readonly isGalleryType = input<boolean>(false);
  readonly isVideoType = input<boolean>(false);
  readonly isPodcastType = input<boolean>(false);
  readonly isMinuteToMinute = input<boolean>(false);
  readonly isRecommendationArticle = input<boolean>(false);
  readonly showAdultLayer = input<boolean>(false);
  readonly hasLead = input<boolean>(true);

  hasBackgroundTitle = signal<boolean>(false);
  readonly styleId = signal<ArticleCardType>(ArticleCardType.MainImgCategoryTitleLeadAuthorDate);

  readonly articleFirstIcon = computed(() => this.setFirstIcon());
  readonly hasUncoloredTitleBackground = computed(() => !!UncoloredBackgroundTitleVariants.includes(this.styleId()));
  readonly hasThumbnail = computed(() => this.displayedThumbnailUrl !== 'assets/images/editor-placeholder.svg');
  readonly isArticleCardWithSmallImage = computed(() => this.checkSmallImageArticleCard());

  readonly currentYear = new Date().getFullYear();
  readonly publishDateFormat = computed(() => (this.currentYear === this.publishDate?.getFullYear() ? 'MMMM d.' : 'yyyy MMMM d.'));
  readonly ArticleCardType = ArticleCardType;
  readonly hu = hu;
  readonly MinuteToMinuteState = MinuteToMinuteState;

  publishDate?: Date | null;
  articleLink?: string[];
  categoryUrl?: string[];
  displayedThumbnailUrl?: string;

  override ngOnInit(): void {
    super.ngOnInit();
    if (this.isArticleCardWithSmallImage()) {
      this.hostClass += ' small-article-card-mobile';
    }
  }

  protected override setProperties(): void {
    this.publishDate = this.data?.publishDate instanceof Date ? this.data?.publishDate : backendDateToDate(this.data?.publishDate as string);
    this.displayedThumbnailUrl = this.data?.thumbnail?.url || 'assets/images/editor-placeholder.svg';
    this.articleLink = this.data && buildArticleUrl(this.data);
    this.categoryUrl = this.data && buildColumnUrl(this.data);
  }

  protected setTitleColor(color: string | undefined): void {
    let titleColor = 'var(--kui-black)';
    let titleBackgroundColor = 'transparent';
    this.hasBackgroundTitle.set(false);

    if (color && color !== '#ffffff') {
      titleColor = 'var(--kui-white)';
      titleBackgroundColor = color;
      this.hasBackgroundTitle.set(true);
    }

    this.hostStyle = `
      --title-color: ${titleColor};
      --title-background-color: ${titleBackgroundColor};
    `;
  }

  protected checkSmallImageArticleCard(): boolean {
    return (
      ((this.isGalleryType() || this.isVideoType() || this.isPodcastType()) &&
        (this.styleId() === this.ArticleCardType.SmallImgBlackTitleCategory || this.styleId() === this.ArticleCardType.SmallImgWhiteTitleCategory)) ||
      (!this.isRecommendationArticle() && this.styleId() === this.ArticleCardType.SmallImgBlackTitleCategory)
    );
  }

  protected setFirstIcon(): string | undefined {
    if (toBool(this.data?.isAdultsOnly)) {
      return 'adult-article';
    }

    if (!this.isMinuteToMinute() && this.data?.minuteToMinute === MinuteToMinuteState.RUNNING) {
      return 'live';
    }

    if (this.isGalleryType()) {
      return 'gallery';
    } else if (this.isPodcastType()) {
      return 'podcast';
    } else if (this.isVideoType()) {
      return 'video';
    } else if (this.isMinuteToMinute() && this.data?.minuteToMinute === MinuteToMinuteState.RUNNING) {
      return 'live';
    }

    let iconType = undefined;

    for (const key of Object.keys(ArticleCardIconTypes)) {
      const toBoolKey = toBool(this.data?.[key as keyof ArticleCard]);
      if (toBoolKey && key !== 'minuteToMinute') {
        iconType = ArticleCardIconTypes[key];
        break;
      }
    }
    return iconType;
  }
}
