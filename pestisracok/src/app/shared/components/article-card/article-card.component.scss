@use 'shared' as *;

:host {
  display: block;

  &:not(.style-Dossier) {
    .article-card-wrapper {
      &:hover {
        .article-card {
          &-title {
            text-decoration: underline;
            text-decoration-thickness: 1px;
          }
        }
      }
    }
  }

  .overlay {
    display: flex;
    justify-content: center;
    flex-direction: column;
    gap: 20px;
    align-items: center;
    backdrop-filter: blur(20px);
    background-color: rgba(var(--kui-white), 0.6);
    height: 100%;
    cursor: pointer;

    &-container {
      display: inline-block;
      container-type: inline-size;
      position: absolute;
      left: 0;
      // + 1px is needed due to Chrome rendering issues.
      width: calc(100% + 1px);
      height: calc(100% + 1px);
      top: 0;
      z-index: 1;
    }

    &-wrapper {
      height: 100%;
      width: 100%;
      position: relative;
    }

    @container (max-width: 476px) {
      backdrop-filter: blur(10px);
    }

    kesma-icon {
      width: 50%;
      display: flex;
      justify-content: center;
      color: var(--kui-black);
    }
  }

  .article-card {
    &-wrapper {
      container-type: inline-size;

      &:hover {
        .article-card {
          &-thumbnail {
            transform: scale(1.1);
          }
        }
      }
    }

    &-title {
      font-family: var(--kui-font-condensed);
      color: var(--kui-black);
      background-color: transparent;
      font-weight: 400;
      font-size: 40px;
      line-height: 44px;
      hyphens: auto;

      &-wrapper {
        display: block;
      }

      &.with-background-color {
        display: inline;
        padding: 5px 10px;
        box-decoration-break: clone;
        -webkit-box-decoration-break: clone;
        line-height: 1.55;
        color: var(--title-color);
        background-color: var(--title-background-color);
      }
    }

    &-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      align-items: center;
    }

    &-thumbnail {
      height: 100%;
      width: 100%;
      object-fit: cover;
      aspect-ratio: 16 / 9;
      transition: transform 0.3s;

      &-wrapper {
        position: relative;
        display: block;
        margin-bottom: 10px;
        overflow: hidden;

        &.has-default-background-color {
          background-color: var(--kui-gray-50);
        }
      }
    }

    &-badge {
      &-wrapper {
        position: absolute;
        top: 10px;
        left: 10px;

        kesma-icon:not(.live) {
          color: var(--kui-white);
          height: 40px;
          width: 40px;
        }

        &.live-wrapper {
          display: flex;
          align-items: center;
          justify-content: space-around;
          border-radius: 5px;
          background: var(--kui-black-o50);
          backdrop-filter: blur(3px);
          height: 32px;
          padding-inline: 8px;
          gap: 5px;

          kesma-icon.live {
            height: 17px;
            width: 16px;
          }

          span {
            color: var(--kui-white);
            text-align: center;
            font-size: 15px;
            font-weight: 800;
            line-height: normal;
            text-transform: uppercase;
          }
        }
      }
    }

    &-publish-date {
      color: var(--kui-gray-800);
      font-size: 14px;
      font-weight: 400;
      line-height: normal;
    }

    &-category {
      padding: 7px 6px;
      border-radius: 5px;
      border: 1px solid var(--kui-black-o20);
      text-align: center;
      color: var(--kui-black);
      background-color: var(--kui-white);
      font-size: 10px;
      font-weight: 800;
      line-height: 10px;
      text-transform: uppercase;
      transition: all 0.3s;
      text-decoration-color: transparent;

      &:hover {
        text-decoration-color: var(--kui-black);
        text-decoration-line: underline;
        text-decoration-style: solid;
      }
    }

    &-author {
      display: flex;
      align-items: center;
      gap: 10px;

      &:not(:first-child) {
        margin-left: -15px;
      }

      &-count,
      &-name {
        color: var(--kui-black);
        font-size: 14px;
        font-weight: 600;
        line-height: normal;
      }
    }

    &-lead {
      color: var(--kui-black);
      display: block;
      font-size: 18px;
      font-weight: 400;
      line-height: normal;

      @include media-breakpoint-down(sm) {
        font-size: 14px;
      }
    }
  }

  &.style-MainImgCategoryTitleLeadAuthorDate,
  &.style-MainImgCategoryTitleLeadAuthorDate2,
  &.style-OnlyTitle {
    .article-card {
      &-title {
        font-size: 70px;
        line-height: 100%;

        &.with-background-color {
          padding: 13px 15px;
          line-height: 1.65;
        }
      }

      &-badge {
        &-wrapper {
          top: 30px;
          left: 20px;

          kesma-icon:not(.live) {
            height: 60px;
            width: 60px;
          }
        }
      }

      &-lead {
        line-height: 140%;
      }

      &-meta {
        margin: 10px 0px;
      }

      @include container-breakpoint-down(sm) {
        &-title {
          font-size: 36px;
          line-height: 36px;

          &.with-background-color {
            padding: 5px 10px;
            line-height: 1.6;
          }
        }

        &-badge {
          &-wrapper {
            top: 15px;
            left: 15px;

            kesma-icon:not(.live) {
              height: 40px;
              width: 40px;
            }
          }
        }

        &-lead {
          line-height: 22px;
        }
      }
    }
  }

  &.style-MainImgCategoryTitleLeadAuthorDate {
    .article-card {
      &-title {
        opacity: 0.9;
        @include media-breakpoint-down(xs) {
          font-size: 30px;
        }
        &-wrapper {
          position: absolute;
          z-index: 1;
          bottom: 20px;
          left: 20px;
          right: 20px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 4;
          overflow: hidden;
          color: var(--title-color);
          @include media-breakpoint-down(md) {
            -webkit-line-clamp: 3;
          }
        }
      }

      &-thumbnail {
        @include media-breakpoint-down(md) {
          aspect-ratio: 3/2;
        }
        &-wrapper {
          margin-bottom: 15px;
        }
      }

      &-meta {
        margin: 0px 0px 10px 0px;
      }

      @include container-breakpoint-down(sm) {
        &-title {
          &-wrapper {
            bottom: 10px;
            left: 10px;
            right: 10px;
          }
        }
      }
    }
  }

  &.style-MainImgCategoryTitleLeadAuthorDate2 {
    .article-card {
      &-thumbnail {
        &-wrapper {
          margin-bottom: 20px;
          @include media-breakpoint-down(sm) {
            margin-bottom: 10px;
          }
        }
      }

      &-meta {
        margin: 15px 0px 10px 0px;
      }
    }
  }

  &.style-ImgCategoryTitleLeadAuthorDate,
  &.style-ImgCategoryTitleAuthorDate {
    .article-card {
      &-thumbnail {
        &-wrapper {
          margin-top: 10px;
        }
      }
    }
  }

  &.style-ImgCategoryTitleLeadAuthorDate,
  &.style-ImgCategoryTitleAuthorDate,
  &.style-CategoryTitleAuthorDate {
    .article-card {
      &-title {
        font-size: 36px;
        line-height: 36px;

        &.with-background-color {
          line-height: 1.6;
          padding: 5px 10px;
        }
      }

      &-lead {
        font-size: 16px;
        margin-bottom: 10px;
      }
    }
  }

  &.style-CategoryTitleAuthorDate {
    .article-card {
      &-meta {
        margin-top: 10px;
      }
    }
  }

  &.style-Dossier {
    .article-card {
      &-title {
        color: var(--kui-white);
        font-size: 30px;
        line-height: normal;
        transition: all 0.3s;
        text-decoration-line: underline;
        text-decoration-color: transparent;
        text-decoration-thickness: 5px;
        text-underline-offset: 0.2px;
        text-decoration-skip-ink: none;

        &-wrapper {
          margin-bottom: 10px;
        }

        &.underlined {
          text-decoration-color: var(--kui-black);
        }
      }

      &-author {
        &-count,
        &-name {
          color: var(--kui-white);
        }
      }

      &-publish-date {
        color: var(--kui-white);
      }
    }

    &:hover {
      .article-card {
        &-title {
          text-decoration-color: var(--kui-white);
        }
      }
    }
  }

  &.style-HighlightedImgCategoryTitleLeadAuthorDate,
  &.style-ColumnImgCategoryTitleLeadAuthorDate,
  &.style-MainCategoryTitleLeadAuthorDate {
    .article-card {
      &-meta {
        margin: 10px 0px;
      }
    }
  }

  &.style-ColumnImgCategoryTitleLeadAuthorDate {
    .article-card {
      &-thumbnail {
        &-wrapper {
          margin-bottom: 15px;
        }
      }

      @include media-breakpoint-down(sm) {
        &-title {
          font-size: 30px;
          line-height: normal;
        }
        &-thumbnail {
          &-wrapper {
            margin-bottom: 10px;
          }
        }
        &-meta {
          margin-bottom: 5px;
        }
      }
    }
  }

  &.style-HighlightedImgCategoryTitleLeadAuthorDate {
    .article-card {
      &-thumbnail {
        &-wrapper {
          margin-bottom: 15px;
        }
      }

      @include container-breakpoint-down(sm) {
        &-title {
          font-size: 36px;
          line-height: 36px;

          &.with-background-color {
            line-height: 1.6;
          }
        }

        &-thumbnail {
          &-wrapper {
            margin-bottom: 10px;
          }
        }

        &-lead {
          line-height: 22px;
        }
      }
    }
  }

  &.style-MainCategoryTitleAuthorDate {
    .article-card {
      &-meta {
        margin-top: 10px;
      }
    }
  }

  &.style-Opinion {
    .article-card {
      &-title {
        color: var(--kui-white);
        padding: 5px 10px;
        line-height: 1.55;
        background-color: var(--kui-turquoise-500);
        box-decoration-break: clone;
        -webkit-box-decoration-break: clone;
        display: inline;
      }

      &-wrapper {
        position: relative;
        border: 1px solid var(--kui-turquoise-500);
        padding: 20px;
      }

      &-lead {
        line-height: 24px;
        margin: 10px 0px 15px 0px;
      }

      &-meta {
        gap: 8px;
      }

      &-author {
        &-count,
        &-name {
          font-weight: 700;
        }
      }

      &-publish-date {
        color: var(--kui-black);
      }

      &-opinion {
        &-meta {
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          justify-content: flex-start;
          row-gap: 20px;
        }
      }

      @include media-breakpoint-down(sm) {
        &-title {
          font-size: 36px;
          line-height: 1.6;
        }

        &-opinion {
          &-meta {
            flex-direction: column-reverse;
            align-items: flex-start;
            row-gap: 15px;
          }
        }
      }
    }

    .quote-wrapper {
      position: absolute;
      bottom: 20px;
      right: 20px;

      kesma-icon {
        height: 70px;
        width: 100px;
      }
    }
  }

  &.style-PodcastLarge {
    .article-card {
      &-title {
        color: var(--kui-white);

        &-wrapper {
          margin-bottom: 12px;
        }
      }

      &-author {
        &-count,
        &-name {
          color: var(--kui-white);
        }
      }

      &-publish-date {
        color: var(--kui-white);
      }

      @include media-breakpoint-down(sm) {
        &-title {
          font-size: 30px;
          line-height: normal;

          &-wrapper {
            margin-bottom: 10px;
          }
        }
      }
    }
  }

  &.style-SmallImgWhiteTitleCategory,
  &.style-SmallImgBlackTitleCategory {
    .article-card {
      &-wrapper {
        display: flex;
        gap: 20px;
      }

      &-thumbnail {
        aspect-ratio: 1;

        &-wrapper {
          height: 90px;
          width: 90px;
          min-width: 90px;
          min-height: 90px;
          margin-bottom: unset;
        }
      }

      &-title {
        color: var(--kui-white);
        font-size: 36px;
        line-height: 36px;

        &-wrapper {
          margin-bottom: 5px;
        }
      }

      &-badge {
        &-wrapper {
          top: calc(50% - 20px);
          left: calc(50% - 20px);

          &.live {
            top: 10px;
            left: 10px;
            height: 25px;
            padding-inline: 4px;

            span {
              font-size: 12px;
            }
          }
        }
      }

      &-category {
        &.line-height-gallery-or-video-small-card {
          line-height: 11px;
        }
      }
    }
  }

  &.style-SmallImgBlackTitleCategory {
    .article-card {
      &-title,
      &-publish-date {
        color: var(--kui-black);
      }

      &-lead {
        margin-top: 10px;
      }
    }
  }

  &.style-SmallImgWhiteTitleCategory {
    .article-card {
      &-author-name,
      &-author-count,
      &-publish-date {
        color: var(--kui-white);
      }
    }
  }

  &.style-GalleryLarge,
  &.style-VideoLarge {
    .article-card {
      &-title {
        font-size: 36px;
        line-height: 1.6;
        padding: 5px 10px;
        box-decoration-break: clone;
        -webkit-box-decoration-break: clone;
        background-color: var(--kui-black);
        color: var(--kui-white);
        display: inline;

        &-wrapper {
          position: absolute;
          left: 10px;
          bottom: 10px;
          right: 10px;
          z-index: 1;
        }
      }

      @include media-breakpoint-down(sm) {
        &-title {
          font-size: 30px;
          background-color: transparent;
          padding: 0;

          &-wrapper {
            position: static;
            margin: 10px 0px;
          }
        }
      }
    }
  }

  &.style-GalleryLarge {
    .article-card {
      &-author-count,
      &-author-name,
      &-publish-date {
        color: var(--kui-white);
      }

      @include media-breakpoint-down(sm) {
        &-title {
          color: var(--kui-white);
        }
      }
    }
  }

  &.style-VideoLarge {
    .article-card {
      &-category {
        line-height: normal;
      }

      &-author-count,
      &-author-name,
      &-publish-date {
        color: var(--kui-black);
      }

      @include media-breakpoint-down(sm) {
        &-title {
          color: var(--kui-black);
        }
        &-category {
          line-height: 10px;
        }
      }
    }
  }

  &.small-article-card-mobile {
    @include media-breakpoint-down(sm) {
      .article-card {
        &-wrapper {
          display: block;
        }

        &-title {
          font-size: 30px;
          line-height: normal;

          &-wrapper {
            margin: 10px 0px;
          }
        }

        &-thumbnail {
          aspect-ratio: 16 / 9;

          &-wrapper {
            height: 100%;
            width: 100%;
            min-width: 100%;
            min-height: 100%;
            margin-bottom: unset;
          }
        }

        &-badge {
          &-wrapper {
            top: 10px;
            left: 10px;
          }
        }
      }
    }
  }

  &.style-RecommendationImgAboveTitle {
    .article-card {
      &-title {
        font-size: 36px;
        line-height: 36px;
      }

      &-meta {
        margin: 10px 0px;
      }

      &-lead {
        font-size: 16px;
      }
    }
  }

  &.style-TenArticleRecommendation {
    padding-bottom: 15px;
    border-bottom: 1px solid var(--kui-black-o10);

    .article-card {
      &-title {
        font-family: var(--kui-font-primary);
        font-size: 18px;
        font-weight: 400;
        line-height: 150%;

        &-wrapper {
          border-left: 4px solid var(--kui-orange-700);
          padding-left: 10px;
        }
      }
    }

    @include media-breakpoint-down(sm) {
      padding-bottom: 10px;
      .article-card {
        &-title {
          font-size: 16px;
        }
      }
    }
  }
}
