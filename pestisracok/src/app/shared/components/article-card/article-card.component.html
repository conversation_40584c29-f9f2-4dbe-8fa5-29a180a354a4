@if (data; as article) {
  <article class="article-card-wrapper">
    @switch (styleId()) {
      <!-- Vezető cikk 3/4 -->

      @case (ArticleCardType.MainImgCategoryTitleLeadAuthorDate) {
        <ng-container *ngTemplateOutlet="thumbnailTemplate; context: { hasBackgroundTitle: true }"></ng-container>
        <ng-container *ngTemplateOutlet="articleMetaTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="leadTemplate"></ng-container>
      }

      <!-- Nagy képes cikk 1/4 -->

      @case (ArticleCardType.MainImgCategoryTitleLeadAuthorDate2) {
        <ng-container *ngTemplateOutlet="thumbnailTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="articleMetaTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="leadTemplate"></ng-container>
      }

      <!-- Csak cím cikkártya 3/4 -->

      @case (ArticleCardType.OnlyTitle) {
        <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
      }

      <!-- Képes 1/4-es cikkártya -->

      @case (ArticleCardType.ImgCategoryTitleLeadAuthorDate) {
        <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="thumbnailTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="leadTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="articleMetaTemplate"></ng-container>
      }

      <!-- Lead nélküli 1/4es cikkártya -->

      @case (ArticleCardType.ImgCategoryTitleAuthorDate) {
        <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="thumbnailTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="articleMetaTemplate"></ng-container>
      }

      <!-- Kép nélküli 1/4es cikkártya -->

      @case (ArticleCardType.CategoryTitleAuthorDate) {
        <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="articleMetaTemplate"></ng-container>
      }

      <!-- Dosszié -->

      @case (ArticleCardType.Dossier) {
        <ng-container *ngTemplateOutlet="thumbnailTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="articleMetaTemplate; context: { isHiddenCategory: true }"></ng-container>
      }

      <!-- Kiemelt képes (vezető 1/2 méret ) cikkártya -->

      @case (ArticleCardType.HighlightedImgCategoryTitleLeadAuthorDate) {
        <ng-container *ngTemplateOutlet="thumbnailTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="articleMetaTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="leadTemplate"></ng-container>
      }

      <!-- Kép nélküli (vezető 1/2 méret ) cikkártya -->

      @case (ArticleCardType.MainCategoryTitleLeadAuthorDate) {
        <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="articleMetaTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="leadTemplate"></ng-container>
      }

      <!-- Lead nélküli (vezető 1/2 méret ) cikkártya -->

      @case (ArticleCardType.MainCategoryTitleAuthorDate) {
        <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="articleMetaTemplate"></ng-container>
      }

      <!-- Vélemény -->

      @case (ArticleCardType.Opinion) {
        <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="leadTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="opinionCardMetaTemplate"></ng-container>
        <div class="quote-wrapper">
          <kesma-icon name="quote"></kesma-icon>
        </div>
      }

      <!-- Podcast kiemelt cikk kártya fehér címmel és metával -->

      @case (ArticleCardType.PodcastLarge) {
        <ng-container *ngTemplateOutlet="thumbnailTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="articleMetaTemplate"></ng-container>
      }

      <!-- Kiemelt képes cikk galériás blokkhoz -->

      @case (ArticleCardType.GalleryLarge) {
        <ng-container *ngTemplateOutlet="thumbnailTemplate; context: { hasBackgroundTitle: true, isMobile: isMobile() }"></ng-container>
        @if (isMobile()) {
          <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
        }
        <ng-container *ngTemplateOutlet="articleMetaTemplate"></ng-container>
      }

      <!-- Kiemelt képes cikk videós blokkhoz -->

      @case (ArticleCardType.VideoLarge) {
        <ng-container *ngTemplateOutlet="thumbnailTemplate; context: { hasBackgroundTitle: true, isMobile: isMobile() }"></ng-container>
        @if (isMobile()) {
          <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
        }
        <ng-container *ngTemplateOutlet="articleMetaTemplate"></ng-container>
      }

      <!-- Képes cikkártya 1/3 (Rovat oldalra) -->

      @case (ArticleCardType.ColumnImgCategoryTitleLeadAuthorDate) {
        <ng-container *ngTemplateOutlet="thumbnailTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="articleMetaTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="leadTemplate"></ng-container>
      }

      <!-- Kép cím felett 1/4 es cikkártya például rovat oldali ajánlóra -->

      @case (ArticleCardType.RecommendationImgAboveTitle) {
        <ng-container *ngTemplateOutlet="thumbnailTemplate; context: { isHiddenBadge: true }"></ng-container>
        <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="articleMetaTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="leadTemplate"></ng-container>
      }

      @case (ArticleCardType.TenArticleRecommendation) {
        <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
      }

      @case (ArticleCardType.ExternalRecommendation) {
        <!-- TODO: External recommendation -->
      }

      <!-- Kicsi cikk kártya fehér címmel, Kicsi cikk kártya fekete címmel -->

      @default {
        <ng-container *ngTemplateOutlet="thumbnailTemplate"></ng-container>
        <div>
          <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
          <ng-container
            *ngTemplateOutlet="
              articleMetaTemplate;
              context: {
                isHiddenPublishDate: (!isMobile() && isArticleCardWithSmallImage()) || !isArticleCardWithSmallImage(),
                isHiddenAuthor: (!isMobile() && isArticleCardWithSmallImage()) || !isArticleCardWithSmallImage(),
              }
            "
          ></ng-container>
        </div>
        @if (hasLead() && isMobile()) {
          <ng-container *ngTemplateOutlet="leadTemplate"></ng-container>
        }
      }
    }
  </article>
}

<ng-template #titleTemplate let-isBackgroundTitle="isBackgroundTitle">
  @if (data?.title; as title) {
    @if (isBackgroundTitle) {
      <section class="article-card-title-wrapper">
        <h2 class="article-card-title" [class.with-background-color]="hasBackgroundTitle()">{{ title | limitString: 100 }}</h2>
      </section>
    } @else {
      <a [routerLink]="articleLink" class="article-card-title-wrapper">
        <h2 class="article-card-title" [class.underlined]="hasTitleUnderline()" [class.with-background-color]="hasBackgroundTitle()">{{ title }}</h2>
      </a>
    }
  }
</ng-template>

<ng-template #articleMetaTemplate let-isHiddenAuthor="isHiddenAuthor" let-isHiddenCategory="isHiddenCategory" let-isHiddenPublishDate="isHiddenPublishDate">
  <div class="article-card-meta">
    @if (!isHiddenAuthor) {
      <ng-container *ngTemplateOutlet="authorTemplate"></ng-container>
    }
    @if (!isHiddenCategory) {
      <ng-container *ngTemplateOutlet="categoryTemplate"></ng-container>
    }
    @if (!isHiddenPublishDate) {
      <ng-container *ngTemplateOutlet="publishDateTemplate"></ng-container>
    }
  </div>
</ng-template>

<ng-template #leadTemplate>
  @if (data?.lead; as lead) {
    <a [routerLink]="articleLink" class="article-card-lead">
      {{ lead }}
    </a>
  }
</ng-template>

<ng-template #thumbnailTemplate let-hasBackgroundTitle="hasBackgroundTitle" let-isHiddenBadge="isHiddenBadge" let-isMobile="isMobile">
  <div class="overlay-wrapper">
    @if (showAdultLayer()) {
      <div class="overlay-container">
        <a [routerLink]="articleLink" class="overlay">
          <kesma-icon name="adult"></kesma-icon>
        </a>
      </div>
    }
    <a [routerLink]="articleLink" class="article-card-thumbnail-wrapper" [class.has-default-background-color]="!hasThumbnail()">
      @if (hasBackgroundTitle && !isMobile) {
        <ng-container *ngTemplateOutlet="titleTemplate; context: { isBackgroundTitle: true }"></ng-container>
      }

      <img
        withFocusPoint
        [data]="data?.thumbnailFocusedImages"
        [displayedUrl]="displayedThumbnailUrl"
        [displayedAspectRatio]="{ desktop: '16:9' }"
        [alt]="data?.thumbnail?.alt || ''"
        class="article-card-thumbnail"
        loading="lazy"
      />
      @if (!isHiddenBadge) {
        <ng-container *ngTemplateOutlet="badgeTemplate"></ng-container>
      }
    </a>
  </div>
</ng-template>

<ng-template #categoryTemplate>
  @if (data?.category || data?.columnSlug) {
    @if (data?.category) {
      <a
        [routerLink]="categoryUrl"
        class="article-card-category"
        [class.line-height-gallery-or-video-small-card]="
          !isMobile() &&
          (isGalleryType() || isVideoType()) &&
          (styleId() === ArticleCardType.SmallImgBlackTitleCategory || styleId() === ArticleCardType.SmallImgWhiteTitleCategory)
        "
      >
        {{ data?.category?.name }}
      </a>
    } @else {
      <a
        [routerLink]="['/', 'rovat', data?.columnSlug ?? '']"
        class="article-card-category"
        [class.line-height-gallery-or-video-small-card]="
          !isMobile() &&
          (isGalleryType() || isVideoType()) &&
          (styleId() === ArticleCardType.SmallImgBlackTitleCategory || styleId() === ArticleCardType.SmallImgWhiteTitleCategory)
        "
      >
        {{ data?.columnTitle }}
      </a>
    }
  }
</ng-template>

<ng-template #publishDateTemplate>
  @if (publishDate) {
    <span class="article-card-publish-date">{{ publishDate | dfnsFormat: publishDateFormat() : { locale: hu } }}</span>
  }
</ng-template>

<ng-template #authorTemplate>
  @if (data?.publicAuthorM2M?.length && data?.publicAuthorM2M; as authors) {
    @for (author of authors; track author.id; let count = $count; let index = $index; let last = $last) {
      @if (count === 1) {
        <ng-container *ngTemplateOutlet="authorThumbnailTemplate; context: { author, hasFullName: true }"></ng-container>
      } @else {
        @if (index < 5) {
          <ng-container *ngTemplateOutlet="authorThumbnailTemplate; context: { author, hasFullName: false }"></ng-container>
        }
        @if (count <= 5 && last) {
          <span class="article-card-author-count">{{ count }} szerző</span>
        } @else if (count > 5 && index === 4) {
          <span class="article-card-author-count">+ {{ count - 5 }} szerző</span>
        }
      }
    }
  }
</ng-template>

<ng-template #authorThumbnailTemplate let-author="author" let-hasFullName="hasFullName">
  <a [routerLink]="articleLink" class="article-card-author">
    <app-avatar-initials [name]="author?.fullName" [size]="30" [avatar]="author?.avatar?.thumbnailUrl ?? author?.avatar"></app-avatar-initials>
    @if (hasFullName) {
      <span class="article-card-author-name">{{ author?.fullName }}</span>
    }
  </a>
</ng-template>

<ng-template #badgeTemplate>
  @if (articleFirstIcon(); as icon) {
    <div class="article-card-badge-wrapper" [class.live-wrapper]="icon === 'live'">
      <kesma-icon [name]="icon" [class]="icon"></kesma-icon>
      @if (icon === 'live') {
        <span>Élő</span>
      }
    </div>
  }
</ng-template>

<ng-template #opinionCardMetaTemplate>
  <div class="article-card-opinion-meta">
    <ng-container *ngTemplateOutlet="articleMetaTemplate; context: { isHiddenCategory: true }"></ng-container>
  </div>
</ng-template>
