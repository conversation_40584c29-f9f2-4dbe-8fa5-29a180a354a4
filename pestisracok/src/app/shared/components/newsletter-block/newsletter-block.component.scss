@use 'shared' as *;

.newsletter-block {
  border: 1px solid var(--kui-black-o10);
  background: var(--kui-white);
  box-shadow: 0 2px 4px 0 var(--kui-gray-700-o10);
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  width: 100%;

  @include media-breakpoint-down(md) {
    flex-direction: column;

    app-simple-button {
      width: 100%;
    }
  }

  &-text {
    color: var(--kui-black);
    font-family: var(--kui-font-condensed);
    font-size: 36px;
    font-weight: 400;
    line-height: 36px;
    flex: 1;

    @include media-breakpoint-down(md) {
      text-align: center;
    }
  }

  &.small {
    flex-direction: column;

    .newsletter-block-text {
      text-align: center;
    }
  }
}

app-simple-button {
  &::ng-deep .btn-primary {
    &:not(:disabled):hover {
      background-color: var(--kui-turquoise-500) !important;
    }
  }
}
