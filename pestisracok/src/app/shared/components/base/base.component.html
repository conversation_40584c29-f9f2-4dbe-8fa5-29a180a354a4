@if (adverts()?.mobile?.layer; as ad) {
  <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
}
@if (adverts()?.desktop?.layer; as ad) {
  <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
}

@if (adverts()?.mobile?.interstitial; as ad) {
  <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
}
@if (adverts()?.desktop?.interstitial; as ad) {
  <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
}

@if (adverts()?.desktop?.technikai_1; as ad) {
  <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
}
@if (adverts()?.desktop?.technikai_2; as ad) {
  <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
}
@if (adverts()?.desktop?.technikai_3; as ad) {
  <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
}
@if (adverts()?.desktop?.technikai_4; as ad) {
  <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
}
@if (adverts()?.mobile?.technikai_1; as ad) {
  <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
}
@if (adverts()?.mobile?.technikai_2; as ad) {
  <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
}
@if (adverts()?.mobile?.technikai_3; as ad) {
  <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
}
@if (adverts()?.mobile?.technikai_4; as ad) {
  <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
}

<app-header class="pestisracok-header" [mainMenu]="mainMenu()"></app-header>

@if (!isHomePage() && !isArticleUrl() && adverts()?.desktop?.leaderboard_1; as ad) {
  <div class="leaderboard-1-ad">
    <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
  </div>
}

<div class="content-wrap" [class.content-wrap-full-width]="(isFullWidth$ | async) === true">
  <router-outlet></router-outlet>
  @if (showRecommendations() && !adultService.showAdultLayer()) {
    <section class="recommendations-wrapper">
      <div class="wrapper">
        <div class="title">Ajánljuk még</div>
        <div class="recommendations-internal">
          @for (article of recommendations()?.['internal']; track $any(article).id; let index = $index; let last = $last) {
            @if (!last || (last && isMobile())) {
              @if (index < (isMobile() ? 1 : 4)) {
                <app-article-card [data]="article" [styleID]="ArticleCardType.ColumnImgCategoryTitleLeadAuthorDate"></app-article-card>
              } @else {
                <app-article-card
                  [data]="article"
                  [isRecommendationArticle]="true"
                  [hasLead]="false"
                  [styleID]="ArticleCardType.SmallImgBlackTitleCategory"
                ></app-article-card>
              }
              @if (index === 3) {
                <div class="full-row">
                  @if (adverts()?.desktop?.roadblock_3; as ad) {
                    <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
                  }
                  @if (adverts()?.mobile?.mobilrectangle_3; as ad) {
                    <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
                  }
                </div>
              }
            }
          }
        </div>
      </div>
    </section>
  }
</div>

@if (adverts()?.desktop?.leaderboard_footer; as ad) {
  <kesma-advertisement-adocean
    [style]="{
      margin: 'var(--ad-margin)',
    }"
    [ad]="ad"
  ></kesma-advertisement-adocean>
}

@if (adverts()?.mobile?.mobilrectangle_footer; as ad) {
  <kesma-advertisement-adocean
    [style]="{
      margin: 'var(--ad-margin)',
    }"
    [ad]="ad"
  ></kesma-advertisement-adocean>
}

<app-footer [footerMenu]="footer()" [mediaworksFooter]="mediaWorksFooter() || []" (openCookieSettings)="openCookieSettings()"></app-footer>
