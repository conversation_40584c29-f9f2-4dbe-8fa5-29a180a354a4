<footer>
  <section class="menu-section">
    <div class="menu-section-inner">
      <div class="menu-section-left">
        <div class="menu-section-left-top">
          <a [routerLink]="['/']">
            <img class="menu-section-logo" alt="PestiSrácok" src="/assets/images/logo.svg" loading="lazy" />
          </a>
          <p class="font-normal-400">
            Ahol alacsonyan szállnak a sallerek. És ahol nincs íróasztalfiók. Mert ami o<PERSON>, az itt megjelenik. Írásaink a közmegegyezéses hazai
            sajtóetika és a Médiaalkotmány szellemisége szerint készülnek. Forrásainkat minden körülmények között megóvjuk, szivárogtasson bátran! Szerzőink
            neve olykor "írói név".
          </p>
        </div>
        <ng-container *ngTemplateOutlet="sloganAndSocial; context: { hideOnMobile: true }"></ng-container>
      </div>
      <div class="menu-section-right">
        @for (menuItem of footerMenu(); track menuItem.id) {
          <div class="menu-section-right-wrapper">
            <ng-container *ngTemplateOutlet="menuItemTemplate; context: { item: menuItem, isFirstElement: true }"></ng-container>
          </div>
        }
      </div>
      <ng-container *ngTemplateOutlet="sloganAndSocial; context: { hideOnMobile: false }"></ng-container>
    </div>
  </section>
  <section class="mw-footer-section">
    <div class="mw-footer-section-inner">
      <kesma-mediaworks-footer-compact [data]="mediaworksFooter()"></kesma-mediaworks-footer-compact>
    </div>
  </section>
</footer>

<!-- TEMPLATES -->

<ng-template #sloganAndSocial let-hideOnMobile="hideOnMobile">
  <div class="menu-section-left-bottom" [class.show-on-desktop]="!hideOnMobile" [class.hide-on-mobile]="hideOnMobile">
    <span class="slogan">*Kell még valamit mondanom Ildikó?</span>
    <span class="font-normal-400 slogan-said">Az őszödi beszéd utolsó mondata.</span>
    <ng-container *ngTemplateOutlet="social"></ng-container>
  </div>
</ng-template>

<ng-template #social>
  <div class="social">
    <a href="https://videa.hu/csatornak/pestisracok.hu-376" target="_blank">
      <kesma-icon class="social-videa" name="social-videa"></kesma-icon>
    </a>
    <a href="https://www.facebook.com/pestisracok.pstv" target="_blank">
      <kesma-icon name="social-facebook"></kesma-icon>
    </a>
    <a href="https://x.com/pestisracok" target="_blank">
      <kesma-icon name="social-x"></kesma-icon>
    </a>
    <a href="https://pestisracok.hu/publicapi/hu/rss/pesti_sracok/articles" target="_blank">
      <kesma-icon class="social-rss" name="social-rss"></kesma-icon>
    </a>
    <a href="https://open.spotify.com/show/7oQyn8CoR18D2PQD2hXz5m" target="_blank">
      <kesma-icon name="social-spotify"></kesma-icon>
    </a>
    <a href="https://podcasts.apple.com/us/podcast/pestisracok-hu/id1684256429" target="_blank">
      <kesma-icon class="social-mac-podcast" name="social-mac-podcast"></kesma-icon>
    </a>
  </div>
</ng-template>

<ng-template #menuItemTemplate let-item="item" let-isFirstElement="isFirstElement">
  @if (item?.hasSubItems) {
    <span class="menu-item-link" [class.title]="isFirstElement">{{ item?.title }}</span>
    <ng-container *ngTemplateOutlet="submenuTemplate; context: { children: item?.children }"></ng-container>
  } @else {
    @if (item?.target === '_blank' || item?.isCustomUrl) {
      <a [href]="item?.link" [target]="item?.target" class="menu-item-link" [class.title]="isFirstElement">{{ item?.title }}</a>
    } @else {
      @if (item?.id === 'cookie-settings') {
        <a (click)="openCookieSettings.emit()" class="menu-item-link cookie" [class.title]="isFirstElement">Süti beállítások</a>
      } @else {
        <a [routerLink]="item?.link" [target]="item?.target" class="menu-item-link" [class.title]="isFirstElement">{{ item?.title }}</a>
      }
    }
  }
</ng-template>

<ng-template #submenuTemplate let-children="children">
  @if (!children?.length) {
    return;
  }
  <div class="submenu">
    <div class="submenu-wrapper">
      @for (child of children | slice: 0 : menuBreakpoints()?.[0]; track $any(child)?.id) {
        <ng-container *ngTemplateOutlet="menuItemTemplate; context: { item: child }"></ng-container>
      }
    </div>

    @if (children?.length > menuBreakpoints()?.[0]) {
      <div class="submenu-wrapper">
        @for (child of children | slice: menuBreakpoints()?.[0] : menuBreakpoints()?.[1]; track $any(child).id) {
          <ng-container *ngTemplateOutlet="menuItemTemplate; context: { item: child }"></ng-container>
        }
      </div>
    }

    @if (children?.length > menuBreakpoints()?.[1]) {
      <div class="submenu-wrapper">
        @for (child of children | slice: menuBreakpoints()?.[1] : menuBreakpoints()?.[2]; track $any(child).id) {
          <ng-container *ngTemplateOutlet="menuItemTemplate; context: { item: child }"></ng-container>
        }
      </div>
    }
  </div>
</ng-template>
