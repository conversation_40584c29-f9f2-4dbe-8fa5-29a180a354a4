<header>
  <div class="mobile-header">
    <div class="left">
      @if (showMobileMenu) {
        <kesma-icon name="close" [size]="16" (click)="closeMobileMenu()" class="close-icon"></kesma-icon>
      } @else {
        <kesma-icon name="hamburger" [size]="32" (click)="openMobileMenu()" class="hamburger-icon"></kesma-icon>
      }
      <a routerLink="/"><img src="/assets/images/logo-2.svg" alt="Pesti Srácok" /></a>
    </div>

    <div class="social-wrapper">
      <a [routerLink]="isUserLoggedIn() ? '/profil' : '/bejelentkezes'">
        <kesma-icon name="user" [size]="28" class="login-icon" [class.logged-in]="isUserLoggedIn()"></kesma-icon>
      </a>
      <a (click)="openSearchOverlay()" title="Keresés">
        <kesma-icon name="search" [size]="32"></kesma-icon>
      </a>
    </div>
  </div>

  @if (showMobileMenu) {
    <app-mobile-menu-overlay [mainMenu]="mainMenu()" (closeMenu)="closeMobileMenu()"></app-mobile-menu-overlay>
  }
  <div class="desktop-header">
    <a routerLink="/" class="logo-wrapper">
      <img src="/assets/images/logo.svg" alt="Pesti Srácok" class="logo" />
    </a>

    <div class="menu">
      @for (menuItem of mainMenu(); track menuItem.id; let index = $index) {
        @if (index < visibleMenuItemsCount()) {
          <ng-container *ngTemplateOutlet="menuItemTemplate; context: { item: menuItem }"></ng-container>
        }
      }
    </div>

    <a routerLink="/pstv-podcast" class="podcast-wrapper">
      <span>Podcast</span>
      <kesma-icon name="mic" [size]="24"></kesma-icon>
    </a>

    <div class="icon-menu">
      <div class="toolbox">
        <a (click)="openSearchOverlay()" title="Keresés">
          <kesma-icon name="search" [size]="32"></kesma-icon>
        </a>
        @if (isUserLoggedIn()) {
          <div class="menu-item parent">
            <div class="menu-item-link">
              <a routerLink="/profil">
                <kesma-icon name="user" [size]="28" class="login-icon logged-in"></kesma-icon>
              </a>
            </div>

            <ng-container *ngTemplateOutlet="submenuOverlayTemplate; context: { children: userMenu, hasIconParent: true }"></ng-container>
          </div>
        } @else {
          <a routerLink="/bejelentkezes">
            <kesma-icon name="user" [size]="28" class="login-icon"></kesma-icon>
          </a>
        }
      </div>

      <div class="social">
        <a href="https://videa.hu/csatornak/pestisracok.hu-376" target="_blank">
          <kesma-icon class="social-videa" name="social-videa"></kesma-icon>
        </a>
        <a href="https://www.facebook.com/pestisracok.pstv" target="_blank">
          <kesma-icon name="social-facebook"></kesma-icon>
        </a>
        <a href="https://x.com/pestisracok" target="_blank">
          <kesma-icon name="social-x"></kesma-icon>
        </a>
        <a href="https://pestisracok.hu/publicapi/hu/rss/pesti_sracok/articles" target="_blank">
          <kesma-icon class="social-rss" name="social-rss"></kesma-icon>
        </a>
      </div>

      <kesma-icon name="hamburger" [size]="32" class="hamburger-menu" (click)="openSideMenu()"></kesma-icon>
    </div>
  </div>

  @if (showSideMenu) {
    <div class="side-menu" (clickOutside)="closeSideMenu()">
      <div class="side-menu-close">
        <kesma-icon name="close" [size]="16" (click)="closeSideMenu()" class="close-icon"></kesma-icon>
      </div>
      <div class="side-menu-items">
        @for (menuItem of mainMenu().slice(visibleMenuItemsCount()); track menuItem.id; let index = $index) {
          <ng-container *ngTemplateOutlet="sideMenuItemTemplate; context: { item: menuItem }"></ng-container>
        }
      </div>
    </div>
  }
</header>

<ng-template #menuItemTemplate let-item="item">
  <div class="menu-item" [class.parent]="item?.hasSubItems">
    @if (item?.hasSubItems) {
      <a class="menu-item-link">
        <span>{{ item.title }}</span>
        <kesma-icon name="caret-down" [size]="10" [height]="5" class="caret-icon"></kesma-icon>
      </a>
      <ng-container *ngTemplateOutlet="submenuOverlayTemplate; context: { children: item.children }"></ng-container>
    } @else {
      @if (item.target === '_blank' || item.isCustomUrl) {
        <a [href]="item.link" [target]="item.target" class="menu-item-link">{{ item.title }}</a>
      } @else {
        <a [routerLink]="item.link" [target]="item.target" class="menu-item-link">{{ item.title }}</a>
      }
    }
  </div>
</ng-template>

<ng-template #sideMenuItemTemplate let-item="item">
  <div class="menu-item" [class.parent]="item?.hasSubItems">
    @if (item?.hasSubItems) {
      <div
        class="menu-item-link"
        [class.active]="submenu.style.display === 'block'"
        (click)="submenu.style.display === 'none' ? (submenu.style.display = 'block') : (submenu.style.display = 'none')"
      >
        <span>{{ item.title }}</span>
        <kesma-icon name="caret-down" [size]="12" [height]="7"></kesma-icon>
      </div>
      <div class="submenus" [style.display]="'none'" #submenu>
        @for (child of item.children; track child.id) {
          <ng-container *ngTemplateOutlet="sideMenuItemTemplate; context: { item: child }"></ng-container>
        }
      </div>
    } @else {
      @if (item.target === '_blank' || item.isCustomUrl) {
        <a [href]="item.link" [target]="item.target" class="menu-item-link">{{ item.title }}</a>
      } @else {
        <a [routerLink]="item.link" [target]="item.target" class="menu-item-link">{{ item.title }}</a>
      }
    }
  </div>
</ng-template>

<ng-template #submenuOverlayTemplate let-children="children" let-hasIconParent="hasIconParent">
  <div class="submenu-overlay" [class.icon-parent]="hasIconParent">
    <div class="submenu-wrapper">
      @for (child of children | slice: 0 : 6; track $any(child).id) {
        <ng-container *ngTemplateOutlet="menuItemTemplate; context: { item: child }"></ng-container>
      }
    </div>

    @if (children?.length > 6) {
      <div class="submenu-wrapper">
        @for (child of children | slice: 6 : 12; track $any(child).id) {
          <ng-container *ngTemplateOutlet="menuItemTemplate; context: { item: child }"></ng-container>
        }
      </div>
    }

    @if (children?.length > 12) {
      <div class="submenu-wrapper">
        @for (child of children | slice: 12 : 18; track $any(child).id) {
          <ng-container *ngTemplateOutlet="menuItemTemplate; context: { item: child }"></ng-container>
        }
      </div>
    }

    @if (children?.length > 18) {
      <div class="submenu-wrapper">
        @for (child of children | slice: 18 : 24; track $any(child).id) {
          <ng-container *ngTemplateOutlet="menuItemTemplate; context: { item: child }"></ng-container>
        }
      </div>
    }
  </div>
</ng-template>
