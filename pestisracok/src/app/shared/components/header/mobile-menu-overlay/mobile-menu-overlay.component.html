<div class="mobile-menu-top">
  <div class="social-wrapper">
    <a href="https://videa.hu/csatornak/pestisracok.hu-376" target="_blank">
      <kesma-icon class="social-videa" name="social-videa"></kesma-icon>
    </a>
    <a href="https://www.facebook.com/pestisracok.pstv" target="_blank">
      <kesma-icon name="social-facebook"></kesma-icon>
    </a>
    <a href="https://x.com/pestisracok" target="_blank">
      <kesma-icon name="social-x"></kesma-icon>
    </a>
    <a href="https://pestisracok.hu/publicapi/hu/rss/pesti_sracok/articles" target="_blank">
      <kesma-icon class="social-rss" name="social-rss"></kesma-icon>
    </a>
  </div>

  <div class="podcast-wrapper-container">
    <a routerLink="/pstv-podcast" class="podcast-wrapper">
      <span>Podcast</span>
      <kesma-icon name="mic" [size]="24"></kesma-icon>
    </a>
  </div>
</div>

<ul class="mobile-menu">
  @for (menuItem of mainMenu(); track menuItem.id; let index = $index) {
    <ng-container *ngTemplateOutlet="menuItemTemplate; context: { item: menuItem }"></ng-container>
  }
</ul>

<div class="user-buttons">
  @if (isUserLoggedIn()) {
    <app-simple-button [routerLink]="['/kijelentkezes']" [wide]="true" (click)="closeMenu.emit()">Kijelentkezés</app-simple-button>
  } @else {
    <app-simple-button [routerLink]="['/bejelentkezes']" [wide]="true" (click)="closeMenu.emit()">Bejelentkezés</app-simple-button>
  }
</div>

<ng-template #menuItemTemplate let-item="item">
  <div class="menu-item" [class.parent]="item?.hasSubItems">
    @if (item?.hasSubItems) {
      <div
        class="menu-item-link"
        [class.active]="submenu.style.display === 'block'"
        (click)="submenu.style.display === 'none' ? (submenu.style.display = 'block') : (submenu.style.display = 'none')"
      >
        <span>{{ item.title }}</span>
        <kesma-icon name="caret-down" [size]="12" [height]="7"></kesma-icon>
      </div>
      <div class="submenus" [style.display]="'none'" #submenu>
        @for (child of item.children; track child.id) {
          <ng-container *ngTemplateOutlet="menuItemTemplate; context: { item: child }"></ng-container>
        }
      </div>
    } @else {
      @if (item.target === '_blank' || item.isCustomUrl) {
        <a [href]="item.link" [target]="item.target" class="menu-item-link">{{ item.title }}</a>
      } @else {
        <a [routerLink]="item.link" [target]="item.target" class="menu-item-link">{{ item.title }}</a>
      }
    }
  </div>
</ng-template>
