@use 'shared' as *;

:host {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 90px;
  background: var(--kui-blue-900);
  color: var(--kui-white);
  position: sticky;
  top: 0;
  width: 100%;
  z-index: 999;

  a {
    cursor: pointer;
  }

  @include media-breakpoint-down(md) {
    height: 70px;
  }

  header {
    max-width: 1400px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    kesma-icon {
      transition: 0.3s;
    }

    .desktop-header {
      display: none;
      max-width: 1400px;
      width: 100%;
      align-items: center;
      justify-content: space-between;

      @include media-breakpoint-up(lg) {
        display: flex;
      }
    }

    .logo-wrapper {
      flex: 0 0 auto;
      margin-right: 30px;
    }

    .podcast-wrapper {
      margin: 0 30px;
      display: flex;
      padding: 5px 10px 5px 15px;
      align-items: center;
      gap: 8px;
      border-radius: 999px;
      background: var(--kui-turquoise-500);
      font-family: var(--kui-font-condensed);
      color: var(--kui-white);
      font-size: 20px;
      font-weight: 400;
      line-height: normal;
      text-transform: uppercase;
      transition: 0.3s;

      &:hover {
        background: var(--kui-orange-700);
      }
    }
  }

  .menu {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 30px;

    &-item {
      display: flex;
      align-items: center;
      gap: 8px;
      position: relative;

      &.parent {
        &:after {
          content: '';
          position: absolute;
          bottom: -15px;
          height: 15px;
          width: 100%;
        }

        &:hover {
          & > .submenu-overlay {
            display: flex;
            z-index: 1;
          }
        }
      }

      &:hover {
        & > .menu-item-link {
          text-decoration-line: underline;
          text-decoration-thickness: 1px;

          .caret-icon {
            transform: rotate(-180deg);
          }
        }
      }

      &-link {
        font-family: var(--kui-font-condensed);
        color: var(--kui-white);
        font-size: 20px;
        font-weight: 400;
        line-height: normal;
        text-transform: uppercase;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: 0.3s;
      }
    }
  }

  .submenu {
    &-overlay {
      position: absolute;
      top: 34px;
      color: var(--kui-blue-900);
      background: var(--kui-gray-50);
      padding: 20px;
      box-shadow: 0 0 6px 0 var(--kui-black-o12);
      border: 1px solid rgba(255, 255, 255, 0.3);
      display: none;
      gap: 15px;

      .menu-item-link {
        color: var(--kui-blue-900);
      }

      &:hover {
        display: flex;
      }
    }

    &-wrapper {
      display: flex;
      flex-direction: column;
      gap: 16px;
      width: 180px;
    }
  }

  .icon-menu {
    display: flex;
    align-items: center;
  }

  .toolbox {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-right: 30px;

    kesma-icon {
      color: var(--kui-white);

      &:hover {
        color: var(--kui-turquoise-500);
      }

      &.login-icon {
        &.logged-in {
          color: var(--kui-orange-700);

          &:hover {
            color: var(--kui-white);
          }
        }
      }
    }
  }

  .social {
    display: flex;
    align-items: center;
    gap: 15px;

    a {
      height: 32px;
      width: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid var(--kui-white-o30);
      border-radius: 50%;
      transition: 0.3s;

      &:hover {
        border-color: var(--kui-turquoise-500);
      }

      kesma-icon {
        height: 16px;
        width: 16px;
        color: var(--kui-white);
        transition: 0.3s;

        &.social-rss {
          height: 18px;
          width: 18px;
        }

        &.social-videa {
          height: 24px;
          width: 24px;
        }
      }

      &:hover kesma-icon {
        color: var(--kui-turquoise-500);
      }
    }
  }

  .hamburger-menu {
    margin-left: 60px;
    transition: 0.3s;
    cursor: pointer;

    &:hover {
      color: var(--kui-turquoise-500);
    }
  }

  .mobile-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    @include media-breakpoint-up(lg) {
      display: none;
    }

    .left {
      display: flex;
      align-items: center;
      gap: 30px;

      .hamburger-icon {
        margin-left: -3px;
      }

      .close-icon {
        margin: 0 13px 0 0;
      }
    }

    .social-wrapper {
      display: flex;
      align-items: center;
      gap: 15px;
    }

    kesma-icon {
      color: var(--kui-white);
      transition: 0.3s;
      cursor: pointer;

      &:hover {
        color: var(--kui-turquoise-500);
      }

      &.logged-in {
        color: var(--kui-orange-700);

        &:hover {
          color: var(--kui-white);
        }
      }
    }
  }

  .side-menu {
    position: fixed;
    width: 308px;
    height: 100%;
    padding: 30px;
    background-color: var(--kui-blue-900);
    right: 0;
    top: 0;

    &-close {
      display: flex;
      justify-content: flex-end;
      padding: 8px 0 30px;

      kesma-icon {
        transition: 0.3s;
        cursor: pointer;

        &:hover {
          color: var(--kui-turquoise-500);
        }
      }
    }

    &-items {
      overflow-y: auto;
      height: calc(100% - 30px - 16px);
    }

    .menu-item {
      display: block;

      &:hover > .menu-item-link {
        color: var(--kui-white);
      }

      &-link {
        width: 100%;
        padding: 15px 0;
        border-bottom: 1px solid var(--kui-white-o10);
        display: flex;
        align-items: center;
        justify-content: space-between;
        transition: 0.3s;

        &:hover {
          text-decoration-line: underline;
          text-decoration-thickness: 1px;
        }

        kesma-icon {
          color: var(--kui-white-o50);
          transition: 0.3s;
        }

        &.active {
          kesma-icon {
            transform: rotate(-180deg);
          }
        }
      }
    }

    .submenus {
      padding: 5px 0 5px 15px;
      border-bottom: 1px solid var(--kui-white-o10);

      .menu-item-link {
        border: none;
        font-family: var(--kui-font-primary);
        font-size: 16px;
        font-weight: 400;
        line-height: 136%;
        text-transform: initial;
        padding: 10px 0;
      }
    }
  }
}
