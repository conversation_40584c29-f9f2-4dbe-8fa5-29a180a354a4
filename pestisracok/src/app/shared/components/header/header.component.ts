import { AfterViewInit, ChangeDetectionStrategy, Component, DestroyRef, HostBinding, inject, input } from '@angular/core';
import { ClickOutsideDirective, IconComponent, SimplifiedMenuItem } from '@trendency/kesma-ui';
import { UtilService } from '@trendency/kesma-core';
import { fromEvent, of } from 'rxjs';
import { map, startWith } from 'rxjs/operators';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { DOCUMENT, NgTemplateOutlet, SlicePipe } from '@angular/common';
import { MobileMenuOverlayComponent } from './mobile-menu-overlay/mobile-menu-overlay.component';
import { AuthService, SearchOverlayService } from '../../services';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [MobileMenuOverlayComponent, IconComponent, RouterLink, SlicePipe, NgTemplateOutlet, ClickOutsideDirective],
})
export class HeaderComponent implements AfterViewInit {
  readonly mainMenu = input<SimplifiedMenuItem[]>([]);
  @HostBinding('style.padding.px') padding = '0 15';

  private readonly utils = inject(UtilService);
  private readonly destroyRef = inject(DestroyRef);
  private readonly document = inject(DOCUMENT);
  private readonly searchOverlayService = inject(SearchOverlayService);

  isUserLoggedIn = inject(AuthService).currentUser;

  showMobileMenu = false;
  showSideMenu = false;

  readonly userMenu: SimplifiedMenuItem[] = [
    {
      id: '1',
      title: 'Profil oldal',
      link: '/profil',
      target: '_self',
    },
    {
      id: '2',
      title: 'Kijelentkezés',
      link: '/kijelentkezes',
      target: '_self',
    },
  ];

  visibleMenuItemsCount = toSignal(
    this.utils.isBrowser()
      ? fromEvent(window, 'resize').pipe(
          map(() => window.innerWidth),
          startWith(window.innerWidth),
          map((width: number) => {
            const paddingX = Number(this.padding.split(' ')[1]);
            if (width < 1080 + paddingX) return 2;
            if (width < 1230 + paddingX) return 3;
            if (width < 1440 + paddingX) return 4;
            return 5;
          }),
          takeUntilDestroyed(this.destroyRef)
        )
      : of(5),
    { initialValue: 5 }
  );

  ngAfterViewInit(): void {
    if (!this.utils.isBrowser()) return;

    const aTags = this.document.querySelectorAll('a');
    aTags.forEach((aTag) => {
      aTag.addEventListener('click', () => {
        this.closeMobileMenu();
      });
    });
  }

  openMobileMenu(): void {
    this.showMobileMenu = true;
  }

  closeMobileMenu(): void {
    this.showMobileMenu = false;
  }

  openSideMenu(): void {
    this.showSideMenu = true;
  }

  closeSideMenu(): void {
    this.showSideMenu = false;
  }

  openSearchOverlay(): void {
    this.searchOverlayService.open();
  }
}
