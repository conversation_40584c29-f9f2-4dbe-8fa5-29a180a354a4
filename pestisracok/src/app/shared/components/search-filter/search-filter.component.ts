import { ChangeDetectionStrategy, Component, effect, EventEmitter, inject, Output, signal } from '@angular/core';
import { SearchSelectComponent } from '../search-select/search-select.component';
import { toSignal } from '@angular/core/rxjs-interop';
import { map } from 'rxjs/operators';
import { contentTypeFilters, publishDateFilters } from '../../utils';
import { ActivatedRoute, Router } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ApiService } from '../../services';
import { IconComponent, PrimaryColumn } from '@trendency/kesma-ui';
import { SimpleButtonComponent } from '../simple-button/simple-button.component';
import { LabelValue } from '../../definitions';

@Component({
  selector: 'app-search-filter',
  imports: [SearchSelectComponent, ReactiveFormsModule, FormsModule, IconComponent, SimpleButtonComponent],
  templateUrl: './search-filter.component.html',
  styleUrl: './search-filter.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SearchFilterComponent {
  @Output() filterEvent = new EventEmitter<Record<string, string>>();

  private readonly apiService = inject(ApiService);
  private readonly router = inject(Router);
  private readonly route = inject(ActivatedRoute);

  readonly publishDateFilters = publishDateFilters;
  readonly contentTypeFilters = contentTypeFilters;

  readonly categorySource = toSignal(this.apiService.getParentColumns().pipe(map(({ data }) => data)), { initialValue: [] });

  readonly globalFilter = signal<string>('');
  readonly selectedPublishDate = signal<LabelValue<string> | undefined>(undefined);
  readonly selectedContentType = signal<LabelValue<string> | undefined>(undefined);
  readonly selectedColumnSlug = signal<PrimaryColumn | undefined>(undefined);

  readonly queryParams = toSignal(this.route.queryParams);

  searchFilter: Record<string, string> = {
    from_date: '',
    'columnSlugs[]': '',
    'content_types[]': '',
  };

  constructor() {
    effect(() => {
      this.determineInitialValues();
    });
  }

  determineInitialValues(): void {
    Object.entries(this.queryParams() ?? {}).forEach(([paramKey, paramValue]) => {
      switch (paramKey) {
        case 'global_filter':
          this.globalFilter.update(() => paramValue);
          break;
        case 'from_date':
          this.selectedPublishDate.update(() => publishDateFilters.find((filter) => filter.value === paramValue));
          this.searchFilter['from_date'] = paramValue;
          break;
        case 'content_types[]':
          this.selectedContentType.update(() => contentTypeFilters.find((filter) => filter.value === paramValue));
          this.searchFilter['content_types[]'] = paramValue;
          break;
        case 'columnSlugs[]':
          this.selectedColumnSlug.update(() => this.categorySource().find((filter) => filter.slug === paramValue));
          this.searchFilter['columnSlugs[]'] = paramValue;
          break;
      }
    });
  }

  clearSearchTerm(): void {
    this.globalFilter.update(() => '');
  }

  onSubmit(): void {
    this.setRouteParams();
  }

  private setRouteParams(): void {
    const queryParams: Record<string, string> = {
      global_filter: this.globalFilter(),
      ...this.searchFilter,
    };

    // Remove empty props
    Object.entries(queryParams).forEach(([key, value]) => {
      if (!value) delete queryParams[key];
    });

    // Remove page prop
    Object.entries(queryParams).forEach(([key]) => {
      if (key === 'page') delete queryParams[key];
    });

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams,
    });
  }
}
