import { ChangeDetectionStrategy, Component, HostBinding, input, InputSignal, OnInit } from '@angular/core';

@Component({
  selector: 'app-podcast-app-recommender',
  templateUrl: './podcast-app-recommender.component.html',
  styleUrls: ['./podcast-app-recommender.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PodcastAppRecommenderComponent implements OnInit {
  desktopWidth: InputSignal<number> = input<number>(12);

  spotifyUrl: InputSignal<string> = input<string>('https://open.spotify.com/show/7oQyn8CoR18D2PQD2hXz5m');
  applePodcastsUrl: InputSignal<string> = input<string>('https://podcasts.apple.com/us/podcast/pestisracok-hu/id1684256429');
  hirFMUrl: InputSignal<string> = input<string>('https://hirfm.hu');

  @HostBinding('class') hostClass: string = '';

  ngOnInit(): void {
    if (this.desktopWidth() <= 4) {
      this.hostClass = 'small';
    }
  }
}
