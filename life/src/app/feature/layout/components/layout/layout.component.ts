import { As<PERSON><PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, TemplateRef, ViewChild } from '@angular/core';
import {
  AdvertisementAdoceanComponent,
  BlockWrapperTemplateData,
  BreakingNews,
  ElectionsBoxComponent,
  HtmlEmbedComponent,
  LayoutComponent as KesmaLayoutComponent,
  KoponyegComponent,
  KoponyegDefinitions,
  KoponyegType,
  LayoutContentItemWrapperTemplateData,
  LayoutContentParams,
  LayoutElementContentConfiguration,
  LayoutElementContentType,
  LayoutElementRow,
  LayoutPageType,
  mapRealEstateApiDataToRealEstateData,
  PAGE_TYPES,
  RealEstateBazaarApiData,
  RealEstateBazaarBackendResponse,
  RealEstateBazaarBlockComponent,
  RealEstateBazaarData,
  RealEstateBazaarSearchBlockComponent,
  SponsoredBoxComponent,
  VoteDataWithAnswer,
  VoteService,
} from '@trendency/kesma-ui';
import { LifeSponsoredQuizComponent } from 'src/app/shared/components/life-sponsored-quiz/life-sponsored-quiz.component';
import {
  ArticleCardComponent,
  BrandingBoxExComponent,
  ElectionsService,
  LifeBlockTitleComponent,
  LifeNewsletterBoxComponent,
  LifeVotingComponent,
  LifeWysiwygBoxComponent,
} from '../../../../shared';
import { VariableSponsoredDidYouKnowWrapperComponent } from '../../../../shared/components/variable-sponsored-did-you-know-wrapper/variable-sponsored-did-you-know-wrapper.component';

@Component({
  selector: 'app-layout',
  templateUrl: './layout.component.html',
  styleUrls: ['./layout.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    KesmaLayoutComponent,
    NgIf,
    KoponyegComponent,
    HtmlEmbedComponent,
    RealEstateBazaarSearchBlockComponent,
    AdvertisementAdoceanComponent,
    RealEstateBazaarBlockComponent,
    BrandingBoxExComponent,
    ElectionsBoxComponent,
    LifeBlockTitleComponent,
    ArticleCardComponent,
    LifeWysiwygBoxComponent,
    LifeVotingComponent,
    LifeNewsletterBoxComponent,
    LifeSponsoredQuizComponent,
    SponsoredBoxComponent,
    AsyncPipe,
    VariableSponsoredDidYouKnowWrapperComponent,
  ],
})
export class LayoutComponent {
  @Input() adPageType = PAGE_TYPES.all_articles_and_sub_pages;
  @Input() structure: LayoutElementRow[];
  @Input() configuration: LayoutElementContentConfiguration[];
  @Input() layoutType?: LayoutPageType;
  @Input() breakingNews: BreakingNews[] = [];
  @Input() contentComponentsWrapper: TemplateRef<LayoutContentItemWrapperTemplateData>;
  @Input() contentComponentsInnerWrapper: TemplateRef<LayoutContentItemWrapperTemplateData>;
  @Input() blockTitleWrapper: TemplateRef<BlockWrapperTemplateData>;
  @Input() editorFrameSize?: 'desktop' | 'mobile';

  @ViewChild('contentComponents', { read: TemplateRef, static: false })
  contentComponents: TemplateRef<LayoutContentParams>;

  readonly LayoutPageTypes = LayoutPageType;
  readonly LayoutElementContentType = LayoutElementContentType;

  readonly KoponyegType: KoponyegType = KoponyegType.Light;
  readonly KoponyegDefinitions: KoponyegDefinitions = { width: 300, height: 100 };
  public realEstateData: RealEstateBazaarData[] = [];
  private extractedData: any = {};
  private realEstateDataLoading = false;

  readonly voteCache = this.voteService.voteCache;

  constructor(
    private readonly changeDetector: ChangeDetectorRef,
    public readonly voteService: VoteService,
    public readonly httpClient: HttpClient,
    public readonly electionsService: ElectionsService
  ) {}

  handleRealEstateInitEvent(): void {
    if (!this.realEstateDataLoading && this.realEstateData.length < 1) {
      this.getRealEstateData();
    }
  }

  getRealEstateData(): void {
    this.realEstateDataLoading = true;
    const realEstates: Array<RealEstateBazaarData> = [];
    // eslint-disable-next-line max-len
    this.httpClient
      .get(
        'https://www.ingatlanbazar.hu/api/property-search?property_location' +
          '=6,1000000004,1000000005,1000000006,1000000007&amp;;' +
          'property_newbuildonly=on&amp;property__2=3_2'
      )
      .subscribe((data: RealEstateBazaarBackendResponse) => {
        data?.hits?.forEach((realEstate: RealEstateBazaarApiData) => {
          realEstates.push(mapRealEstateApiDataToRealEstateData(realEstate));
        });
        this.realEstateData = realEstates;
        this.realEstateDataLoading = false;
        this.changeDetector.detectChanges();
      });
  }

  onVotingSubmit($event: string, voteData: VoteDataWithAnswer): void {
    const votingSubmit$ = this.voteService.onVotingSubmit($event, voteData).subscribe({
      complete: () => {
        this.changeDetector.detectChanges();
        votingSubmit$.unsubscribe();
      },
    });
  }

  clearExtractedData(): void {
    this.extractedData = {};
  }
}
