<kesma-layout
  [adPageType]="adPageType"
  [blockTitleAfterComponentRef]="blockTitleAfterComponent"
  [blockTitleRef]="blockTitles"
  [breakingNews]="breakingNews"
  [configuration]="configuration"
  [layoutType]="layoutType ?? LayoutPageTypes.HOME"
  [structure]="structure"
  [contentComponentsRef]="contentComponents"
  [contentComponentWrapperRef]="contentComponentsWrapper"
  [contentComponentInnerWrapperRef]="contentComponentsInnerWrapper"
  [blockTitleWrapperRef]="blockTitleWrapper"
  [editorFrameSize]="editorFrameSize"
></kesma-layout>

<ng-template #blockTitles let-layoutElement="layoutElement" let-layoutType="layoutType">
  <life-block-title-row *ngIf="layoutType !== LayoutPageTypes.SIDEBAR" [data]="layoutElement.blockTitle"></life-block-title-row>
</ng-template>

<ng-template #blockTitleAfterComponent let-layoutElement="layoutElement">
  <life-block-title-row *ngIf="layoutElement.blockTitle?.sponsored" [data]="layoutElement.blockTitle" [isOnlyUrl]="true"> </life-block-title-row>
</ng-template>

<ng-template #contentComponents let-desktopWidth="desktopWidth" let-index="index" let-layoutElement="layoutElement">
  <ng-container *ngIf="layoutElement?.config || layoutElement?.configurable === false">
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.BLOCK_SEPARATOR">
      <hr class="block-separator" />
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Article">
      <life-article-card
        *ngIf="layoutElement.extractorData?.[index] as data"
        [data]="data"
        [desktopWidth]="desktopWidth"
        [isInSidebar]="layoutType === LayoutPageTypes.SIDEBAR"
        [isSponsoredContent]="!!data.sponsorTitle"
        [styleID]="layoutElement.styleId"
      >
      </life-article-card>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Wysiwyg">
      <life-wysiwyg-box *ngIf="layoutElement.extractorData as data" [htmlArray]="data"> </life-wysiwyg-box>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.SponsoredQuiz">
      <ng-container *ngIf="layoutElement.extractorData as sponsoredQuizData">
        <life-sponsored-quiz [data]="sponsoredQuizData"> </life-sponsored-quiz>
      </ng-container>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Vote">
      @if (layoutElement.extractorData; as extractorData) {
        @if ((voteCache[extractorData?.data?.id] | async) || extractorData; as voteData) {
          <life-voting
            (vote)="onVotingSubmit($event, voteData)"
            [data]="voteData.data"
            [desktopWidth]="desktopWidth"
            [isSidebar]="layoutType === LayoutPageTypes.SIDEBAR"
            [voteId]="voteData.votedId"
          />
        }
      }
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.koponyeg">
      <kesma-koponyeg [data]="KoponyegDefinitions" [type]="KoponyegType"></kesma-koponyeg>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.HtmlEmbed">
      <kesma-html-embed *ngIf="layoutElement?.config?.htmlContent as data" [data]="data"></kesma-html-embed>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.NewsletterBlock">
      <life-newsletter-box></life-newsletter-box>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.IngatlanbazarSearch">
      <kesma-real-estate-bazaar-search-block
        [defaultLocation]="layoutElement.config.defaultLocation"
        [defaultType]="layoutElement.config.defaultType"
        [showAdvertiseButton]="layoutElement.config.showAdvertiseButton"
        [showBudapestLocations]="layoutElement.config.showBudapestLocations"
        [showCountyLocations]="layoutElement.config.showBudapestLocations"
        [showNewBuildButton]="layoutElement.config.showNewBuildButton"
        [showOtherLocations]="layoutElement.config.showOtherLocations"
        [utmSource]="layoutElement.config.utmSource"
      >
      </kesma-real-estate-bazaar-search-block>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Ad">
      <kesma-advertisement-adocean
        *ngIf="layoutElement.ad as ad"
        [ad]="ad"
        [isHidden]="layoutElement.contentType !== LayoutElementContentType.Ad && !ad"
        [style]="{ background: !ad.bannerName.includes('prcikkfix_') ? 'var(--ad-bg)' : '', padding: 'var(--ad-padding)' }"
      >
      </kesma-advertisement-adocean>
    </ng-container>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.IngatlanbazarConfigurable">
    <kesma-real-estate-bazaar-block
      (initEvent)="handleRealEstateInitEvent()"
      [data]="realEstateData"
      [itemsToShow]="layoutElement.itemsToShow"
      [showHeader]="layoutElement.showHeader"
    >
    </kesma-real-estate-bazaar-block>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.INGATLANBAZAR">
    <kesma-real-estate-bazaar-block (initEvent)="handleRealEstateInitEvent()" [data]="realEstateData" [itemsToShow]="1" [showHeader]="true">
    </kesma-real-estate-bazaar-block>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.BrandingBoxEx">
    <app-branding-box-ex [brand]="layoutElement.brand"></app-branding-box-ex>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.ELECTIONS_BOX">
    <kesma-elections-box [desktopWidth]="desktopWidth" [link]="electionsService.getElections2024Link()" [styleID]="layoutElement.styleId">
    </kesma-elections-box>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.DID_YOU_KNOW">
    <app-variable-sponsored-did-you-know-wrapper [id]="layoutElement.config?.selectedDidYouKnowBox?.[0]?.id"></app-variable-sponsored-did-you-know-wrapper>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.CONFIGURABLE_SPONSORED_BOX">
    <kesma-sponsored-box [data]="layoutElement.config"></kesma-sponsored-box>
  </ng-container>
</ng-template>
