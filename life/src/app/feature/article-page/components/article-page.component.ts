import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgSwitchCase, NgTemplateOutlet } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { Dom<PERSON>anitizer, SafeHtml } from '@angular/platform-browser';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { FormatDatePipe, IMetaData, SchemaOrgService, SeoService, StorageService, UtilService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  AdvertsMeta,
  ALL_BANNER_LIST,
  AnalyticsService,
  Article,
  ArticleAdvertisements,
  ArticleBody,
  ArticleBodyDetails,
  ArticleBodyType,
  ArticleCard,
  ArticleFileLinkDirective,
  ArticleResolverData,
  ArticleRouteParams,
  ArticleVideoComponent,
  AutoArticleBodyAdService,
  buildArticleUrl,
  DetailReferenceArticle,
  ElectionsBoxComponent,
  ElectionsBoxStyle,
  GalleryData,
  GalleryElementData,
  getStructuredDataForArticle,
  NativtereloComponent,
  PAGE_TYPES,
  previewBackendArticleToArticleCard,
  SecondaryFilterAdvertType,
  SponsoredTag,
  TenArticleRecommenderData,
  VoteDataWithAnswer,
  VoteService,
} from '@trendency/kesma-ui';
import { differenceInYears } from 'date-fns';
import { DateFnsModule } from 'ngx-date-fns';
import { combineLatest, forkJoin, Observable, Subject } from 'rxjs';
import { map, switchMap, takeUntil } from 'rxjs/operators';
import { EcommGoogleNativeAds } from 'src/app/shared/definitions/advertisement.definitions';
import { environment } from '../../../../environments/environment';
import {
  ArticleCardComponent,
  ArticleCardType,
  defaultMetaInfo,
  EcommAdsBoxComponent,
  ElectionsService,
  GalleryService,
  LifeAdultComponent,
  LifeArticleRecommenderComponent,
  LifeMultiArticleRecommenderComponent,
  LifeNewsletterBoxComponent,
  LifeQuizComponent,
  LifeRatingComponent,
  LifeSliderGalleryComponent,
  LifeVotingComponent,
  LifeWysiwygBoxComponent,
  PersonalizedRecommendationService,
  SocialComponent,
  SponsoredTagBoxComponent,
  SponsorshipComponent,
  TagListComponent,
} from '../../../shared';
import { SidebarComponent } from '../../layout/components/sidebar/sidebar.component';
import { ArticleHeaderComponent } from './article-header/article-header.component';

@Component({
  selector: 'app-article-page',
  templateUrl: 'article-page.component.html',
  styleUrls: ['article-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [AutoArticleBodyAdService, FormatDatePipe],
  imports: [
    NgIf,
    AdvertisementAdoceanComponent,
    ArticleHeaderComponent,
    SponsorshipComponent,
    TagListComponent,
    RouterLink,
    SocialComponent,
    NgTemplateOutlet,
    NativtereloComponent,
    NgFor,
    ElectionsBoxComponent,
    SidebarComponent,
    EcommAdsBoxComponent,
    NgSwitch,
    NgSwitchCase,
    ArticleFileLinkDirective,
    ArticleVideoComponent,
    DateFnsModule,
    LifeAdultComponent,
    ArticleCardComponent,
    LifeWysiwygBoxComponent,
    LifeQuizComponent,
    LifeRatingComponent,
    LifeArticleRecommenderComponent,
    LifeNewsletterBoxComponent,
    LifeVotingComponent,
    LifeSliderGalleryComponent,
    LifeMultiArticleRecommenderComponent,
    SponsoredTagBoxComponent,
    AsyncPipe,
  ],
})
export class ArticlePageComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('dataTrigger', { static: false }) readonly dataTrigger: ElementRef<HTMLDivElement>;
  @ViewChild('externalRecommendationsBlock', { static: false }) readonly externalRecommendationsBlock: ElementRef<HTMLDivElement>;

  article?: Article;
  articleSlug?: string;
  isUserAdultChoice?: boolean;
  adverts?: ArticleAdvertisements;
  interrupter?: ArticleAdvertisements;
  mobile_pr_cikkfix?: Advertisement[];
  advertisementMeta: AdvertsMeta;

  embedPrAdvert?: SafeHtml;

  ecommGoogleNativeAds?: EcommGoogleNativeAds;

  externalRecommendation?: ArticleCard[];
  metaData?: IMetaData;
  articleLink: string[] = [];
  adPageType = PAGE_TYPES.all_articles_and_sub_pages;
  galleries: Record<string, GalleryData> = {};
  sponsoredTag?: SponsoredTag;

  ElectionsBoxStyle = ElectionsBoxStyle;

  readonly ArticleBodyType = ArticleBodyType;
  readonly ArticleCardType = ArticleCardType;
  public readonly buildArticleUrl = buildArticleUrl;
  isDesktopAd: boolean = this.utilsService.isBrowser() && window?.innerWidth > 768;
  isMobileAd: boolean = this.utilsService.isBrowser() && window?.innerWidth <= 768;
  tereloUrl: string =
    'https://terelo.mediaworks.hu/nativterelo/nativterelo.html?utmSource=life.hu' + '&traffickingPlatforms=Life%20Nat%C3%ADv' + '&domain=Life';
  googleNewUrl: string = 'https://news.google.com/publications/CAAqIAgKIhpDQklTRFFnTWFna0tCMnhwWm1VdWFIVW9BQVAB?hl=hu&gl=HU&ceid=HU%3Ahu';
  readonly #unsubscribe$: Subject<boolean> = new Subject();
  #cannonicalUrl?: string;
  isExceptionAdvertEnabled: boolean;

  readonly voteCache = this.voteService.voteCache;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly seo: SeoService,
    private readonly cdr: ChangeDetectorRef,
    private readonly adStore: AdvertisementAdoceanStoreService,
    private readonly analyticsService: AnalyticsService,
    private readonly formatDate: FormatDatePipe,
    private readonly storage: StorageService,
    private readonly schemaService: SchemaOrgService,
    private readonly voteService: VoteService,
    private readonly utilsService: UtilService,
    private readonly galleryService: GalleryService,
    private readonly autoArticleBodyAd: AutoArticleBodyAdService,
    private readonly sanitizer: DomSanitizer,
    private readonly personalizedRecommendationService: PersonalizedRecommendationService,
    public readonly electionsService: ElectionsService
  ) {}

  get galleriesData(): Record<string, GalleryData> {
    return this.galleries as unknown as Record<string, GalleryData>;
  }

  get isArticleOutdated(): boolean {
    const publishDiff = this.article?.publishDate ? differenceInYears(new Date(), this.article?.publishDate) : 0;
    const lastUpdatedDiff = this.article?.lastUpdated ? differenceInYears(new Date(), this.article?.lastUpdated) : 1;
    return publishDiff >= 1 && lastUpdatedDiff >= 1;
  }

  ngOnInit(): void {
    (this.route.data as Observable<{ data: ArticleResolverData }>).pipe(takeUntil(this.#unsubscribe$)).subscribe(
      ({
        data: {
          article: { data: article },
          articleSlug,
          recommendations,
          url,
          article: { meta },
        },
      }) => {
        if (!article) {
          return;
        }
        let body = article.body;
        this.autoArticleBodyAd.init(article.body);
        body = this.autoArticleBodyAd.autoAd();

        this.article = {
          ...article,
          slug: articleSlug,
          body: this.#prepareArticleBody(body),
          excerpt: article?.lead || article?.excerpt,
        };
        this.articleLink = this.article ? buildArticleUrl(this.article) : [];
        this.embedPrAdvert = this.sanitizer.bypassSecurityTrustHtml(this.article?.embedPrAdvert ?? '');
        this.externalRecommendation = recommendations?.data?.externalRecommendation;
        this.getPersonalizedRecommendations();
        this.articleSlug = articleSlug;
        this.adPageType = `column_${this.article.primaryColumn.slug}`;
        this.sponsoredTag = meta?.['sponsoredTag'];
        this.isUserAdultChoice = (this.storage.getSessionStorageData('isAdultChoice', false) ?? false) && this.article?.isAdultsOnly;
        this.#cannonicalUrl = this.article.seo?.seoCanonicalUrl || this.article?.canonicalUrl || `${this.seo.hostUrl}/${url || ''}`;
        this.adStore.setIsAdultPage(this.isUserAdultChoice);
        this.adStore.setArticleParentCategory(this.adPageType);
        this.seo.updateCanonicalUrl(this.#cannonicalUrl, {
          skipSeoMetaCheck: true,
          addHostUrl: false,
        });
        this.loadEmbeddedGalleries();
        this.voteService.initArticleVotes(this.article);
        this.#setMetaData();
        if (this.article) {
          this.schemaService.removeStructuredData();
          this.schemaService.insertSchema(getStructuredDataForArticle(this.article, this.seo.currentUrl, environment?.siteUrl ?? ''));
        }
        this.cdr.markForCheck();

        const categorySlug = this.article?.primaryColumn?.slug;

        setTimeout(() => {
          this.analyticsService.sendPageView(
            {
              pageCategory: categorySlug,
              customDim2: this.article?.topicLevel1,
              customDim1: this.article?.aniCode,
              title: this.article?.title,
              articleSource: this.article?.articleSource ? this.article.articleSource : 'no source',
              publishDate: this.formatDate.transform(this.article?.publishDate as Date, 'dateTime'),
              lastUpdatedDate: this.formatDate.transform(
                (this.article?.lastUpdated ? this.article.lastUpdated : this.article?.publishDate) as Date,
                'dateTime'
              ),
            },
            'Cikk'
          );
        }, 0);
      }
    );

    (
      combineLatest([
        this.route.data as Observable<{
          data: ArticleResolverData;
        }>,
        this.adStore.isAdult.asObservable(),
      ]) as Observable<[{ data: ArticleResolverData }, boolean]>
    )
      .pipe(takeUntil(this.#unsubscribe$))
      .pipe(
        map<[{ data: ArticleResolverData }, boolean], boolean | undefined>(
          ([
            {
              data: { article },
            },
          ]) => {
            this.adStore.getAdvertisementMeta(article?.data?.tags, article.data.isAdultsOnly);
            this.isExceptionAdvertEnabled = article?.data.isExceptionAdvertEnabled;

            return article?.data?.withoutAds;
          }
        )
      )
      .pipe(
        switchMap((withoutAds) => {
          this.resetAds();
          withoutAds ? this.adStore.disableAds() : this.adStore.enableAds();
          return this.adStore.advertisemenets$;
        })
      )
      .subscribe((adverts): void => {
        this.adverts = this.adStore.separateAdsByMedium(adverts, this.adPageType, ALL_BANNER_LIST, SecondaryFilterAdvertType.REPLACEABLE);
        const prAndInterrupter = this.adStore.separateAdsByMedium(adverts);
        this.interrupter = prAndInterrupter;

        this.mobile_pr_cikkfix = [
          prAndInterrupter?.mobile.prcikkfix_1,
          prAndInterrupter?.mobile.prcikkfix_2,
          prAndInterrupter?.mobile.prcikkfix_3,
          prAndInterrupter?.mobile.prcikkfix_4,
          prAndInterrupter?.mobile.prcikkfix_5,
        ];
        const { desktop, mobile } = this.adverts as AdvertisementsByMedium;
        this.ecommGoogleNativeAds = {
          ecomm: {
            desktop: [desktop?.ecomm_1, desktop?.ecomm_2, desktop?.ecomm_3, desktop?.ecomm_4, desktop?.ecomm_5, desktop?.ecomm_6],
            mobile: [mobile?.ecomm_1, mobile?.ecomm_2, mobile?.ecomm_3, mobile?.ecomm_4, mobile?.ecomm_5, mobile?.ecomm_6],
          },
          google_natives: {
            desktop: [desktop?.googlenativ_1, desktop?.googlenativ_2, desktop?.googlenativ_3],
            mobile: [mobile?.googlenativ_1, mobile?.googlenativ_2, mobile?.googlenativ_3],
          },
        };

        this.adStore.onArticleLoaded();
        this.cdr.detectChanges();
      });
  }

  resetAds(): void {
    this.adverts = undefined;
    this.mobile_pr_cikkfix = undefined;
    this.ecommGoogleNativeAds = undefined;
    this.cdr.detectChanges();
  }

  ngAfterViewInit(): void {
    if (this.utilsService.isBrowser()) {
      setTimeout(() => {
        this.route.data.pipe(takeUntil(this.#unsubscribe$)).subscribe(() => {
          if ('IntersectionObserver' in window) {
            this.observeExternalRecommendations();
            this.observeArticleEnd();
          }
        });
      }, 1000);
    }
  }

  ngOnDestroy(): void {
    this.adStore.onArticleDestroy();
    this.#unsubscribe$.next(true);
    this.#unsubscribe$.complete();
  }

  onVotingSubmit($event: string, voteData: VoteDataWithAnswer): void {
    const votingSubmit$ = this.voteService.onVotingSubmit($event, voteData).subscribe({
      complete: () => {
        this.cdr.detectChanges();
        votingSubmit$.unsubscribe();
      },
    });
  }

  onIsUserAdultChoose(isUserAdult: boolean): void {
    this.isUserAdultChoice = isUserAdult;
    this.adStore.setIsAdultPage(isUserAdult);
  }

  getMultiArticleRecommenderArticles(element: TenArticleRecommenderData): ArticleCard[] {
    return (
      element.details
        ?.filter((detailItem) => this.checkDetailItem(detailItem.type) && detailItem.value)
        // We already checked the type and if value exists, so we can safely cast it:
        .map((detailItem) => previewBackendArticleToArticleCard((detailItem as DetailReferenceArticle).value))
        .slice(0, 10) ?? []
    );
  }

  checkDetailItem(type: string): boolean {
    return type === 'Detail.Reference.Article' || type === 'Detail.Reference.ArticleOptional';
  }

  getPersonalizedRecommendations(): void {
    if (this.utilsService.isBrowser()) {
      this.personalizedRecommendationService
        .getPersonalizedRecommendations()
        .pipe(takeUntil(this.#unsubscribe$))
        .subscribe((data: ArticleCard[]): void => {
          this.externalRecommendation = data;
          this.cdr.detectChanges();
        });
    }
  }

  openGalleryDedicatedRouteLayer(gallery: GalleryData, selectedImageIndex: number): void {
    if (!gallery || !this.utilsService.isBrowser()) {
      return;
    }

    const url = location.pathname;
    const galleryUrl = ['/', 'galeria', gallery.slug, ...(selectedImageIndex || selectedImageIndex === 0 ? [selectedImageIndex + 1] : [])];

    this.router.navigate(galleryUrl, { state: { referrerArticle: url } });
  }

  /**
   * When changing slides in the gallery we should send a pageView to Google Analytics and Gemius.
   * We need to explicitly send the href, title and referrers as these pageViews are just "virtual" views, because
   * they are not triggered by a real navigation in the browser.
   * @param gallery gallery that should receive the page views.
   * @param params parameters of the slide change event. For example the index of the image
   */
  handleGallerySlideChange(gallery: GalleryData, params: any): void {
    const { index } = params;
    const galleryUrl = [this.seo.hostUrl, 'galeria', gallery.slug, ...(index || index === 0 ? [index + 1] : [])].join('/');
    const pageViewParams = {
      href: galleryUrl,
      title: gallery.title,
      referrer: this.seo.currentUrl,
    } as any;
    this.analyticsService.sendPageView(pageViewParams, 'Galéria');
    pp_gemius_hit(environment.gemiusId, `page=${galleryUrl}`);
  }

  #prepareArticleBody(body: ArticleBody[]): ArticleBody[] {
    let advertIndex = 1;
    return body.map((bodyPart: ArticleBody) => ({
      ...bodyPart,
      details: (bodyPart.details ?? []).map((detail: ArticleBodyDetails) => ({
        ...detail,
        ...this.#prepareArticleBodyDetail(detail, bodyPart.type),
      })),
      ...(bodyPart.type === ArticleBodyType.Advert && {
        adverts: {
          mobile: `mobilinterrupter_${advertIndex}`,
          desktop: `desktopinterrupter_${advertIndex++}`,
        },
      }),
    }));
  }

  #prepareArticleBodyDetail(detail: ArticleBodyDetails, type: ArticleBodyType): ArticleBodyDetails {
    let newDetail: ArticleBodyDetails;
    switch (type) {
      case ArticleBodyType.Article:
        newDetail = {
          ...detail,
          value: detail?.value ? previewBackendArticleToArticleCard(detail?.value) : null,
        };
        break;
      default:
        newDetail = { ...detail };
    }
    return newDetail;
  }

  #setMetaData(): void {
    const { thumbnail, publicAuthor, publishDate, alternativeTitle, metaThumbnail, secondaryThumbnail } = this.article || {};
    if (!this.article) {
      return;
    }

    const title = this.article.seo?.seoTitle || this.article.title;

    const comboTitle = `${title} - ${defaultMetaInfo?.ogSiteName}`;
    const finalTitle = alternativeTitle ?? comboTitle;
    const finalOgTitle = alternativeTitle && alternativeTitle.length > 0 ? alternativeTitle : `${this.article.title} - ${defaultMetaInfo?.ogSiteName}`;
    this.metaData = {
      ...defaultMetaInfo,
      title: finalTitle,
      description: this.article.seo?.seoDescription || this.article.excerpt || this.article.lead || defaultMetaInfo.description,
      robots: this.article.seo?.seoRobotsMeta || 'index, follow, max-image-preview:large',
      ogTitle: finalOgTitle,
      keywords: '',
      ogImage: secondaryThumbnail || metaThumbnail || thumbnail,
      ogType: 'article',
      articleAuthor: publicAuthor,
      articlePublishedTime: publishDate?.toISOString(),
    };
    this.seo.setMetaData(this.metaData);
  }

  private loadEmbeddedGalleries(): void {
    const bodyElements = (this.article?.body as any) ?? [];
    const gallerySubs = ((bodyElements ?? []) as GalleryElementData[])
      .filter(({ type }) => type === ArticleBodyType.Gallery)
      .filter((bodyElem) => !!bodyElem.details[0].value)
      .map((bodyElem: GalleryElementData) => this.galleryService.getGalleryDetails(bodyElem.details[0].value.slug));

    forkJoin(gallerySubs)
      .pipe(takeUntil(this.#unsubscribe$))
      .subscribe((galleries) => {
        galleries.forEach((gallery) => {
          const galleryData: GalleryData = {
            ...gallery,
            highlightedImageUrl: gallery.highlightedImage.url,
          } as any as GalleryData;
          this.galleries[gallery.id] = galleryData;
          this.cdr.markForCheck();
        });
      });
  }

  private observeArticleEnd(): void {
    if (!this.dataTrigger?.nativeElement) {
      return;
    }

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(({ isIntersecting }) => {
        if (isIntersecting) {
          this.sendEcommerceEvent();
          observer.unobserve(this.dataTrigger.nativeElement);
        }
      });
    });
    observer.observe(this.dataTrigger.nativeElement);
  }

  private sendEcommerceEvent(): void {
    const routeParams: ArticleRouteParams = this.route.snapshot.params as ArticleRouteParams;
    if (!this.article) {
      return;
    }
    this.analyticsService.sendEcommerceEvent({
      id: `T${this.article.id}`,
      title: this.article.title,
      articleSlug: routeParams.articleSlug ? routeParams.articleSlug : 'cikk-elonezet',
      category: this.article.columnTitle ?? '',
      articleSource: this.article.articleSource ? this.article.articleSource : 'no source',
      publishDate: this.formatDate.transform(this.article.publishDate as Date, 'dateTime'),
      lastUpdatedDate: this.formatDate.transform((this.article.lastUpdated ? this.article.lastUpdated : this.article.publishDate) as Date, 'dateTime'),
    });
  }

  private observeExternalRecommendations(): void {
    if (!this.externalRecommendationsBlock?.nativeElement) {
      return;
    }

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(({ isIntersecting }) => {
        if (isIntersecting && this.externalRecommendation) {
          this.personalizedRecommendationService.sendPersonalizedRecommendationAv(this.externalRecommendation).subscribe();
          observer.unobserve(this.externalRecommendationsBlock.nativeElement);
        }
      });
    });
    observer.observe(this.externalRecommendationsBlock.nativeElement);
  }
}
