<ng-container *ngIf="!isUserAdultChoice && article?.isAdultsOnly; else adultContent">
  <life-adult (isUserAdult)="onIsUserAdultChoose($event)"></life-adult>
</ng-container>

<ng-template #adultContent>
  <section>
    <div class="wrapper">
      <kesma-advertisement-adocean
        *ngIf="adverts?.mobile?.['mobilrectangle_1'] as ad"
        [isExceptionAdvertEnabled]="isExceptionAdvertEnabled"
        [ad]="ad"
        [style]="{ margin: 'var(--ad-margin)', background: 'var(--ad-bg)', padding: 'var(--ad-padding)' }"
      >
      </kesma-advertisement-adocean>
      <app-article-header [data]="article" class="wrapper-row"></app-article-header>
    </div>

    <kesma-advertisement-adocean
      *ngIf="adverts?.desktop?.['leaderboard_1'] as ad"
      [ad]="ad"
      [style]="{ margin: 'var(--ad-margin)', background: 'var(--ad-bg)', padding: 'var(--ad-padding)' }"
    >
    </kesma-advertisement-adocean>

    <div class="wrapper with-aside">
      <div class="left-column">
        <app-sponsorship *ngIf="article?.sponsorship as sponsorship" [sponsorData]="sponsorship"></app-sponsorship>
        <app-tag-list *ngIf="article?.tags as tags" [data]="tags"></app-tag-list>
        <div class="article-author">
          <div class="article-author-details">
            <img [alt]="article?.publicAuthorDescription || ''" [src]="article?.avatar || '/assets/images/placeholder.svg'" class="article-thumbnail" />
            <div>
              <div class="article-author-name">
                @if (article?.publicAuthorSlug) {
                  <a [routerLink]="['/', 'szerzo', article?.publicAuthorSlug]">{{ article?.publicAuthor || 'Life' }}</a>
                } @else {
                  {{ article?.publicAuthor || 'Life' }}
                }
              </div>
              <div class="article-date">{{ article?.publishDate | dfnsFormat: 'yyyy.MM.dd.' }}</div>
            </div>
          </div>
          <app-social [articleLink]="articleLink" [articleTitle]="article?.title"></app-social>
        </div>

        <div *ngIf="isArticleOutdated" class="out-dated-article-box">
          <img alt="Clock icon" loading="lazy" src="/assets/images/life-clock.svg" />
          <div>Cikkünk több mint egy évvel ezelőtt frissült utoljára, a benne szereplő információk elavultak lehetnek.</div>
        </div>

        <div class="article-lead">{{ article?.excerpt }}</div>

        <div *ngIf="embedPrAdvert" [innerHTML]="embedPrAdvert" class="article-embed-pr-advert"></div>

        <kesma-advertisement-adocean
          *ngIf="adverts?.mobile?.['mobilrectangle_2'] as ad"
          [ad]="ad"
          [style]="{ margin: 'var(--ad-margin)', background: 'var(--ad-bg)', padding: 'var(--ad-padding)' }"
        >
        </kesma-advertisement-adocean>
        <kesma-advertisement-adocean
          *ngIf="adverts?.desktop?.['roadblock_1'] as ad"
          [isExceptionAdvertEnabled]="isExceptionAdvertEnabled"
          [ad]="ad"
          [style]="{ margin: 'var(--ad-margin)', background: 'var(--ad-bg)', padding: 'var(--ad-padding)' }"
        >
        </kesma-advertisement-adocean>
        <ng-container [ngTemplateOutletContext]="{ body: article?.body }" [ngTemplateOutlet]="bodyContent"></ng-container>

        <div class="google-news-wrapper">
          <figure class="google-news">
            <a [href]="googleNewUrl" target="_blank">
              <img alt="Google News" loading="lazy" src="/assets/images/google-news.svg" />
            </a>
          </figure>
          <span class="google-news-text"
            >A legfrissebb hírekért kövess minket a
            <a class="google-news-link" [href]="googleNewUrl" target="_blank"> LIFE </a>
            Google News oldalán is!</span
          >
        </div>

        <div #dataTrigger *ngIf="article"></div>

        <app-sponsorship *ngIf="article?.sponsorship as sponsorship" [sponsorData]="sponsorship"></app-sponsorship>

        <kesma-nativterelo [tereloUrl]="tereloUrl"></kesma-nativterelo>

        @if (sponsoredTag) {
          <app-sponsored-tag-box [sponsoredTag]="sponsoredTag" [excludedSlug]="article?.slug || ''" />
        }

        <div #externalRecommendationsBlock class="recommendation-block">
          <ng-container *ngFor="let article of externalRecommendation; let i = index">
            <life-article-card [data]="article" [styleID]="ArticleCardType.ExternalRecommendation"></life-article-card>
            <div *ngIf="i === 5" class="full-row">
              <kesma-advertisement-adocean
                *ngIf="adverts?.mobile?.mobilrectangle_ottboxextra as ad"
                [ad]="ad"
                [style]="{ margin: 'var(--ad-margin)', background: 'var(--ad-bg)', padding: 'var(--ad-padding)' }"
              ></kesma-advertisement-adocean>
              <kesma-advertisement-adocean
                *ngIf="adverts?.desktop?.roadblock_ottboxextra as ad"
                [ad]="ad"
                [style]="{ margin: 'var(--ad-margin)', background: 'var(--ad-bg)', padding: 'var(--ad-padding)' }"
              ></kesma-advertisement-adocean>
            </div>
          </ng-container>
        </div>

        <ng-container *ngIf="electionsService.isElections2024Enabled()">
          <kesma-elections-box
            [desktopWidth]="10"
            [link]="electionsService.getElections2024Link()"
            [styleID]="ElectionsBoxStyle.DIVERTER"
          ></kesma-elections-box>
        </ng-container>

        <kesma-advertisement-adocean
          *ngIf="adverts?.mobile?.mobilrectangle_3 as ad"
          [ad]="ad"
          [style]="{ margin: 'var(--ad-margin)', background: 'var(--ad-bg)', padding: 'var(--ad-padding)' }"
        >
        </kesma-advertisement-adocean>

        <kesma-advertisement-adocean
          *ngIf="adverts?.desktop?.roadblock_2 as ad"
          [ad]="ad"
          [style]="{ margin: 'var(--ad-margin)', background: 'var(--ad-bg)', padding: 'var(--ad-padding)' }"
        >
        </kesma-advertisement-adocean>

        <kesma-advertisement-adocean
          *ngIf="adverts?.mobile?.mobilrectangle_4 as ad"
          [ad]="ad"
          [style]="{ margin: 'var(--ad-margin)', background: 'var(--ad-bg)', padding: 'var(--ad-padding)' }"
        >
        </kesma-advertisement-adocean>
      </div>
      <aside>
        <app-sidebar [adPageType]="adPageType" [articleId]="article?.id" [articleSlug]="articleSlug"></app-sidebar>
      </aside>
    </div>
    <div class="wrapper">
      <kesma-advertisement-adocean
        *ngIf="adverts?.desktop?.roadblock_3 as ad"
        [ad]="ad"
        [style]="{
          margin: 'var(--ad-margin)',
          background: 'var(--ad-bg)',
          padding: 'var(--ad-padding)',
          display: 'flex',
          'justify-content': 'center',
        }"
      >
      </kesma-advertisement-adocean>

      <app-ecomm-ads-box [ads]="ecommGoogleNativeAds?.google_natives"></app-ecomm-ads-box>

      <app-ecomm-ads-box [ads]="ecommGoogleNativeAds?.ecomm"></app-ecomm-ads-box>
    </div>

    <div
      [style]="{
        display: 'flex',
        width: '100%',
        'justify-content': 'center',
        'flex-direction': 'column',
        'max-width': 'calc(100% - 30px)',
        margin: 'auto',
      }"
    >
      <ng-container *ngIf="mobile_pr_cikkfix?.length">
        <ng-container *ngFor="let pr_cik_fix of mobile_pr_cikkfix">
          <kesma-advertisement-adocean *ngIf="pr_cik_fix as ad" [ad]="ad" [style]="{ 'margin-bottom': '20px' }"> </kesma-advertisement-adocean>
        </ng-container>
      </ng-container>
    </div>
  </section>
</ng-template>

<ng-template #bodyContent let-body="body">
  <ng-container *ngFor="let element of body">
    <ng-container [ngSwitch]="element.type">
      <ng-container *ngSwitchCase="ArticleBodyType.Wysywyg">
        <ng-container *ngFor="let wysiwygDetail of element?.details">
          <life-wysiwyg-box [html]="wysiwygDetail?.value || ''" [useLightbox]="true" trArticleFileLink></life-wysiwyg-box>
        </ng-container>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.Advert">
        <kesma-advertisement-adocean
          *ngIf="interrupter?.mobile?.[element.adverts.mobile] as ad"
          [ad]="ad"
          [style]="{ margin: 'var(--ad-margin)', background: 'var(--ad-bg)' }"
        >
        </kesma-advertisement-adocean>
        <kesma-advertisement-adocean
          *ngIf="interrupter?.desktop?.[element.adverts.desktop] as ad"
          [ad]="ad"
          [style]="{ margin: 'var(--ad-margin)', background: 'var(--ad-bg)' }"
        >
        </kesma-advertisement-adocean>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.MediaVideo">
        <kesma-article-video [data]="element?.details?.[0]?.value"></kesma-article-video>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.Quiz">
        <life-quiz [data]="element?.details?.[0]?.value"></life-quiz>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.Rating">
        <life-rating *ngIf="element?.details?.[0]?.value as data" [rating]="data?.value"></life-rating>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.Article">
        <life-article-recommender
          *ngIf="element?.details?.[0]?.value as data"
          [focusedImages]="data?.thumbnailFocusedImages"
          [lead]="data.lead"
          [thumbnailUrl]="data.thumbnailUrl || data.thumbnail?.url"
          [title]="data.title"
          [url]="buildArticleUrl(data)"
        ></life-article-recommender>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.ExternalArticleRecommendation">
        <life-article-recommender
          *ngIf="element?.details as data"
          [isExternal]="true"
          [lead]="data[0].value"
          [thumbnailUrl]="data[2]?.value"
          [url]="data[1].value"
        ></life-article-recommender>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.NewsletterSignUp">
        <life-newsletter-box></life-newsletter-box>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.Voting">
        @if (voteCache[element?.details?.[0]?.value?.id ?? ''] | async; as voteData) {
          <life-voting (vote)="onVotingSubmit($event, voteData)" [data]="voteData.data" [voteId]="voteData.votedId" />
        }
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.Gallery">
        <life-slider-gallery
          (imageClicked)="openGalleryDedicatedRouteLayer(galleriesData[element?.details?.[0]?.value?.id], $event)"
          (slideChanged)="handleGallerySlideChange(galleriesData[element?.details?.[0]?.value?.id], $event)"
          *ngIf="galleriesData[element?.details?.[0]?.value?.id]"
          [canOpenLayer]="false"
          [data]="galleriesData[element?.details?.[0]?.value?.id]"
          [isInsideAdultArticleBody]="article?.isAdultsOnly"
        ></life-slider-gallery>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.TenArticleRecommender">
        <life-multi-article-recommender *ngIf="getMultiArticleRecommenderArticles(element) as data" [data]="data"></life-multi-article-recommender>
      </ng-container>
    </ng-container>
  </ng-container>
</ng-template>
