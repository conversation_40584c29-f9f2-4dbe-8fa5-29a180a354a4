<h2 class="voting-question">{{ data?.question }}</h2>

<div class="voting-form">
  @if (!showResults || data?.isResultVisible) {
    @for (item of data?.answers; track item.id) {
      <div class="voting-item">
        <div class="voting-item-label">
          <input
            [id]="item.id"
            *ngIf="!showResults"
            [name]="'answers-' + data?.id"
            class="voting-item-radio-button"
            type="radio"
            [value]="item.id"
            [checked]="voteId === item.id"
            (click)="setVoteId(item.id)"
          />
          <label class="voting-item-text" [for]="item.id"> {{ item.answer }} </label>
        </div>
        <div class="voting-line" *ngIf="showResults">
          <span class="voting-line-inner" [style.width.%]="item?.votePercentage"></span>
        </div>
        <div class="voting-item-percentage" *ngIf="showResults">{{ item?.votePercentage }}%</div>
      </div>
    }
  }
</div>

<div class="voting-group" [class.mobile]="desktopWidth < 5 || isSidebar">
  <ng-container *ngIf="!showResults; else voted">
    <life-simple-button color="primary" (click)="onVote()" [disabled]="!voteId">Szavazok</life-simple-button>
    <life-simple-button color="secondary" (click)="watchResult()">Szavazás állása</life-simple-button>
  </ng-container>
  <ng-template #voted>
    <life-simple-button color="primary" *ngIf="canGoBack" (click)="showResults = !showResults">Vissza a szavazáshoz</life-simple-button>
    <span class="voting-group-sum">
      @if (data?.isResultVisible) {
        Összesen: {{ sum }} szavazat
      } @else {
        Köszönjük a szavazatát!
      }
    </span>
  </ng-template>
</div>
