import { ChangeDetectionStrategy, Component, HostBinding, Input, OnInit } from '@angular/core';
import { VotingComponent } from '@trendency/kesma-ui';
import { NgIf } from '@angular/common';
import { LifeSimpleButtonComponent } from '../life-simple-button/life-simple-button.component';

@Component({
  selector: 'life-voting',
  templateUrl: './life-voting.component.html',
  styleUrls: ['./life-voting.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, LifeSimpleButtonComponent],
})
export class LifeVotingComponent extends VotingComponent implements OnInit {
  canGoBack: boolean = true;

  @Input() desktopWidth: number = 12;
  @Input() @HostBinding('class.sidebar') isSidebar: boolean = false;

  get sum(): number {
    return this.data?.voteCountSum ?? this.data?.answers?.reduce((acc, curr) => acc + (Number(curr?.voteCount) ?? 0), 0) ?? 0;
  }

  override ngOnInit(): void {
    this.canGoBack = !(this.showResults || !this.voteId);
    super.ngOnInit();
  }

  override onVote(): void {
    this.canGoBack = false;
    super.onVote();
  }

  watchResult(): void {
    this.voteId = undefined;
    this.canGoBack = true;
    this.showResults = true;
  }
}
