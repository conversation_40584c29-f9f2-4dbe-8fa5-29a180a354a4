import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { DossierData, IconComponent, KesmaSwipeComponent } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'vg-dossier-list',
  templateUrl: './vg-dossier-list.component.html',
  styleUrls: ['./vg-dossier-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, IconComponent, KesmaSwipeComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class VgDossierListComponent {
  dossierData = input.required<DossierData[]>();
}
