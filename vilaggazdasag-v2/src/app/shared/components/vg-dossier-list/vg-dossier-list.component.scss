@use 'shared' as *;

:host {
  display: flex;
  width: 100%;
  align-items: center;
  column-gap: 24px;
  padding: 8px;
  border: 1px solid var(--kui-deep-teal-100);

  .dossier-element {
    color: var(--kui-deep-teal-900);
    font-size: 18px;
    font-weight: 700;
    line-height: normal;
    letter-spacing: 0.36px;

    &:hover {
      color: var(--kui-amethyst-500);
    }

    &.first {
      display: flex;
      gap: 16px;
      align-items: center;
      flex: 0 0 auto;

      .divider {
        width: 1px;
        height: 24px;
        background: var(--kui-brand-mnb);
      }
    }
  }

  .main {
    width: 100%;
    ::ng-deep .bottom-navigation {
      position: absolute;
      top: 0;
      right: -85px;
      gap: 16px;

      @include media-breakpoint-down(sm) {
        right: 0;
        top: -30px;
      }

      .navigation-button {
        &.disabled {
          kesma-icon {
            color: var(--kui-black);
          }
        }

        &:not(.disabled) {
          kesma-icon {
            color: var(--kui-deep-teal-400);
          }
        }

        &:not(.disabled):hover {
          kesma-icon {
            color: var(--kui-amethyst-500);
          }
        }
      }

      kesma-icon {
        height: 24px;
        width: 24px;
      }
    }
  }

  .navigation {
    width: 80px;
    @include media-breakpoint-down(sm) {
      display: none;
    }
  }

  @include media-breakpoint-down(sm) {
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
    gap: 8px;
    padding: 8px;

    .dossier-element {
      font-size: 16px;
      line-height: 22px;
    }

    .divider {
      display: none;
    }

    .navigation {
      order: 1;
    }
  }
}
