@use 'shared' as *;

:host {
  display: flex;
  width: 100%;
  flex-direction: column;
  gap: 24px;
  padding-bottom: 16px;

  &.style-Horizontal {
    @include media-breakpoint-up(sm) {
      border-bottom: 1px solid var(--kui-deep-teal-400);
    }

    vg-article-card {
      ::ng-deep {
        .icon {
          height: 20px !important;
          width: 20px !important;
        }
      }

      &.style-FeaturedTopImgTagTitleLead {
        ::ng-deep {
          .article-card {
            &-title {
              font-size: 16px !important;
              line-height: 22px !important;
            }

            &-lead {
              display: none;
            }
          }
        }
      }
    }

    .dossier-articles-container {
      display: flex;
      gap: 24px;

      @include media-breakpoint-down(sm) {
        flex-direction: column;
      }
    }

    .more-dossiers-container {
      &.mobile {
        display: none;
        @include media-breakpoint-down(sm) {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
      }
    }
  }

  &.style-Vertical {
    .dossier-articles-container {
      display: flex;
      flex-direction: column;
      gap: 24px;
    }

    .dossier-articles-column {
      display: block;
      width: 100%;
    }

    .more-dossiers-container {
      &.mobile {
        display: none;
      }
    }

    .dossier-header-title {
      @include media-breakpoint-down(sm) {
        line-height: 24px;
        font-size: 18px;
      }
    }

    vg-article-card {
      @include media-breakpoint-down(sm) {
        &.style-FeaturedTopImgTagTitleLead {
          ::ng-deep {
            .article-card {
              &-title {
                font-size: 18px !important;
                line-height: 24px !important;
                letter-spacing: 0.36px;
              }

              &-lead {
                font-weight: 400;
                letter-spacing: 0.36px;
              }
            }
          }
        }

        &.style-FeaturedRightImgTagTitle {
          ::ng-deep {
            .article-card {
              &-title {
                font-size: 16px !important;
                line-height: 22px !important;
              }
            }
          }
        }
      }
    }
  }
}

.dossier-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;

  &-title {
    display: block;
    color: var(--kui-sponsor-block-title-color);
    transition: color 0.3s;

    &:hover {
      color: var(--kui-amethyst-500) !important;
    }

    &.mobile {
      @include media-breakpoint-down(sm) {
        font-size: 18px;
        line-height: normal;
        letter-spacing: 0.36px;
        flex: 1;
      }
    }
  }

  &.sponsored {
    padding: 4px 8px;
    border-style: solid;
    border-width: 1px;
    border-color: var(--kui-sponsor-block-title-color);

    @include media-breakpoint-down(sm) {
      padding: 8px;
    }
  }
}
.dossier-ad-label {
  width: 100%;
  text-align: center;
  opacity: 0.75;
  font-size: 12px;
  text-transform: uppercase;
  margin-top: -10px;
}

.sponsor-details {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-grow: 1;

  &-image {
    width: 105px;
    height: auto;

    @include media-breakpoint-down(sm) {
      width: 136px;
    }
  }

  &-separator {
    width: 1px;
    height: 24px;
    background-color: var(--kui-gray-950);

    @include media-breakpoint-down(sm) {
      display: none;
    }
  }
}

.more-dossiers-container {
  padding: 8px 0px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--kui-gray-950);
  flex-grow: 0.2;
  transition: all 0.3s;

  &:hover {
    color: var(--kui-amethyst-500);
    border-bottom: 1px solid var(--kui-amethyst-500);

    .icon {
      filter: invert(37%) sepia(24%) saturate(6358%) hue-rotate(245deg) brightness(101%) contrast(97%);
    }
  }
}

.dossier-articles-column {
  display: flex;
  flex-direction: column;
  width: 33%;

  @include media-breakpoint-down(sm) {
    width: 100%;
  }

  &.second {
    gap: 24px;

    @include media-breakpoint-down(sm) {
      gap: 12px;
    }
  }

  &.third {
    gap: 16px;
  }
}

.dossier-article {
  &:hover {
    .dossier-article-title,
    .dossier-article-lead {
      text-decoration-color: var(--kui-deep-teal-400);
    }

    .dossier-article-thumbnail {
      transform: scale(1.2);
    }

    kesma-icon {
      color: var(--kui-amethyst-500);
    }
  }
  &-details {
    display: flex;
    flex-direction: row;
    gap: 16px;
  }

  /* &-video-badge {
    display: flex;
    flex-direction: row;
    gap: 4px;
    align-items: center;

    span {
      margin-top: 2px;
      color: var(--kui-amethyst-500);
    }
  } */

  &-tag-and-title {
    display: flex;
    flex-direction: column;
    gap: 8px;
    flex: 1;
  }

  &-tags {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  &-title,
  .font-small-14 {
    font-weight: 700 !important;
  }

  &-title {
    line-height: 22px;
  }

  &-first-tag {
    display: flex;
    align-items: center;
    transition: color 0.3s;

    &:hover {
      color: var(--kui-deep-teal-400);
    }
  }

  &-type-title {
    color: var(--kui-white);
  }

  &-type {
    border-radius: 2px;
    padding: 8px;
    border: 1px solid var(--kui-gray-50);
    background-color: var(--kui-gray-950-65);
    transition: background-color 0.3s;

    &:hover {
      background-color: var(--kui-deep-teal-400);
    }
  }

  &-title,
  &-lead {
    text-decoration: underline transparent;
    text-decoration-thickness: 1px;
    text-underline-offset: 5px;
    transition: all 0.3s;
  }

  &-lead {
    margin-top: 8px;
    display: block;

    &.hidden-lead-on-desktop {
      display: none;
      @include media-breakpoint-down(sm) {
        display: block;
      }
    }
  }

  &-thumbnail-box {
    position: relative;
    overflow: hidden;
    width: 112px;
    height: 112px;
  }

  &-thumbnail {
    height: 100%;
    width: 100%;
    object-fit: cover;
    transition: all 0.5s ease-in-out;
  }

  &-badges {
    display: flex;
    gap: 8px;
    align-items: center;

    .badge-title {
      font-size: 12px;
      font-weight: 700;
      line-height: 14px;
      margin-left: -4px;
    }
  }
}

:host {
  &.style-Horizontal,
  &.column-alignment {
    .dossier-articles {
      @media screen and (max-width: 1300px) {
        &-container {
          flex-direction: column;
        }

        &-column {
          width: 100%;
        }
      }
    }
  }
}

.purple {
  color: var(--kui-amethyst-500);
}

.red {
  color: var(--kui-red-500);
}

.thumbnail-icon {
  position: absolute;
  right: 8px;
  bottom: 8px;
  color: var(--kui-deep-teal-400);
}

.font-medium-16 {
  font-weight: 400;
}
