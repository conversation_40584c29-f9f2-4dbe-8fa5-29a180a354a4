<kesma-layout
  [adPageType]="adPageType"
  [blockTitleRef]="blockTitles"
  [breakingNews]="breakingNews"
  [configuration]="configuration"
  [contentComponentsRef]="contentComponents"
  [fullWidthCustomBackground]="true"
  [layoutType]="layoutType ?? LayoutPageTypes.HOME"
  [structure]="structure"
  [contentComponentWrapperRef]="contentComponentsWrapper"
  [contentComponentInnerWrapperRef]="contentComponentsInnerWrapper"
  [blockTitleWrapperRef]="blockTitleWrapper"
  [editorFrameSize]="editorFrameSize"
></kesma-layout>

<ng-template #blockTitles let-backgroundColor="backgroundColor" let-desktopWidth="desktopWidth" let-layoutElement="layoutElement" let-layoutType="layoutType">
  <vg-block-title
    [data]="getBlockTitleData(layoutElement, backgroundColor)"
    [desktopWidth]="layoutType === LayoutPageTypes.SIDEBAR ? 3 : desktopWidth"
    [headingLevel]="2"
  >
  </vg-block-title>
</ng-template>

<ng-template #contentComponents let-backgroundColor="backgroundColor" let-desktopWidth="desktopWidth" let-index="index" let-layoutElement="layoutElement">
  <ng-container *ngIf="layoutElement?.config || layoutElement?.configurable === false">
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Article">
      <vg-article-card
        *ngIf="layoutElement.extractorData?.[index] as data"
        [data]="data"
        [desktopWidth]="desktopWidth"
        [displayLinkIcon]="
          layoutElement.styleId === ArticleCardType.FeaturedRightImgTagTitle || layoutElement.styleId === ArticleCardType.FeaturedRightImgTagTitleLead
        "
        [hasOutline]="layoutElement.hasOutline || !!backgroundColor"
        [hasSeparator]="layoutElement.hasSeparator"
        [isLight]="!!backgroundColor"
        [isSidebar]="layoutType === LayoutPageTypes.SIDEBAR"
        [styleID]="layoutElement.styleId"
      >
      </vg-article-card>
    </ng-container>

    <ng-container
      *ngIf="layoutElement.contentType === LayoutElementContentType.Opinion && !(layoutElement.secondaryContentType === LayoutElementContentType.OpinionBlock)"
    >
      <vg-article-card
        *ngIf="layoutElement.extractorData?.[index] as data"
        [data]="data"
        [desktopWidth]="desktopWidth"
        [hasOutline]="layoutElement.hasOutline || !!backgroundColor"
        [hasSeparator]="layoutElement.hasSeparator"
        [isLight]="!!backgroundColor"
        [isOpinion]="true"
        [isSidebar]="layoutType === LayoutPageTypes.SIDEBAR"
        [styleID]="layoutElement.styleId"
      >
      </vg-article-card>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.NEWS_FEED">
      <vg-news-feed *ngIf="layoutElement.extractorData?.[index] as data" [data]="data"></vg-news-feed>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.DossierList">
      <vg-dossier-list *ngIf="layoutElement.extractorData as data" [dossierData]="data"></vg-dossier-list>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.StockChart">
      <app-exchange-box [border]="layoutElement.border" [dataType]="layoutElement.dataType" [styleId]="layoutElement.styleId"> </app-exchange-box>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Dossier">
      <vg-dossier-box *ngIf="layoutElement.extractorData as data" [data]="data?.[0]" [desktopWidth]="desktopWidth" [styleID]="layoutElement.styleId">
      </vg-dossier-box>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.ARTICLES_WITH_VIDEO_CONTENT">
      <vg-video-block
        *ngIf="layoutElement.extractorData as data"
        [data]="data"
        [desktopWidth]="desktopWidth"
        [isBackgroundDark]="!!backgroundColor || (isMobile$ | async) === true"
        [isSidebar]="layoutType === LayoutPageTypes.SIDEBAR"
      >
      </vg-video-block>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Ad">
      <kesma-advertisement-adocean *ngIf="layoutElement.ad as ad" [ad]="ad" [isHidden]="layoutElement.contentType !== LayoutElementContentType.Ad && !ad">
      </kesma-advertisement-adocean>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.NewsletterBlock">
      <vg-newsletter-box [isFullWidth]="desktopWidth === 12" [isSidebar]="layoutType === LayoutPageTypes.SIDEBAR" [styleID]="layoutElement.styleId">
      </vg-newsletter-box>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.NewsletterBlockGong">
      <vg-newsletter-box [isSidebar]="layoutType === LayoutPageTypes.SIDEBAR" [styleID]="NewsletterBoxType.Gong"></vg-newsletter-box>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.PrBlock">
      <vg-sponsored-content *ngIf="layoutElement.extractorData?.[index] as data" [data]="data" [styleID]="layoutElement.styleId"> </vg-sponsored-content>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.BLOG && index === 0">
      <vg-external-blog *ngIf="getBlogData(layoutElement) as blogData" [data]="blogData.data" [styleID]="blogData.styleID"> </vg-external-blog>
    </ng-container>

    <ng-container
      *ngIf="
        layoutElement.contentType === LayoutElementContentType.AGROKEP || (layoutElement.contentType === LayoutElementContentType.AGROKEP_LIST && index === 1)
      "
    >
      <vg-agrokep
        *ngIf="getAgrokepData() | async as agrokepData"
        [articleLength]="layoutElement.contentLength"
        [data]="agrokepData"
        [desktopWidth]="desktopWidth"
        [isSidebar]="layoutType === LayoutPageTypes.SIDEBAR"
      >
      </vg-agrokep>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.BrandingBoxEx">
      <app-vg-branding-box-ex [brand]="layoutElement.brand" [articleLimit]="layoutElement?.articleLimit || 4"></app-vg-branding-box-ex>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Gallery">
      <vg-gallery-block *ngIf="layoutElement.extractorData?.[index] as data" [data]="data" [desktopWidth]="desktopWidth" [isBackgroundDark]="!!backgroundColor">
      </vg-gallery-block>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.LATEST_NEWS">
      <vg-latest-news *ngIf="layoutElement.extractorData as data" [data]="data"></vg-latest-news>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.MOST_VIEWED">
      <vg-most-read *ngIf="layoutElement.extractorData as data" [data]="data"></vg-most-read>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.TEXT_BOX">
      <vg-knowledge-box *ngIf="layoutElement?.config?.text as data" [routeUrl]="['/', 'szoszedet']" [text]="data"> </vg-knowledge-box>
    </ng-container>

    <ng-container
      *ngIf="
        layoutElement.contentType === LayoutElementContentType.Opinion &&
        layoutElement.secondaryContentType === LayoutElementContentType.OpinionBlock &&
        index === 0
      "
    >
      <vg-opinion-block *ngIf="layoutElement.extractorData as data" [data]="data" [desktopWidth]="desktopWidth"> </vg-opinion-block>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.ARTICLES_WITH_PODCAST_CONTENT && index === 0">
      <vg-podcast-box
        *ngIf="layoutElement.extractorData as data"
        [class.sidebar]="layoutType === LayoutPageTypes.SIDEBAR"
        [data]="data"
        [desktopWidth]="desktopWidth"
        [isLight]="!!backgroundColor || (isMobile$ | async) === true || layoutType === LayoutPageTypes.SIDEBAR"
        [isSidebar]="layoutType === LayoutPageTypes.SIDEBAR"
      >
      </vg-podcast-box>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.PODCAST_ARTICLE_LIST">
      <vg-article-card
        *ngIf="layoutElement.extractorData?.[index] as data"
        [data]="data"
        [desktopWidth]="desktopWidth"
        [hasOutline]="layoutElement.hasOutline || !!backgroundColor"
        [hasSeparator]="layoutElement.hasSeparator"
        [isLight]="!!backgroundColor"
        [isSidebar]="layoutType === LayoutPageTypes.SIDEBAR"
        [styleID]="layoutElement.styleId"
      >
      </vg-article-card>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.CONFERENCE">
      <vg-conference-box
        *ngIf="layoutElement.extractorData as data"
        [class.in-sidebar]="layoutType === LayoutPageTypes.SIDEBAR"
        [class.not-in-sidebar]="layoutType !== LayoutPageTypes.SIDEBAR"
        [data]="data"
        [desktopWidth]="desktopWidth"
        [isSidebar]="layoutType === LayoutPageTypes.SIDEBAR"
      >
      </vg-conference-box>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.MinuteToMinute">
      <ng-container *ngIf="layoutElement.extractorData?.[index] as data">
        <vg-minute-by-minute [data]="data" [minuteByMinutes]="(getMinuteToMinuteData(data) | async) ?? []"></vg-minute-by-minute>
      </ng-container>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.koponyeg">
      <kesma-koponyeg [data]="KoponyegDefinitions" [type]="KoponyegType"></kesma-koponyeg>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.HtmlEmbed">
      <kesma-html-embed *ngIf="layoutElement?.config?.htmlContent as data" [data]="data"></kesma-html-embed>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Image">
      <vg-header-image *ngIf="layoutElement.extractorData as data" [data]="data"></vg-header-image>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.IngatlanbazarSearch">
      <kesma-real-estate-bazaar-search-block
        [defaultLocation]="layoutElement.config.defaultLocation"
        [defaultType]="layoutElement.config.defaultType"
        [showAdvertiseButton]="layoutElement.config.showAdvertiseButton"
        [showBudapestLocations]="layoutElement.config.showBudapestLocations"
        [showCountyLocations]="layoutElement.config.showBudapestLocations"
        [showNewBuildButton]="layoutElement.config.showNewBuildButton"
        [showOtherLocations]="layoutElement.config.showOtherLocations"
        [utmSource]="layoutElement.config.utmSource"
      >
      </kesma-real-estate-bazaar-search-block>
    </ng-container>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.IngatlanbazarConfigurable">
    <kesma-real-estate-bazaar-block
      (initEvent)="handleRealEstateInitEvent()"
      [data]="realEstateBazaar.realEstateData"
      [itemsToShow]="layoutElement.itemsToShow"
      [showHeader]="layoutElement.showHeader"
    >
    </kesma-real-estate-bazaar-block>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.INGATLANBAZAR">
    <kesma-real-estate-bazaar-block (initEvent)="handleRealEstateInitEvent()" [data]="realEstateBazaar.realEstateData" [itemsToShow]="1" [showHeader]="true">
    </kesma-real-estate-bazaar-block>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Wysiwyg">
    <vg-wysiwyg-box *ngIf="layoutElement.extractorData as data" [htmlArray]="data"></vg-wysiwyg-box>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.DID_YOU_KNOW">
    <app-variable-sponsored-did-you-know-wrapper [id]="layoutElement.config?.selectedDidYouKnowBox?.[0]?.id"></app-variable-sponsored-did-you-know-wrapper>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.CONFIGURABLE_SPONSORED_BOX">
    <kesma-sponsored-box [data]="layoutElement.config"></kesma-sponsored-box>
  </ng-container>
</ng-template>
