import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, TemplateRef, ViewChild } from '@angular/core';
import {
  AdvertisementAdoceanComponent,
  ArticleCard,
  backendBoolToBool,
  BlockTitle,
  BlockWrapperTemplateData,
  BreakingNews,
  HtmlEmbedComponent,
  KoponyegComponent,
  KoponyegDefinitions,
  KoponyegType,
  LayoutComponent as KesmaLayoutComponent,
  LayoutContentItemWrapperTemplateData,
  LayoutContentParams,
  LayoutElementContentConfiguration,
  LayoutElementContentType,
  LayoutElementRow,
  LayoutPageType,
  MinuteToMinuteBlock,
  PAGE_TYPES,
  provideLayoutDataExtractors,
  RealEstateBazaarBlockComponent,
  RealEstateBazaarSearchBlockComponent,
  SponsoredBoxComponent,
} from '@trendency/kesma-ui';
import { fromEvent, map, Observable, of, startWith } from 'rxjs';
import { AgrokepService } from '../../services/agrokep.service';
import { UtilService } from '@trendency/kesma-core';
import {
  AgroData,
  ArticleCardType,
  ExchangeBoxComponent,
  ExternalBlogType,
  MinuteToMinuteService,
  NewsletterBoxType,
  RealEstateBazaarService,
  VgAgrokepComponent,
  VgArticleCardComponent,
  VgBlockTitleComponent,
  VgBrandingBoxExComponent,
  VgConferenceBoxComponent,
  VgDossierBoxComponent,
  VgDossierListComponent,
  VgExternalBlogComponent,
  VgGalleryBlockComponent,
  VgHeaderImageComponent,
  VgKnowledgeBoxComponent,
  VgLatestNewsComponent,
  VgMinuteByMinuteComponent,
  VgMostReadComponent,
  VgNewsFeedComponent,
  VgNewsletterBoxComponent,
  VgOpinionBlockComponent,
  VgPodcastBoxComponent,
  VgSponsoredContentComponent,
  VgVideoBlockComponent,
  VgWysiwygBoxComponent,
} from '../../../../shared';
import { AsyncPipe, NgIf } from '@angular/common';
import { VG_EXTRACTORS_CONFIG } from '../../extractors/extractor.config';
import { VariableSponsoredDidYouKnowWrapperComponent } from '../../../../shared/components/variable-sponsored-did-you-know-wrapper/variable-sponsored-did-you-know-wrapper.component';

@Component({
  selector: 'app-layout',
  templateUrl: './layout.component.html',
  styleUrls: ['./layout.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    ExchangeBoxComponent,
    VgBrandingBoxExComponent,
    AsyncPipe,
    VgArticleCardComponent,
    VgNewsletterBoxComponent,
    VgBlockTitleComponent,
    VgKnowledgeBoxComponent,
    VgMostReadComponent,
    VgLatestNewsComponent,
    VgGalleryBlockComponent,
    VgAgrokepComponent,
    VgExternalBlogComponent,
    VgSponsoredContentComponent,
    VgVideoBlockComponent,
    VgHeaderImageComponent,
    VgMinuteByMinuteComponent,
    VgConferenceBoxComponent,
    VgPodcastBoxComponent,
    VgOpinionBlockComponent,
    VgDossierBoxComponent,
    VgDossierListComponent,
    VgNewsFeedComponent,
    KesmaLayoutComponent,
    AdvertisementAdoceanComponent,
    KoponyegComponent,
    HtmlEmbedComponent,
    RealEstateBazaarSearchBlockComponent,
    RealEstateBazaarBlockComponent,
    VgWysiwygBoxComponent,
    SponsoredBoxComponent,
    VariableSponsoredDidYouKnowWrapperComponent,
  ],
  providers: [provideLayoutDataExtractors(VG_EXTRACTORS_CONFIG, true)],
})
export class LayoutComponent {
  @Input() adPageType = PAGE_TYPES.main_page;
  @Input() structure: LayoutElementRow[];
  @Input() configuration: LayoutElementContentConfiguration[];
  @Input() layoutType?: LayoutPageType;
  @Input() breakingNews: BreakingNews[] = [];
  @Input() contentComponentsWrapper: TemplateRef<LayoutContentItemWrapperTemplateData>;
  @Input() contentComponentsInnerWrapper: TemplateRef<LayoutContentItemWrapperTemplateData>;
  @Input() blockTitleWrapper: TemplateRef<BlockWrapperTemplateData>;
  @Input() editorFrameSize?: 'desktop' | 'mobile';

  @ViewChild('contentComponents', {
    read: TemplateRef,
    static: false,
  })
  contentComponents: TemplateRef<LayoutContentParams>;

  readonly LayoutPageTypes = LayoutPageType;
  readonly LayoutElementContentType = LayoutElementContentType;
  readonly NewsletterBoxType = NewsletterBoxType;
  readonly ArticleCardType = ArticleCardType;

  readonly KoponyegType: KoponyegType = KoponyegType.Light;
  readonly KoponyegDefinitions: KoponyegDefinitions = {
    width: 300,
    height: 100,
  };

  isMobile$ = this.utils.isBrowser()
    ? fromEvent(window, 'resize').pipe(
        map(() => window.innerWidth),
        startWith(window.innerWidth),
        map((width: number) => width <= 576)
      )
    : of(false);

  constructor(
    private readonly changeDetector: ChangeDetectorRef,
    private readonly minuteToMinuteService: MinuteToMinuteService,
    private readonly agrokepService: AgrokepService,
    private readonly utils: UtilService,
    public readonly realEstateBazaar: RealEstateBazaarService
  ) {}

  getBlockTitleData(layoutElement: LayoutElementRow, parentBackgroundColor: string): BlockTitle {
    return {
      ...layoutElement.blockTitle,
      text: layoutElement.blockTitle?.text || 'Blokk cím',
      isDark: !!layoutElement?.backgroundColor || !!parentBackgroundColor,
    } as BlockTitle;
  }

  handleRealEstateInitEvent(): void {
    this.realEstateBazaar.handleRealEstateInitEvent()?.subscribe(() => {
      this.changeDetector.detectChanges();
    });
  }

  getBlogData(layoutElement: any): { data: ArticleCard[]; styleID: ExternalBlogType } {
    const data = layoutElement?.config?.selectedBlogs.map((blog: any) =>
      blog?.data
        ? {
            ...blog.data,
            columnEmphasizeOnArticleCard: backendBoolToBool(blog.data?.columnEmphasizeOnArticleCard),
            hasGallery: backendBoolToBool(blog.data?.hasGallery),
            isAdultsOnly: backendBoolToBool(blog.data?.isAdultsOnly),
            isGalleryType: backendBoolToBool(blog.data?.isGalleryType),
            isInterviewType: backendBoolToBool(blog.data?.isInterviewType),
            isPodcastType: backendBoolToBool(blog.data?.isPodcastType),
            isVideoType: backendBoolToBool(blog.data?.isVideoType),
          }
        : {}
    );

    let styleID = ExternalBlogType.MNB;
    switch (layoutElement.styleId) {
      case 1:
        styleID = ExternalBlogType.MNB;
        break;
      case 2:
        styleID = ExternalBlogType.CENTURY;
        break;
      case 3:
        styleID = ExternalBlogType.INDICATOR;
        break;
    }

    return { data, styleID };
  }

  getMinuteToMinuteData(article: ArticleCard): Observable<MinuteToMinuteBlock[]> | undefined {
    if (!this.utils.isBrowser()) {
      return;
    }
    return this.minuteToMinuteService.getBlocks(article);
  }

  getAgrokepData(): Observable<AgroData[]> | undefined {
    return this.agrokepService.getAgrokepArticles();
  }
}
