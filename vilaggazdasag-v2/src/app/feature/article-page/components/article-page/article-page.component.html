<ng-container *ngIf="!isUserAdultChoice && article?.isAdultsOnly; else articleContent">
  <app-adult-content (isUserAdult)="onIsUserAdultChoose($event)"></app-adult-content>
</ng-container>

<ng-template #articleContent>
  <section class="article">
    <div *ngIf="showTypeHeader && article" class="wrapper-header">
      <app-article-opinion-header *ngIf="article.isOpinion && !article.sponsorship" [data]="article" [isMobileApp]="isMobileApp"></app-article-opinion-header>
      <app-article-minute-to-minute-header
        *ngIf="isMinuteByMinute && adverts && !article.sponsorship"
        [adverts]="adverts"
        [data]="article"
        [isMobileApp]="isMobileApp"
        [minuteToMinutes$]="minuteToMinutes$"
      ></app-article-minute-to-minute-header>
      <app-article-podcast-header
        *ngIf="article.isPodcastType && !article.sponsorship"
        [data]="article"
        [isMobileApp]="isMobileApp"
      ></app-article-podcast-header>
      <app-article-video-header *ngIf="article.isVideoType && !article.sponsorship" [data]="article" [isMobileApp]="isMobileApp"></app-article-video-header>
      <app-article-interview-header
        *ngIf="adverts && article.isInterviewType && !article.sponsorship"
        [adverts]="adverts"
        [data]="article"
        [isMobileApp]="isMobileApp"
      ></app-article-interview-header>
      <app-article-sponsored-header *ngIf="article?.sponsorship" [data]="article" [isMobileApp]="isMobileApp"></app-article-sponsored-header>
    </div>
    <div class="wrapper with-aside">
      <div [ngClass]="{ 'left-column': !foundationTagSlug }">
        <app-article-header
          *ngIf="article && !showTypeHeader"
          [data]="article"
          [foundationTagSlug]="foundationTagSlug"
          [foundationTagTitle]="foundationTagTitle"
          [isMobileApp]="isMobileApp"
        ></app-article-header>

        <kesma-advertisement-adocean
          *ngIf="!article?.sponsorship && adverts?.desktop?.['roadblock_1'] as ad"
          [isExceptionAdvertEnabled]="isExceptionAdvertEnabled"
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
            background: 'var(--ad-bg-light)',
            padding: 'var(--ad-padding)',
          }"
        >
        </kesma-advertisement-adocean>
        <kesma-advertisement-adocean
          *ngIf="!article?.sponsorship && adverts?.mobile?.['mobilrectangle_1'] as ad"
          [isExceptionAdvertEnabled]="isExceptionAdvertEnabled"
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
            background: 'var(--ad-bg-light)',
            padding: 'var(--ad-padding)',
          }"
        >
        </kesma-advertisement-adocean>

        <ng-container [ngTemplateOutletContext]="{ body: article?.body }" [ngTemplateOutlet]="bodyContent"></ng-container>
        <div *ngIf="isMinuteByMinute" class="minute-by-minute-blocks">
          <ng-container *ngFor="let block of (minuteToMinutes$ | async) ?? []; let index = index">
            <div class="minute-by-minute-block">
              <!-- Scroll Target: -->
              <div class="scroll-target-wrapper">
                <div [id]="'pp-' + block.id"></div>
              </div>
              <div class="minute-by-minute-block-meta">
                <div class="minute-by-minute-block-meta-date">
                  <div *ngIf="article?.minuteToMinute === MinuteToMinuteState.RUNNING">{{ block.date | publishDate }} frissült</div>
                  <div *ngIf="article?.minuteToMinute === MinuteToMinuteState.RUNNING" class="separator"></div>
                  <div>
                    {{ block.date | formatDate: 'y-l-d-h-m' }}
                  </div>
                </div>
              </div>
              <h2 *ngIf="block?.title" class="minute-by-minute-block-title">{{ block?.title }}</h2>
              <ng-container *ngFor="let element of block.body" [ngSwitch]="element.type">
                <ng-container *ngSwitchCase="ArticleBodyType.Wysywyg">
                  <ng-container *ngFor="let wysiwygDetail of element?.details">
                    <vg-wysiwyg-box [html]="wysiwygDetail?.value || ''" [removeScriptsOnSsr]="false" trArticleFileLink></vg-wysiwyg-box>
                  </ng-container>
                </ng-container>
              </ng-container>
              <div *ngIf="!isMobileApp" class="minute-by-minute-block-share" data-skip-on-mobile-app>
                <span *ngIf="(isMobile$ | async) !== true" class="minute-by-minute-block-share-text">Megosztás</span>
                <app-social-share
                  [emailSubject]="block.title || ''"
                  [isNewsFeedType]="(isMobile$ | async) === true"
                  [isOutline]="true"
                  [linkToCopy]="getMinuteByMinuteShareUrl(block.id)"
                  [smallPopup]="(isMobile$ | async) !== true"
                ></app-social-share>
              </div>
            </div>
            <ng-container *ngIf="index === 0 || index % 5 === 4">
              <kesma-advertisement-adocean
                *ngIf="adverts?.desktop?.['roadblock_2'] as ad"
                [ad]="ad"
                [style]="{
                  margin: 'var(--ad-margin)',
                  background: 'var(--ad-bg-light)',
                  padding: 'var(--ad-padding)',
                }"
              >
              </kesma-advertisement-adocean>

              <kesma-advertisement-adocean
                *ngIf="adverts?.mobile?.['mobilrectangle_2'] as ad"
                [ad]="ad"
                [style]="{
                  margin: 'var(--ad-margin)',
                  background: 'var(--ad-bg-light)',
                  padding: 'var(--ad-padding)',
                }"
              >
              </kesma-advertisement-adocean>
            </ng-container>

            <vg-newsletter-box *ngIf="!isMobileApp && index === 0" [styleID]="NewsletterBoxType.Gong" data-skip-on-mobile-app></vg-newsletter-box>
          </ng-container>
        </div>

        <div #dataTrigger *ngIf="article"></div>

        <div *ngIf="!isMobileApp" data-skip-on-mobile-app>
          @if (sponsoredTag) {
            <app-sponsored-tag-box [sponsoredTag]="sponsoredTag" [excludedSlug]="article?.slug" />
          }
          <ng-container *ngIf="article?.isOpinion">
            <app-author-more-articles [data]="authorArticles"></app-author-more-articles>
            <app-opinion-articles [data]="opinionArticles"></app-opinion-articles>
          </ng-container>

          <ng-container *ngIf="article?.isVideoType">
            <app-video-more-articles [data]="videoArticles"></app-video-more-articles>
          </ng-container>

          <ng-container *ngIf="article?.isPodcastType">
            <vg-podcast-box [data]="podcastArticles" [isLight]="true" [isPodcastBoxOnArticlePage]="true"></vg-podcast-box>
          </ng-container>

          <kesma-nativterelo [tereloUrl]="tereloUrl"></kesma-nativterelo>

          <ng-container *ngIf="!article?.sponsorship">
            <div #externalRecommendationsBlock></div>
            <!-- Data trigger -->
            <app-ottbox
              [data]="externalRecommendations"
              [mobilrectangle_ottboxextra]="adverts?.mobile?.mobilrectangle_ottboxextra"
              [roadblock_ottboxextra]="adverts?.desktop?.roadblock_ottboxextra"
            ></app-ottbox>
          </ng-container>

          <vg-newsletter-box [styleID]="NewsletterBoxType.SocialSorted"></vg-newsletter-box>

          <ng-container *ngIf="!article?.sponsorship">
            <kesma-advertisement-adocean
              *ngIf="adverts?.desktop?.['roadblock_2'] as ad"
              [ad]="ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg-light)',
                padding: 'var(--ad-padding)',
              }"
            >
            </kesma-advertisement-adocean>

            <kesma-advertisement-adocean
              *ngIf="adverts?.mobile?.['mobilrectangle_2'] as ad"
              [ad]="ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg-light)',
                padding: 'var(--ad-padding)',
              }"
            >
            </kesma-advertisement-adocean>
          </ng-container>

          <ng-container *ngFor="let pr_ad of mobile_pr_cikkfix">
            <kesma-advertisement-adocean
              *ngIf="pr_ad as ad"
              [ad]="ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg-light)',
                padding: 'var(--ad-padding)',
              }"
            ></kesma-advertisement-adocean>
          </ng-container>

          <app-recommendation-box *ngIf="!foundationTagSlug" [recommendations$]="recommendations$"></app-recommendation-box>

          <app-foundation-recommendation
            *ngIf="foundationTagSlug && articleSlug"
            [articleSlug]="articleSlug"
            [foundationTagSlug]="foundationTagSlug"
            [tags]="article?.tags ?? []"
          ></app-foundation-recommendation>

          <ng-container *ngIf="!article?.sponsorship">
            <kesma-advertisement-adocean
              *ngIf="adverts?.desktop?.['roadblock_3'] as ad"
              [ad]="ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg-light)',
                padding: 'var(--ad-padding)',
              }"
            >
            </kesma-advertisement-adocean>
            <kesma-advertisement-adocean
              *ngIf="adverts?.mobile?.['mobilrectangle_3'] as ad"
              [ad]="ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg-light)',
                padding: 'var(--ad-padding)',
              }"
            >
            </kesma-advertisement-adocean>
          </ng-container>
        </div>
      </div>
      <aside *ngIf="!isMobileApp && !foundationTagSlug" data-skip-on-mobile-app>
        <app-sidebar [articleId]="article?.id" [articleSlug]="articleSlug" [categorySlug]="article?.primaryColumn?.slug"></app-sidebar>
      </aside>
    </div>
  </section>
</ng-template>

<ng-template #bodyContent let-body="body">
  <ng-container *ngFor="let element of body">
    <ng-container [ngSwitch]="element.type">
      <ng-container *ngSwitchCase="ArticleBodyType.Wysywyg">
        <ng-container *ngFor="let wysiwygDetail of element?.details">
          <vg-wysiwyg-box [html]="wysiwygDetail?.value || ''" trArticleFileLink></vg-wysiwyg-box>
        </ng-container>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.NewsletterSignUp">
        <vg-newsletter-box *ngIf="!isMobileApp" [isListPageView]="true" [styleID]="NewsletterBoxType.Basic" data-skip-on-mobile-app></vg-newsletter-box>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.NewsletterGongSignUp">
        <vg-newsletter-box *ngIf="!isMobileApp" [isListPageView]="true" [styleID]="NewsletterBoxType.Gong" data-skip-on-mobile-app></vg-newsletter-box>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.TenArticle">
        <app-article-related-content [data]="getTenArticleRecommenderArticles(element)" [withImage]="false"></app-article-related-content>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.TwoArticle">
        <app-article-related-content [data]="twoArticle(element.details)" [withImage]="true"></app-article-related-content>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.DoubleArticleRecommendations">
        <div *ngIf="doubleArticleTitle(element.details)" class="double-title">{{ doubleArticleTitle(element.details).value }}</div>
        <div *ngIf="doubleArticleLead(element.details)" class="double-lead">{{ doubleArticleLead(element.details).value }}</div>
        <div class="double-recommendations">
          <ng-container *ngFor="let article of doubleArticleRecommendations(element.details); let i = index; trackBy: trackByFn">
            <vg-article-card
              [data]="doubleArticleRecommendation(article.value)"
              [displayLinkIcon]="true"
              [hasOutline]="true"
              [styleID]="ArticleCardType.RelatedArticle"
            >
            </vg-article-card>
          </ng-container>
        </div>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.GraphData">
        <kesma-chart [data]="element.details[0].value" />
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.Voting">
        @if (voteCache[element.details?.[0]?.value?.id ?? ''] | async; as voteData) {
          <vg-voting (vote)="onVotingSubmit($event, voteData)" [data]="voteData.data" [sponsored]="voteData?.data?.sponsorship" [voteId]="voteData.votedId" />
        }
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.Gallery">
        <vg-vg-article-slider-gallery
          (fullscreenLayerClicked)="openGalleryDedicatedRouteLayer($event)"
          (slideChanged)="handleGallerySlideChange(galleriesData[element?.details[0]?.value?.id], $event)"
          *ngIf="galleriesData[element?.details[0]?.value?.id]"
          [data]="galleriesData[element?.details[0]?.value?.id]"
          [highlightedImageId]="element?.details[1]?.value?.id"
          [isInsideAdultArticleBody]="article?.isAdultsOnly"
        ></vg-vg-article-slider-gallery>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.Stock">
        <app-exchange-box *ngIf="!isMobileApp" [styleId]="ExchangeBoxType.ChartAndTableRow" data-skip-on-mobile-app></app-exchange-box>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.SubsequentDossier">
        <vg-dossier-recommender
          *ngIf="!isMobileApp"
          [articlesCount]="element?.articleCount"
          [data]="dossierRecommender(element?.details[0]?.value)"
          data-skip-on-mobile-app
        ></vg-dossier-recommender>
      </ng-container>

      <ng-container *ngIf="!article?.sponsorship">
        <ng-container *ngSwitchCase="ArticleBodyType.Advert">
          <kesma-advertisement-adocean
            *ngIf="adverts?.mobile?.[element.adverts.mobile] as ad"
            [ad]="ad"
            [style]="{
              margin: 'var(--ad-margin)',
              background: 'var(--ad-bg-light)',
              padding: 'var(--ad-padding)',
            }"
          ></kesma-advertisement-adocean>
          <kesma-advertisement-adocean
            *ngIf="adverts?.desktop?.[element.adverts.desktop] as ad"
            [ad]="ad"
            [style]="{
              margin: 'var(--ad-margin)',
              background: 'var(--ad-bg-light)',
              padding: 'var(--ad-padding)',
            }"
          >
          </kesma-advertisement-adocean>
        </ng-container>
      </ng-container>
    </ng-container>
  </ng-container>
</ng-template>
