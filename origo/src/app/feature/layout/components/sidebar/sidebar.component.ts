import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { EmbeddingService, UtilService } from '@trendency/kesma-core';
import {
  ArticleCard,
  Layout,
  LayoutContent,
  LayoutElementRow,
  LayoutPageType,
  LayoutService,
  PAGE_TYPES,
  SecondaryFilterAdvertType,
} from '@trendency/kesma-ui';
import { forkJoin, of } from 'rxjs';
import { catchError, mergeMap, tap } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { LayoutComponent } from '../layout/layout.component';
import { NgIf } from '@angular/common';
import { ApiService, OrigoSportService } from '../../../../shared';

const MAXIMUM_SHORT_ARTICLE_SIDEBAR_COMP_COUNT = 7;

@Component({
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, LayoutComponent],
})
export class SidebarComponent implements OnInit, OnChanges, AfterViewInit {
  @Input() adPageType = PAGE_TYPES.all_articles_and_sub_pages;
  @Input() hideAds = false;
  @Input() categorySlug?: string;
  @Input() articleId?: string;
  @Input() articleSlug?: string;
  @Input() columnSlug?: string;
  @Input() excludedIds: string[] = [];
  @Input() isShortNews: boolean | undefined = false;
  @Output() populatedEvent = new EventEmitter<boolean>();
  @ViewChild('layout') layoutComponent: LayoutComponent;
  layoutApiData?: Layout;
  layoutReady = false;

  secondary_pageType = this.origoSportService.isSport ? PAGE_TYPES.column_sport_all_articles_and_sub_pages : PAGE_TYPES.all_articles_and_sub_pages;

  secondaryFilterType = SecondaryFilterAdvertType.REPLACEABLE_WITH_ABSOLUTE;

  LayoutPageType = LayoutPageType;

  constructor(
    private readonly api: ApiService,
    private readonly layoutService: LayoutService,
    private readonly embedding: EmbeddingService,
    private readonly utilsService: UtilService,
    private readonly changeRef: ChangeDetectorRef,
    private readonly origoSportService: OrigoSportService
  ) {}

  ngOnInit(): void {
    this.populateSidebar();
  }

  get structure(): LayoutElementRow[] {
    if (this.isShortNews) {
      return this.layoutApiData?.struct[0].elements[0].elements.slice(0, MAXIMUM_SHORT_ARTICLE_SIDEBAR_COMP_COUNT) as LayoutElementRow[];
    } else {
      return this.layoutApiData?.struct as LayoutElementRow[];
    }
  }

  populateSidebar(): void {
    const allExcludedId = [...(this.articleId ? [this.articleId] : []), ...this.excludedIds];

    this.layoutService
      .getSidebar(this.categorySlug, this.articleSlug, this.origoSportService.isSport)
      .pipe(
        tap(() => {
          this.layoutApiData = undefined;
          this.changeRef.detectChanges();
        }),
        mergeMap((layoutApiResponse) =>
          forkJoin([
            of(layoutApiResponse),
            this.api.getSidebarArticleRecommendations(this.getArticleCount(layoutApiResponse?.data?.content), this.categorySlug, allExcludedId).pipe(
              catchError((error, _) => {
                if (!environment.production && this.utilsService.isBrowser()) {
                  console.warn('[dev] Unable to download sidebar: ', error);
                }
                return [];
              })
            ),
          ])
        )
      )
      .subscribe(([{ data: layoutApiData }, { data: recommendedArticles }]) => {
        this.layoutApiData = layoutApiData;
        this.fillLayoutContent(recommendedArticles);
        this.layoutReady = true;
        this.changeRef.detectChanges();
        setTimeout(() => {
          this.embedding.loadEmbedMedia();
        }, 0);
        this.populatedEvent.emit(true);
        this.changeRef.detectChanges();
      });
  }

  ngAfterViewInit(): void {
    setTimeout(() => {
      this.embedding.loadEmbedMedia();
    }, 0);
  }

  ngOnChanges(changes: SimpleChanges): void {
    // need sidebar refetch to avoid showing the same article in sidebar that is currently displayed on detail page
    if (
      (changes['articleId']?.currentValue && !changes['articleId']?.firstChange) ||
      (changes['excludedIds']?.currentValue && !changes['excludedIds']?.firstChange)
    ) {
      this.populateSidebar();
    }
  }

  getArticleCount(content: LayoutContent[]): number {
    // no flat() ??? -> upgrade to es2019
    return content.map(({ selectedArticles }) => selectedArticles).reduce((acc, val) => acc.concat(val), []).length;
  }

  fillLayoutContent(articles: ArticleCard[]): void {
    let articleCursor = 0;
    this.layoutApiData?.content.forEach(({ selectedArticles }) => {
      if (selectedArticles?.length) {
        for (let i = 0; i < selectedArticles.length; i++) {
          // only overwrite null values
          if (!selectedArticles[i] && articles[articleCursor]) {
            selectedArticles[i] = {
              id: articles[articleCursor]?.id ?? '',
              // TODO: mismatching types????
              data: articles[articleCursor] as any,
            };
            articleCursor++;
          }
        }
      }
    });
  }
}
