{"name": "origo", "author": "Trendency", "description": "Friss hĂ­rek MagyarorszĂˇgrĂłl Ă©s a nagyvilĂˇgbĂłl, sport, technika, szĂłrakozĂˇs.", "license": "proprietary", "repository": "", "version": "3.5.0", "scripts": {"ng": "ng", "start": "ng serve", "start-with-proxy": "ng serve --proxy-config proxy.conf.js", "build": "ng build", "watch": "ng build --watch --configuration development", "watch:local": "ng build --watch --configuration local-ssr", "serve:ssr": "node dist/server/server.mjs", "lint": "npx eslint \"src/**/*.{js,ts,html}\" --quiet --fix", "format": "npx prettier --config ./.prettierrc \"src/**/*.{js,ts,html,scss}\" --write", "build:ssr-local": "ng build origo --configuration local-ssr", "build:ssr-dev": "ng build origo --configuration development", "build:ssr-test": "ng build origo --configuration test", "build:ssr-prod": "ng build origo --configuration production", "prepare": "husky"}, "private": true, "dependencies": {"@angular/animations": "^19.2.1", "@angular/cdk": "^19.2.1", "@angular/common": "^19.2.1", "@angular/compiler": "^19.2.1", "@angular/core": "^19.2.1", "@angular/forms": "^19.2.1", "@angular/platform-browser": "^19.2.1", "@angular/platform-browser-dynamic": "^19.2.1", "@angular/platform-server": "^19.2.1", "@angular/router": "^19.2.1", "@angular/ssr": "^19.2.1", "@ng-select/ng-select": "^14.2.3", "@trendency/kesma-core": "3.1.6", "@trendency/kesma-ui": "3.30.0", "angular-google-tag-manager": "^1.11.0", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "express": "^4.18.2", "flatpickr": "^4.6.13", "http-proxy-middleware": "^3.0.3", "https-proxy-agent": "^7.0.6", "lodash-es": "^4.17.21", "ngx-captcha": "^13.0.0", "ngx-date-fns": "^12.0.0", "rxjs": "^7.8.2", "swiper": "^11.2.5", "tslib": "^2.8.1", "zone.js": "~0.15.0", "chart.js": "^4.4.9"}, "devDependencies": {"@angular-eslint/builder": "^19.2.1", "@angular-eslint/eslint-plugin": "^19.2.1", "@angular-eslint/eslint-plugin-template": "^19.2.1", "@angular-eslint/schematics": "^19.2.1", "@angular-eslint/template-parser": "^19.2.1", "@angular/build": "^19.2.1", "@angular/cli": "^19.2.1", "@angular/compiler-cli": "^19.2.1", "@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@types/express": "^4.17.17", "@types/google.analytics": "^0.0.46", "@types/lodash-es": "^4.17.12", "@types/node": "^22.13.10", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "bootstrap": "^5.3.3", "browser-sync": "^3.0.3", "check-engine": "^1.14.0", "eslint": "^8.57.0", "eslint-plugin-functional": "^6.2.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-rxjs": "^5.0.2", "husky": "^9.1.7", "lint-staged": "^15.4.3", "prettier": "^3.5.3", "typescript": "~5.5.4"}, "browser": {"fs": false}, "engines": {"node": "^18.13.0 || ^20.9.0", "npm": ">=9.0.0"}}