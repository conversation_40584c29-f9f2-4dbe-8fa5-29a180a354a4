import { ChangeDetectionStrategy, ChangeDetectorRef, Component, computed, inject, input, signal, TemplateRef, ViewChild } from '@angular/core';
import {
  AdvertisementAdoceanComponent,
  BlockWrapperTemplateData,
  BreakingNews,
  HtmlEmbedComponent,
  LayoutComponent as KesmaLayoutComponent,
  LayoutContentItemWrapperTemplateData,
  LayoutContentParams,
  LayoutElementContentConfiguration,
  LayoutElementContentType,
  LayoutElementRow,
  LayoutPageType,
  mapRealEstateApiDataToRealEstateData,
  provideLayoutDataExtractors,
  RealEstateBazaarApiData,
  RealEstateBazaarBackendResponse,
  RealEstateBazaarBlockComponent,
  RealEstateBazaarData,
  RealEstateBazaarSearchBlockComponent,
  SponsoredBoxComponent,
  VoteDataWithAnswer,
  VoteService,
} from '@trendency/kesma-ui';
import { HttpClient } from '@angular/common/http';
import {
  ArticleCardComponent,
  AstronetBrandingBoxComponent,
  AstronetColumnsComponent,
  AstronetFortuneComponent,
  AstronetHoroscopeComponent,
  BlockTitleRowComponent,
  BrandingBoxExComponent,
  ColumnBlockComponent,
  DossierCardComponent,
  DossierCardType,
  FreshArticleCardComponent,
  GalleryListComponent,
  ImageComponent,
  LatestAndMostReadArticlesComponent,
  MinuteByMinuteComponent,
  MostViewedComponent,
  NewsletterBlockComponent,
  StarBirthsComponent,
  SubColumnsBoxComponent,
  TopicSuggestionComponent,
  TopTenBoxComponent,
  VideoArticlesComponent,
  VotingComponent,
  WysiwygBoxComponent,
} from '../../../../shared';
import { BORS_EXTRACTORS_CONFIG } from '../../extractors/extractors.config';
import { AsyncPipe } from '@angular/common';
import { UtilService } from '@trendency/kesma-core';
import { TopCommentedArticlesComponent } from '../../../../shared/components/top-commented-articles/top-commented-articles.component';
import { VariableSponsoredDidYouKnowWrapperComponent } from '../../../../shared/components/variable-sponsored-did-you-know-wrapper/variable-sponsored-did-you-know-wrapper.component';

@Component({
  selector: 'app-layout',
  templateUrl: './layout.component.html',
  styleUrls: ['./layout.component.scss'],
  providers: [provideLayoutDataExtractors(BORS_EXTRACTORS_CONFIG, true)],
  imports: [
    RealEstateBazaarBlockComponent,
    RealEstateBazaarSearchBlockComponent,
    HtmlEmbedComponent,
    AdvertisementAdoceanComponent,
    KesmaLayoutComponent,
    ArticleCardComponent,
    GalleryListComponent,
    WysiwygBoxComponent,
    DossierCardComponent,
    ImageComponent,
    VotingComponent,
    AstronetFortuneComponent,
    FreshArticleCardComponent,
    AstronetHoroscopeComponent,
    BrandingBoxExComponent,
    VideoArticlesComponent,
    NewsletterBlockComponent,
    StarBirthsComponent,
    TopTenBoxComponent,
    ColumnBlockComponent,
    BlockTitleRowComponent,
    MostViewedComponent,
    LatestAndMostReadArticlesComponent,
    MinuteByMinuteComponent,
    AstronetBrandingBoxComponent,
    AstronetColumnsComponent,
    AsyncPipe,
    SponsoredBoxComponent,
    SubColumnsBoxComponent,
    TopicSuggestionComponent,
    TopCommentedArticlesComponent,
    VariableSponsoredDidYouKnowWrapperComponent,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LayoutComponent {
  private readonly httpClient = inject(HttpClient);
  private readonly voteService = inject(VoteService);
  private readonly changeDetector = inject(ChangeDetectorRef);
  private readonly utilService = inject(UtilService);

  readonly votingCache = this.voteService.voteCache;
  readonly isMobile = this.utilService.isBrowser() ? window.innerWidth < 992 : false;

  adPageType = input('');
  structure = input.required<LayoutElementRow[]>();
  configuration = input.required<LayoutElementContentConfiguration[]>();
  layoutType = input.required<LayoutPageType>();
  breakingNews = input<BreakingNews[]>([]);
  contentComponentsWrapper = input<TemplateRef<LayoutContentItemWrapperTemplateData>>();
  contentComponentsInnerWrapper = input<TemplateRef<LayoutContentItemWrapperTemplateData>>();
  blockTitleWrapper = input<TemplateRef<BlockWrapperTemplateData>>();
  isInIframe = input<boolean>(false);
  editorFrameSize = input<'desktop' | 'mobile'>();

  vm = computed(() => {
    return {
      adPageType: this.adPageType(),
      structure: this.structure(),
      configuration: this.configuration(),
      layoutType: this.layoutType(),
      breakingNews: this.breakingNews(),
      realEstateData: this.realEstateData(),
      realEstateDataLoading: this.realEstateDataLoading(),
    };
  });

  @ViewChild('contentComponents', {
    read: TemplateRef,
    static: false,
  })
  contentComponents: TemplateRef<LayoutContentParams>;

  readonly LayoutElementContentType = LayoutElementContentType;
  readonly LayoutPageType = LayoutPageType;
  readonly DossierCardType = DossierCardType;

  private readonly realEstateData = signal<RealEstateBazaarData[]>([]);
  private readonly realEstateDataLoading = signal(false);

  handleRealEstateInitEvent(): void {
    if (!this.realEstateDataLoading && this.realEstateData.length < 1) {
      this.getRealEstateData();
    }
  }

  getRealEstateData(): void {
    this.realEstateDataLoading.set(true);
    const realEstates: Array<RealEstateBazaarData> = [];
    this.httpClient
      .get(
        // eslint-disable-next-line max-len
        'https://www.ingatlanbazar.hu/api/property-search?property_location=6,1000000004,1000000005,1000000006,1000000007&amp;;property_newbuildonly=on&amp;property__2=3_2'
      )
      .subscribe((data: RealEstateBazaarBackendResponse) => {
        data?.hits?.forEach((realEstate: RealEstateBazaarApiData) => {
          realEstates.push(mapRealEstateApiDataToRealEstateData(realEstate));
        });
        this.realEstateData.set(realEstates);
        this.realEstateDataLoading.set(false);
      });
  }

  onVotingSubmit(userVote: string, voteData: VoteDataWithAnswer): void {
    if (voteData.votedId || voteData.showResults) {
      return;
    }

    this.voteService.onVotingSubmit(userVote, voteData).subscribe(() => this.changeDetector.detectChanges());
  }
}
