import { AfterViewInit, ChangeDetectionStrategy, Component, computed, inject, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { map } from 'rxjs/operators';
import { toSignal } from '@angular/core/rxjs-interop';
import {
  ArticleCardComponent,
  ArticleCardType,
  BreadcrumbComponent,
  createBorsOnlineTitle,
  DEFAULT_BREADCRUMB_ITEM,
  defaultMetaInfo,
  makeBreadcrumbSchema,
  PagerComponent,
  PageTitleComponent,
  SearchFilterComponent,
} from '../../../shared';
import { SidebarComponent } from '../../layout/components/sidebar/sidebar.component';
import { IMetaData, SchemaOrgService, SeoService } from '@trendency/kesma-core';
import { getNewestArticleThumbnail } from '../../../shared/utils/meta.utils';
import { createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';

@Component({
  selector: 'app-fresh-news',
  templateUrl: './fresh-news.component.html',
  styleUrls: ['./fresh-news.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ArticleCardComponent, SidebarComponent, BreadcrumbComponent, PagerComponent, SearchFilterComponent, PageTitleComponent],
})
export class FreshNewsComponent implements AfterViewInit, OnInit {
  private readonly route = inject(ActivatedRoute);
  private readonly seo = inject(SeoService);
  private readonly schemaService = inject(SchemaOrgService);
  routeData = toSignal(this.route.data.pipe(map(({ data }) => data)));
  articleCards = computed(() => this.routeData().data);
  mainArticleCard = computed(() => this.articleCards()[0]);
  smallArticleCards = computed(() => this.articleCards().slice(1));
  limitable = computed(() => this.routeData().meta.limitable);
  readonly currentPage = computed(() =>
    this.routeData()?.category?.meta?.limitable?.pageCurrent ? this.routeData()!.category.meta.limitable.pageCurrent + 1 : 1
  );
  protected readonly ArticleCardType = ArticleCardType;

  ngOnInit(): void {
    this.setMetaData();
  }

  ngAfterViewInit(): void {
    const breadcrumbSchema = makeBreadcrumbSchema([DEFAULT_BREADCRUMB_ITEM]);
    this.schemaService.removeStructuredData();
    this.schemaService.insertSchema(breadcrumbSchema);
  }

  private setMetaData(): void {
    const plainTitle = 'Legfrissebb hírek';
    const title = createBorsOnlineTitle(plainTitle);
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      ogImage: getNewestArticleThumbnail(this.articleCards(), this.seo.hostUrl),
      robots: 'noindex, follow',
    };
    this.seo.setMetaData(metaData);
    const canonical = createCanonicalUrlForPageablePage('friss-hirek', this.route.snapshot);
    if (canonical) {
      this.seo.updateCanonicalUrl(canonical);
    }
  }
}
