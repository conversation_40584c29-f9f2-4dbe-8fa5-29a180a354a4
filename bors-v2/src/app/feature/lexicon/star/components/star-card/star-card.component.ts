import { ChangeDetectionStrategy, Component, HostBinding, input, Input } from '@angular/core';
import { BaseComponent } from '@trendency/kesma-ui';
import { NgTemplateOutlet } from '@angular/common';
import { RouterLink } from '@angular/router';
import { StarLinkComponent } from '../star-link/star-link.component';
import { IStarCard, IStarLink, StarCardTypes, StarLinkTypes } from '../../definitions/star.definitions';

@Component({
  selector: 'app-star-card',
  imports: [NgTemplateOutlet, RouterLink, StarLinkComponent],
  templateUrl: './star-card.component.html',
  styleUrl: './star-card.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StarCardComponent extends BaseComponent<IStarCard> {
  @HostBinding('class') hostClass: string;

  protected readonly IStarCardTypes = StarCardTypes;
  protected readonly StarLinkTypes = StarLinkTypes;

  _styleId: StarCardTypes;
  starLink: string;
  displayName: string;

  readonly imgLoadingType = input<string>('eager');

  get buttonData(): IStarLink {
    return {
      url: `/lexikon/sztar/${this.data?.slug}`,
      text: 'Tovább',
    };
  }

  get styleId(): StarCardTypes {
    return this._styleId;
  }

  @Input() set styleId(styleId: StarCardTypes) {
    this._styleId = styleId;
    this.hostClass = `style-${styleId}`;
  }

  protected override setProperties(): void {
    this.starLink = `/lexikon/sztar/${this.data?.slug}`;
    this.displayName = this.data?.[this.data?.displayContextName];
  }
}
