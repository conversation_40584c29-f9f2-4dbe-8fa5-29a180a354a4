@switch (styleId) {
  @case (IStarCardTypes.FEATURED) {
    <ng-container [ngTemplateOutlet]="celebImage" [ngTemplateOutletContext]="{ img: data?.profileImage?.detail }"></ng-container>

    <div class="star-card-details">
      <ng-container [ngTemplateOutlet]="celebName" [ngTemplateOutletContext]="{ name: displayName }"></ng-container>
      <ng-container [ngTemplateOutlet]="celebTags" [ngTemplateOutletContext]="{ tags: data?.occupations }"></ng-container>
      <ng-container [ngTemplateOutlet]="celebDescription" [ngTemplateOutletContext]="{ description: data?.lead }"></ng-container>
      <app-star-link [data]="buttonData" [styleId]="StarLinkTypes.LINE_LEFT"></app-star-link>
    </div>
  }
  @case (IStarCardTypes.LIST) {
    <ng-container [ngTemplateOutlet]="celebImage" [ngTemplateOutletContext]="{ img: data?.profileImage?.thumbnail }"></ng-container>

    <div class="star-card-details">
      <ng-container [ngTemplateOutlet]="celebName" [ngTemplateOutletContext]="{ name: displayName }"></ng-container>
      <ng-container [ngTemplateOutlet]="celebTags" [ngTemplateOutletContext]="{ tags: data?.occupations }"></ng-container>
      <ng-container [ngTemplateOutlet]="celebDescription" [ngTemplateOutletContext]="{ description: data?.lead }"></ng-container>
      <app-star-link [data]="buttonData" [styleId]="StarLinkTypes.LINE_LEFT"></app-star-link>
    </div>

    <div class="mobile-only">
      <div class="mobile-only-header">
        <ng-container [ngTemplateOutlet]="celebImage" [ngTemplateOutletContext]="{ img: data?.profileImage?.thumbnail }"></ng-container>

        <div class="star-card-details">
          <ng-container [ngTemplateOutlet]="celebName" [ngTemplateOutletContext]="{ name: displayName }"></ng-container>
          <ng-container [ngTemplateOutlet]="celebTags" [ngTemplateOutletContext]="{ tags: data?.occupations }"></ng-container>
        </div>
      </div>

      <ng-container [ngTemplateOutlet]="celebDescription" [ngTemplateOutletContext]="{ description: data?.lead }"></ng-container>
      <app-star-link [data]="buttonData" [styleId]="StarLinkTypes.LINE_LEFT"></app-star-link>
    </div>
  }
  @case (IStarCardTypes.SIMPLE) {
    <ng-container [ngTemplateOutlet]="celebImage" [ngTemplateOutletContext]="{ img: data?.profileImage?.list }"></ng-container>
    <ng-container [ngTemplateOutlet]="celebName" [ngTemplateOutletContext]="{ name: displayName }"></ng-container>
  }
  @case (IStarCardTypes.SIMPLE_TAG) {
    <ng-container [ngTemplateOutlet]="celebImage" [ngTemplateOutletContext]="{ img: data?.profileImage?.list }"></ng-container>
    <ng-container [ngTemplateOutlet]="celebName" [ngTemplateOutletContext]="{ name: displayName }"></ng-container>
    <ng-container [ngTemplateOutlet]="celebTags" [ngTemplateOutletContext]="{ tags: data?.occupations }"></ng-container>
  }
}

<ng-template #celebImage let-img="img">
  <a [routerLink]="starLink" class="star-card-img-wrapper">
    <img [alt]="displayName + ' képe'" [src]="img" class="star-card-img" [loading]="imgLoadingType()" />
  </a>
</ng-template>

<ng-template #celebName let-name="name">
  <a [routerLink]="starLink" class="star-card-name">{{ name }}</a>
</ng-template>

<ng-template #celebTags let-tags="tags">
  <div class="star-card-tags">
    @for (tag of tags; track tag.slug) {
      <a [routerLink]="['/', 'lexikon', 'sztar', 'kereso']" [queryParams]="{ 'occupations_filter[]': tag.slug }" class="star-card-tag">{{ tag.title }}</a>
    }
  </div>
</ng-template>

<ng-template #celebDescription let-description="description">
  <div class="star-card-description">{{ description }}</div>
</ng-template>
