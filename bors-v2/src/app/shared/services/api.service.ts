import { inject, Injectable } from '@angular/core';
import { IHttpOptions, ReqService } from '@trendency/kesma-core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import {
  Advertisement,
  ApiListResult,
  ApiResponseMetaList,
  ApiResult,
  ArticleCard,
  ArticleCategoryParams,
  ArticleSearchResult,
  BackendArticleSearchResult,
  BackendComment,
  BackendGalleryDetails,
  BasicDossier,
  buildMenuItem,
  GalleryData,
  GalleryDetails,
  InitResponse,
  Layout,
  mapBackendArticleDataToArticleCard,
  mapBackendGalleryDetailsResultToGalleryDetails,
  MenuTreeResponse,
  PortalBasedMenuLinkOverride,
  PortfolioResponse,
  SimplifiedMenuTree,
  VariableDidYouKnowBox,
} from '@trendency/kesma-ui';
import { BackendAllowedLoginMethodsResponse, BackendUserLoginResponse, LoginFormData, RegistrationFormData } from '../definitions';
import {
  loginFormDataToBackendRequest,
  registrationFormDataToBackendRequest,
  requestPasswordResetDataToBackendRequest,
  resetPasswordDataToBackendRequest,
} from '../utils';
import { BackendArticleSocial, CommentableType, CommentRequestParams, HistoricalComment } from '../../feature/comments/api/comment.definitions';
import { getCommentOrderParam } from '../../feature/comments/utils/comment.utils';

@Injectable({
  providedIn: 'root',
})
export class ApiService {
  private readonly reqService = inject(ReqService);

  init(): Observable<ApiResult<InitResponse>> {
    return this.reqService.get('/init');
  }

  getMenu(overrides?: PortalBasedMenuLinkOverride): Observable<SimplifiedMenuTree> {
    return this.reqService.get<ApiResult<MenuTreeResponse>>('menu/tree').pipe(
      map(
        ({ data }) =>
          ({
            header: (data?.header ?? []).map((item) => buildMenuItem(item, '', overrides)),
            header_0: (data?.header_0 ?? []).map((item) => buildMenuItem(item, '', overrides)),
            header_1: (data?.header_1 ?? []).map((item) => buildMenuItem(item, '', overrides)),
            header_2: (data?.header_2 ?? []).map((item) => buildMenuItem(item, '', overrides)),
            footer: (data?.footer ?? []).map((item) => buildMenuItem(item, '', overrides)),
            footer_0: (data?.footer_0 ?? []).map((item) => buildMenuItem(item, '', overrides)),
            footer_1: (data?.footer_1 ?? []).map((item) => buildMenuItem(item, '', overrides)),
          }) as SimplifiedMenuTree
      )
    );
  }

  getAllCommercials(): Observable<ApiResult<Advertisement[], ApiResponseMetaList>> {
    return this.reqService.get('portal/commercials');
  }

  getLayoutPreview(hash: string): Observable<ApiResult<Layout>> {
    return this.reqService.get(`layout/preview/view?previewHash=${hash}`);
  }

  getCategoryLayout(categorySlug: string): Observable<ApiResult<Layout>> {
    return this.reqService.get<ApiResult<Layout>>(`column-layout/${categorySlug}`);
  }

  getCategoryArticles(
    categorySlug: string,
    page = 0,
    itemsPerPage = 10,
    year?: string,
    month?: string,
    excludedIds: string[] = []
  ): Observable<ApiResult<ArticleCard[], ApiResponseMetaList>> {
    let params: ArticleCategoryParams = {
      columnSlug: categorySlug,
      rowCount_limit: itemsPerPage.toString(),
      page_limit: page.toString(),
      'excludedArticleIds[]': excludedIds ? excludedIds : [],
    };
    params = year ? { ...params, year } : params;
    params = month ? { ...params, month } : params;
    return this.reqService.get<ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>>(`content-page/articles-by-column`, { params: params }).pipe(
      map(({ data, meta }) => {
        return {
          meta,
          data: data.map((article) => {
            return mapBackendArticleDataToArticleCard(article);
          }),
        };
      })
    );
  }

  getSidebarArticleRecommendations(count: number, columnSlug?: string, excludedIds: string[] = []): Observable<ApiResult<ArticleCard[], ApiResponseMetaList>> {
    let { params }: IHttpOptions = {
      params: {
        rowCount_limit: count.toString(),
        'excludedArticleIds[]': excludedIds,
      },
    };
    params = columnSlug ? { ...params, columnSlug } : params;

    return this.reqService.get<ApiResult<ArticleCard[], ApiResponseMetaList>>(
      columnSlug ? '/content-page/articles-by-column?dev' : '/content-page/articles-by-last-day',
      { params }
    );
  }

  getOpinionAuthor(authorSlug: string, page = 0, itemsPerPage = 12): Observable<ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>> {
    return this.reqService.get(`content-page/articles-by-opinion-type`, {
      params: {
        author: authorSlug,
        rowCount_limit: itemsPerPage.toString(),
        page_limit: page.toString(),
      },
    });
  }

  getArticlesByTag(slug: string): Observable<ApiResult<ArticleCard[], ApiResponseMetaList>> {
    return this.reqService.get(`content-page/articles-by-tag?tagSlug=${slug}`);
  }

  getGalleries(page = 0, itemsPerPage = 30): Observable<ApiResult<GalleryData[], ApiResponseMetaList>> {
    return this.reqService.get(`/media/galleries`, {
      params: {
        rowCount_limit: itemsPerPage.toString(),
        page_limit: page.toString(),
      },
    });
  }

  getDossier(
    dossierSlug: string,
    page = 0,
    itemsPerPage = 30
  ): Observable<ApiResult<BackendArticleSearchResult[], ApiResponseMetaList & Partial<BasicDossier>>> {
    return this.reqService.get(`/content-group/dossiers/${dossierSlug}`, {
      params: { rowCount_limit: itemsPerPage?.toString(), page_limit: page?.toString() },
    });
  }

  public searchByParams(
    page?: number,
    rowCount_limit?: number,
    extraParams?: Record<string, string | string[]>
  ): Observable<ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>> {
    return this.reqService.get(`/content-page/search`, {
      params: { rowCount_limit: rowCount_limit?.toString(), page_limit: page?.toString(), ...extraParams },
    });
  }

  searchByKeyword(
    searchQuery: string,
    page?: number,
    rowCount_limit?: number,
    queryParams: Record<string, any> = {}
  ): Observable<ApiResult<ArticleSearchResult[]>> {
    return this.reqService.get(`/content-page/search`, {
      params: { ...queryParams, global_filter: searchQuery, rowCount_limit: rowCount_limit?.toString(), page_limit: page?.toString() },
    });
  }

  getArticlesByLastDay(
    searchQuery: string,
    page?: number,
    rowCount_limit?: number,
    queryParams: Record<string, any> = {}
  ): Observable<ApiResult<ArticleSearchResult[]>> {
    return this.reqService.get(`/content-page/articles-by-last-day`, {
      params: { ...queryParams, global_filter: searchQuery, rowCount_limit: rowCount_limit?.toString(), page_limit: page?.toString() },
    });
  }

  getPortfolioFooter(): Observable<PortfolioResponse> {
    return this.reqService.get('portal/portfolio-footer');
  }

  searchArticles(
    page: number,
    perPage = 10,
    filters: Record<string, string | string[] | undefined> | undefined = {}
  ): Observable<ApiResult<ArticleSearchResult[], ApiResponseMetaList>> {
    const params = {
      rowCount_limit: perPage.toString(),
      page_limit: page.toString(),
      ...filters,
    };

    return this.reqService.get(`/content-page/search`, {
      params,
    });
  }

  register(formData: RegistrationFormData, recaptchaToken: string): Observable<void> {
    return this.reqService.post(`user/register`, registrationFormDataToBackendRequest(formData, recaptchaToken));
  }

  verifyRegister(id: string, hashedEmail: string, expiration: string, signature: string): Observable<void> {
    return this.reqService.get(`user/register/verify/${id}/${hashedEmail}`, { params: { expiration, signature } });
  }

  getAllowedLoginMethods(): Observable<BackendAllowedLoginMethodsResponse> {
    return this.reqService.get(`user/auth/allowed-logins`);
  }

  login(formData: LoginFormData, recaptchaToken: string): Observable<BackendUserLoginResponse> {
    return this.reqService.post(`portal_user/auth/login_check`, loginFormDataToBackendRequest(formData, recaptchaToken));
  }

  requestPasswordReset(email: string, recaptchaToken: string): Observable<void> {
    return this.reqService.post(`user/password/forget`, requestPasswordResetDataToBackendRequest(email, recaptchaToken));
  }

  resetPassword(email: string, password: string, resetPasswordToken: string): Observable<void> {
    return this.reqService.post(`user/password/reset`, resetPasswordDataToBackendRequest(email, password, resetPasswordToken));
  }

  readonly comments = {
    list: (type: CommentableType, id: string, params: CommentRequestParams): Observable<ApiListResult<BackendComment>> =>
      this.reqService.get(`comments/${type}/${id}/${type === 'article' ? 'comments' : 'answers'}`, {
        params: {
          rowCount_limit: 6,
          ...getCommentOrderParam(params.order),
        },
      }),

    history: (id: string): Observable<ApiListResult<HistoricalComment>> => this.reqService.get(`comments/comment/${id}/history`),

    socialData: (articleId: string): Observable<ApiResult<BackendArticleSocial>> =>
      this.reqService.get<ApiResult<BackendArticleSocial>>(`/content-page/article/${articleId}/social-count`),
  };

  getGalleryDetails(slug: string): Observable<GalleryDetails> {
    return this.reqService
      .get<ApiResult<BackendGalleryDetails[], ApiResponseMetaList>>(`media/gallery/${slug}`)
      .pipe(map(mapBackendGalleryDetailsResultToGalleryDetails));
  }

  getVariableSponsoredDidYouKnowBox(id: string): Observable<VariableDidYouKnowBox> {
    return this.reqService.get(`/content-group/did-you-know/${id}?getRandomData=1`);
  }

  getTopCommentedArticles(): Observable<ApiResult<ArticleCard[]>> {
    return this.reqService.get<ApiResult<ArticleCard[]>>('content-page/top-commented-articles');
  }
}
