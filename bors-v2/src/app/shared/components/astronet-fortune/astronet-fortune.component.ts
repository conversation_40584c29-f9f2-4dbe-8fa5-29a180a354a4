import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconComponent, KesmaSwipeComponent } from '@trendency/kesma-ui';

@Component({
  selector: 'app-astronet-fortune',
  templateUrl: './astronet-fortune.component.html',
  styleUrl: './astronet-fortune.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [KesmaSwipeComponent, IconComponent],
})
export class AstronetFortuneComponent {
  readonly data: Record<string, string>[] = [
    {
      link: 'https://astronet.borsonline.hu/joslas/tarot/',
      image: '../../../../assets/images/fortune-card-1.jpg',
      name: '<PERSON><PERSON> kártya',
    },
    {
      link: 'https://astronet.borsonline.hu/joslas/maya-tarot/',
      image: '../../../../assets/images/fortune-card-2.jpg',
      name: '<PERSON><PERSON><PERSON><PERSON> j<PERSON>l<PERSON>',
    },
    {
      link: 'https://astronet.borsonline.hu/joslas/ciganykartya/',
      image: '../../../../assets/images/fortune-card-3.jpg',
      name: 'Cigány-kártya',
    },
  ];

  readonly swiperBreakpoints = {
    default: {
      itemCount: 2,
      gap: '16px',
    },
    576: {
      itemCount: 2,
      gap: '32px',
    },
  };
}
