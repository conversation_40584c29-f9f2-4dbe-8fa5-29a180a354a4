@use 'shared' as *;

:host {
  --clip-path-height: 10px;

  display: block;
  width: 100%;
  color: var(--kui-white);
  container-type: inline-size;

  @include media-breakpoint-down(md) {
    // Mobile: fill screen width, ignore body padding
    margin-inline: -15px;
    width: calc(100% + 30px);
  }

  .top-commented {
    display: flex;
    flex-direction: column;
    background-color: var(--kui-red-500);
    clip-path: polygon(0 var(--clip-path-height), 101% 0, 100% 100%, 0 100%);
    padding: calc(16px + #{var(--clip-path-height)}) 16px 16px;
    gap: 16px;

    @include media-breakpoint-up(md) {
      @include container-breakpoint-up(xs) {
        --clip-path-height: 20px;

        padding: calc(32px + #{var(--clip-path-height)}) 32px 32px;
        gap: 0;
      }
    }

    .article-row {
      display: flex;
      justify-content: space-between;
      gap: 16px;

      @include container-breakpoint-up(xs) {
        gap: 32px;
      }

      @container (max-width: 348px) {
        flex-wrap: wrap;
        gap: 0;

        a {
          margin-inline-start: auto;
        }
      }

      h2 {
        font-size: 28px;
        font-weight: 800;
        line-height: 28px;

        @container (min-width: 600px) {
          font-size: 48px;
          line-height: 42px;
        }
      }

      a {
        font-size: 16px;
        font-weight: 700;
        line-height: 20px;
        color: var(--kui-white);
        display: flex;
        align-items: center;
        gap: 10px;
        white-space: nowrap;

        kesma-icon {
          fill: var(--kui-white);
          width: 16px;
        }
      }

      .article-column {
        display: flex;
        flex-direction: column;
        gap: 16px;
        width: 47%;
        @container (max-width: 348px) {
          width: 100%;
        }
      }
    }
  }
}
