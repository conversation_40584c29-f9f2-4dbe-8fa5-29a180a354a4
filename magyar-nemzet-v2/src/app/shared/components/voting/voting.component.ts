import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';
import { Sponsorship, ViewModel, VoteData, VotingComponent as KesmaVotingComponent } from '@trendency/kesma-ui';
import { FormsModule } from '@angular/forms';
import { AsyncPipe, NgIf } from '@angular/common';

@Component({
  selector: 'mno-voting',
  templateUrl: './voting.component.html',
  styleUrls: ['./voting.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, FormsModule, AsyncPipe],
})
export class VotingComponent extends KesmaVotingComponent {
  @HostBinding('class') override hostClass = '';
  @Input()
  @HostBinding('class.in-sidebar')
  inSidebar = false;

  override readonly vm = new ViewModel({
    showResults: false,
    hasExpired: false,
    userVotedId: '' as string | undefined,
    voteData: {} as VoteData,
    buttonLabel: 'Szavazok',
    isSubmitButtonDisabled: true,
  });
  sponsorship?: Sponsorship;

  override ngOnInit(): void {
    super.ngOnInit();

    this.vm.next({
      buttonLabel: this.isExpired ? 'Lejárt szavazás' : this.showResults ? 'Szavaztál' : 'Szavazok',
    });
  }

  override setVoteId(id: string): void {
    this.vm.next({
      userVotedId: id,
      isSubmitButtonDisabled: false,
    });
  }

  isThisTheHighestVoteCount(voteCount: number | undefined): boolean {
    if (!voteCount) return false;

    const highestVoteCount = this.data?.answers?.reduce((acc, curr) => {
      return (acc.voteCount as number) > (curr.voteCount as number) ? acc : curr;
    }).voteCount;

    return highestVoteCount === voteCount;
  }

  protected override setProperties(): void {
    super.setProperties();
    if (this.data?.sponsorship) {
      this.sponsorship = this.data?.sponsorship;
      this.hostClass = 'poll-sponsored';
    }
  }
}
