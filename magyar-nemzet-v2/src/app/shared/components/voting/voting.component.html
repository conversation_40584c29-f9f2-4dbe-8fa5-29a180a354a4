<div *ngIf="vm.state$ | async as state" class="poll">
  <div class="poll-header">
    <h3 [style.background-color]="sponsorship?.highlightedColor" [style.color]="sponsorship?.fontColor" class="poll-header-title">
      <span class="poll-header-title-text">{{ state.showResults ? 'Köszönjük válaszát!' : data?.title }}</span>
      <a *ngIf="sponsorship?.thumbnailUrl" [href]="sponsorship?.url">
        <img
          [alt]="'Szponzor logo:' + sponsorship?.title"
          [src]="sponsorship?.thumbnailUrl"
          [title]="sponsorship?.title"
          class="poll-sponsor-logo"
          loading="lazy"
        />
      </a>
    </h3>
    <h4 class="poll-header-question">{{ data?.question }}</h4>
  </div>

  <form class="poll-form">
    <ul class="poll-form-wrapper">
      @if (data?.isResultVisible || !state.showResults) {
        @for (item of data?.answers; track item.id) {
          <div (click)="setVoteId(item.id)" [class.active]="state.userVotedId === item.id" [class.results]="state.showResults" class="poll-form-radio">
            <li class="poll-form-radio-option">
              <label [for]="item.id" class="poll-form-radio-label">
                {{ item.answer }}
                <input
                  [checked]="state.userVotedId === item.id"
                  [disabled]="state.showResults"
                  [id]="item.id"
                  [name]="'poll-' + data?.id"
                  [value]="item.id"
                  class="poll-form-radio-input"
                  type="radio"
                />
                <span *ngIf="!state.showResults" class="poll-form-checkmark"></span>
              </label>
              <div *ngIf="state.showResults" class="poll-form-result">{{ item?.votePercentage ?? '0' }}%</div>
            </li>
            <div *ngIf="state.showResults" class="poll-form-radio-progress-bar-wrapper">
              <div [style.width]="item.votePercentage + '%'" class="poll-form-radio-progress-bar"></div>
            </div>
          </div>
        }
      }
    </ul>

    <div *ngIf="!state.showResults && !showResults" class="poll-form-buttons">
      <input
        (click)="onVote()"
        [class.disabled]="state.isSubmitButtonDisabled || isExpired"
        [class.voted]="state.showResults"
        [disabled]="state.isSubmitButtonDisabled || isExpired"
        [value]="state.buttonLabel"
        class="poll-form-buttons-submit btn btn-ghost btn-ghost-blue-transparent"
        type="submit"
      />
    </div>
  </form>
</div>
