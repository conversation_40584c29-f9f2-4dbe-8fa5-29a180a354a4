@use 'shared' as *;

:host {
  display: flex;
  flex-direction: column;
  padding: 16px 16px 24px;
  margin-bottom: 24px;
  background-color: var(--kui-opinion-bg-color);

  --kui-opinion-fg-color: var(--kui-slate-950);
  --kui-opinion-fg-hover-color: var(--kui-blue-700);
  --kui-opinion-bg-color: transparent;
  --kui-opinion-border-color: var(--kui-blue-500);
  --kui-opinion-label-color: var(--kui-blue-900);
  --kui-opinion-info-bg-color: var(--kui-blue-900);

  @include media-breakpoint-down(sm) {
    width: calc(100% + 32px);
    margin-inline: -16px;
    &.style-OpinionWaiter,
    &.style-AuthorOpinionHeader {
      margin-inline: 0;
      width: 100%;

      .opinion-container {
        border-left: 0;
        border-right: 0;
      }
    }
    &.style-Large {
      .opinion-container {
        border-left: 0;
        border-right: 0;
      }
    }
    .opinion-wrapper {
      padding-inline: 0;
      max-width: 100%;
    }
  }

  &.layout-opinion {
    border-bottom: 1px dotted var(--kui-slate-300);

    &:hover {
      border-bottom-color: var(--kui-blue-700);
    }
  }

  &.style-Narrow,
  &.style-Large,
  &.style-OpinionHeader,
  &.style-InterviewHeader,
  &.style-MinuteToMinuteHeader,
  &.style-SorozatvetoHeader,
  &.style-WhereTheBallWillBe {
    --kui-opinion-fg-color: var(--kui-white);
    --kui-opinion-fg-hover-color: var(--kui-blue-500);
    --kui-opinion-bg-color: var(--kui-blue-950);
    --kui-opinion-border-color: var(--kui-blue-50);
    --kui-opinion-label-color: var(--kui-blue-50);
  }

  &.style-AuthorOpinionHeader {
    --kui-opinion-fg-color: var(--kui-blue-900);
    --kui-opinion-fg-hover-color: var(--kui-blue-500);
    --kui-opinion-bg-color: transparent;
    --kui-opinion-border-color: var(--kui-blue-500);
    --kui-opinion-label-color: var(--kui-blue-500);
  }

  &.style-InterviewHeader,
  &.style-MinuteToMinuteHeader,
  &.style-SorozatvetoHeader {
    --kui-opinion-fg-color: var(--kui-white);
    --kui-opinion-fg-hover-color: var(--kui-blue-500);
    --kui-opinion-bg-color: var(--kui-slate-900);
    --kui-opinion-info-bg-color: var(--kui-slate-800);
    --kui-opinion-border-color: transparent;
    --kui-opinion-label-color: var(--kui-white);
  }

  &.style-MinuteToMinuteHeader {
    --kui-opinion-bg-color: var(--kui-blue-950);
    --kui-opinion-info-bg-color: var(--kui-blue-900);
  }

  &.style-OpinionWaiter,
  &.style-AuthorOpinionHeader {
    --kui-opinion-bg-color: transparent;
    --kui-opinion-border-color: var(--kui-blue-500);
    --kui-opinion-fg-color: var(--kui-blue-900);
    --kui-opinion-fg-hover-color: var(--kui-blue-500);

    padding-left: 0;
    padding-right: 0;

    .opinion {
      &-link-wrapper {
        flex-direction: row;
        justify-content: space-evenly;
        align-items: stretch;
        flex-wrap: nowrap;

        .opinion-wrapper-container,
        .opinion-thumbnail {
          flex: 50%;
          max-width: calc(50% - 24px);
        }
      }

      &-ornament {
        flex-basis: auto;

        .icon {
          padding-right: 0;
        }

        .opinion-wrapper-label {
          position: relative;
          color: var(--kui-blue-500);
          background-color: var(--kui-opinion-bg-color);
          font-size: 24px;
          font-weight: 700;
          line-height: 32px;
          letter-spacing: 0.01em;
          padding-right: 12px;
        }
      }

      &-layout-wrapper {
        flex-wrap: nowrap;
        gap: 24px;
        padding: 0 24px;
      }

      &-content-container {
        gap: 16px;
        width: 50%;
      }

      &-title {
        font-size: 36px;
        font-weight: 700;
        line-height: 44px;
        letter-spacing: 0;
        text-align: center;

        @include media-breakpoint-down(xs) {
          font-size: 26px;
        }
      }

      &-lead {
        font-size: 20px;
        font-weight: 400;
        line-height: 24px;
        letter-spacing: 0;
        text-align: center;
      }
    }
  }

  &.style-AuthorOpinionHeader {
    width: 100%;
    padding-top: 0;
    padding-bottom: 0;

    .opinion-container {
      width: 100%;
    }

    @include media-breakpoint-down(sm) {
      padding-left: 0;
      padding-right: 0;
      margin-bottom: 0;
    }

    .opinion {
      &-container {
        @include media-breakpoint-down(sm) {
          gap: 24px;
        }
      }

      &-title {
        @include media-breakpoint-down(sm) {
          font-size: 32px;
          line-height: 36px;
        }
      }

      &-ornament {
        padding: 8px 0;
        margin-bottom: 0;

        .opinion-wrapper-label {
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
          gap: 16px;
          padding: 0 8px;
        }
      }

      &-layout-wrapper {
        padding: 0 16px;
        gap: 44px;
        @include media-breakpoint-down(sm) {
          flex-direction: column;
          gap: 16px;
          padding-inline: 0;
          padding-bottom: 0;
        }
      }

      &-label {
        color: var(--kui-blue-900);

        &:hover {
          color: var(--kui-blue-500);
        }
      }

      &-content-container,
      &-link-wrapper .opinion-thumbnail {
        @include media-breakpoint-down(sm) {
          flex: 1;
          width: auto;
          max-width: 100%;
        }
      }

      &-thumbnail {
        margin-bottom: 0;
      }
    }
  }

  &.style-OpinionHeader,
  &.style-InterviewHeader,
  &.style-MinuteToMinuteHeader,
  &.style-SorozatvetoHeader {
    margin-inline: 0;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 24px;

    .opinion {
      &-wrapper {
        max-width: calc(100% - 140px);
        display: flex;
        flex-direction: row;
        align-items: stretch;
        justify-content: center;
        margin: 0 auto;
        gap: 24px;
        @include media-breakpoint-down(sm) {
          flex-direction: column;
          max-width: 100%;

          padding-inline: 0;

          .opinion {
            &-container,
            &-thumbnail {
              width: 100%;
            }

            &-thumbnail {
              order: 0;
              margin: 0;
            }

            &-container {
              order: 1;
              border-left: 0;
              border-right: 0;
            }

            &-ornament {
              .icon {
                padding: 0;
                margin: 0 16px;
                width: auto;
              }
            }

            &-title {
              padding: 0;
            }

            &-lead {
              padding: 0;
            }
          }
        }
      }

      &-container {
        width: calc(50% - 24px);
        flex-shrink: 0;
        flex-grow: 0;

        .opinion-label {
          font-size: 24px;
          font-weight: 500;
          line-height: 32px;
          letter-spacing: 0.01em;
        }

        .opinion-labels {
          margin-bottom: 24px;
          align-items: center;
        }
      }

      &-ornament {
        flex-basis: auto;
      }

      &-title {
        padding: 0 48px;
      }

      &-lead {
        font-weight: 500;
        padding: 0 48px;
      }

      &-thumbnail {
        height: auto;
        object-fit: cover;
        margin: 12px 0;
        width: 50%;
      }
    }

    @include media-breakpoint-down(sm) {
      .opinion-info-block {
        justify-content: space-between;
        padding: 16px;
        align-items: center;

        .section-left {
          width: 50%;
          height: auto;
          justify-content: flex-start;
          flex-wrap: wrap;

          .opinion-photographer {
            width: 100%;
          }
        }

        .section-right {
          width: auto;
          justify-content: flex-end;
          border: 0;
        }

        .opinion-labels {
          margin-bottom: 0;
        }

        .opinion-label {
          padding: 4px;

          &:first-child {
            text-transform: capitalize;
          }
        }
      }
    }
  }

  &.style-InterviewHeader,
  &.style-MinuteToMinuteHeader,
  &.style-SorozatvetoHeader {
    padding: 48px 16px 0;

    .opinion {
      &-container {
        border: 0;
        align-items: stretch;
        justify-content: flex-start;
        gap: 16px;
        padding-top: 16px;

        .opinion {
          &-labels {
            justify-content: flex-start;
            border-bottom: 1px solid var(--kui-opinion-label-color);
            padding-bottom: 16px;
            margin-bottom: 32px;
            align-items: center;
          }

          &-label,
          &-column,
          &-label-date {
            height: 16px;
            font-size: 16px !important;
            font-weight: 700 !important;
            line-height: 21px !important;
            color: var(--kui-opinion-label-color);
          }

          &-wrapper {
            margin-bottom: 20px;
          }
        }
      }

      &-title {
        padding: 0;
        font-size: 32px;
        font-weight: 700;
        line-height: 36px;
        letter-spacing: 0;
        text-align: left;
      }

      &-lead {
        padding: 0;
        text-align: left;
      }

      &-author {
        display: flex;
        gap: 24px;
        height: 16px;

        &-name,
        &-source {
          @include transition;
          color: var(--kui-opinion-label-color);
          font-size: 14px;
          line-height: 18px;
        }

        &-name {
          font-weight: 700;
        }

        &-source {
          font-weight: 400;
          border-left: 2px solid var(--kui-opinion-label-color);
          padding-left: 24px;

          &:first-child {
            border-left: 0;
            padding-left: 0;
          }
        }
      }

      &-info-block {
        height: 56px;

        .section-left {
          justify-content: flex-start;

          .opinion-label:first-child {
            font-weight: 500;
          }
        }
      }

      &-thumbnail {
        margin: 0;
      }
    }
  }

  &.style-Large {
    mno-opinion-author {
      margin-top: 12px;
    }
    .opinion-container {
      padding-inline: 16px;
    }
    .opinion-ornament {
      margin-inline: -16px;
      width: calc(100% + 32px);
    }
  }

  &.style-InterviewHeader {
    .opinion-wrapper {
      padding-bottom: 16px;
    }

    @include media-breakpoint-down(sm) {
      padding-top: 0;
      .opinion {
        &-container {
          .opinion-labels {
            margin-bottom: 0;
          }
        }

        &-author {
          align-items: center;
          color: var(--kui-white);
          font-size: 14px;
          font-weight: 700;
          line-height: 18px; /* 128.571% */
          gap: 4px;
        }
      }
    }
  }

  &.style-SorozatvetoHeader {
    background-size: cover;
    background-repeat: no-repeat;

    .opinion {
      &-wrapper {
        margin: 0 12px;
        width: 100%;
        max-width: 100%;
        min-height: 300px;
      }

      &-container {
        width: 100%;
        justify-content: flex-end;

        .opinion-labels {
          border-bottom: 0 !important;
          margin-bottom: 0;
        }

        .opinion-label:first-child {
          font-weight: 500 !important;
        }

        .opinion-label {
          font-weight: 400 !important;
        }

        .opinion-title {
          min-height: 132px;
        }
      }
    }
  }

  &.style-WhereTheBallWillBe {
    flex-direction: row;
    flex-wrap: wrap;

    .opinion {
      &-wrapper {
        display: flex;
        flex-direction: row;
        width: 100%;
        justify-content: center;
        align-items: center;
        column-gap: 84px;
        padding: 24px 84px;

        @include media-breakpoint-down(md) {
          flex-direction: column-reverse;
          row-gap: 24px;
        }
      }

      &-info-block {
        padding: 16px 84px;

        @include media-breakpoint-down(md) {
          padding: 8px;
        }

        .section-left {
          justify-content: left;

          @include media-breakpoint-down(md) {
            .opinion-labels {
              .option-label:nth-child(n + 2) {
                display: none;
              }
              .icon {
                display: none;
              }
            }
          }
        }

        .section-right {
          justify-content: right;

          mno-social-share {
            margin-right: 8px;
          }
        }
      }

      &-photographer {
        display: none;
      }

      &-container {
        border: none;
        max-width: 560px;
        padding: 0 32px;
      }

      &-column {
        font-size: 24px;
        font-style: normal;
        font-weight: 500;
        line-height: 32px;
        letter-spacing: 0.24px;
      }

      &-divider {
        width: 230px;
        height: 1px;
        background: var(--kui-white);
      }

      &-title {
        font-size: 32px;
        font-family: var(--kui-font-secondary);
        font-style: normal;
        font-weight: 700;
        line-height: 36px;

        @include media-breakpoint-down(md) {
          font-size: 24px;
          line-height: 30px;
        }

        &-quote-left {
          margin-right: -12px;
          margin-top: -22px;
        }

        &-quote-right {
          margin-left: -12px;
          margin-bottom: -12px;
          transform: rotate(180deg);
        }
      }

      &-lead {
        font-size: 20px;
        font-style: normal;
        font-weight: 700;
        line-height: 26px;
        letter-spacing: 0.3px;
      }

      &-author-name {
        font-size: 24px;
        font-style: normal;
        font-weight: 500;
        line-height: 32px;
        letter-spacing: 0.24px;
        color: var(--kui-white);

        @include media-breakpoint-down(md) {
          font-size: 20px;
          line-height: 26px;
          letter-spacing: 0.3px;
        }
      }

      &-author-image {
        width: 345px;
        height: 345px;
        border-radius: 50%;

        @include media-breakpoint-down(md) {
          max-width: 230px;
          width: 100%;
          max-height: 230px;
          height: 100%;
        }
      }
    }
    .opinion-info-block {
      .section-right {
        border-left: none;
      }
    }
  }
}

.opinion {
  &-column {
    @include transition;

    &:hover {
      @include transition;
      color: var(--kui-blue-500);
    }
  }

  &-info-block {
    width: calc(100% + 32px);
    background-color: var(--kui-opinion-info-bg-color);
    margin: 0 -16px -24px;
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .opinion-label {
      font-weight: 400;

      &:first-child {
        font-weight: 500;
        color: var(--kui-white);
        @include transition;
      }
    }

    a.opinion-label:hover {
      @include transition;
      color: var(--kui-blue-500);
    }

    .section-left,
    .section-right {
      min-width: calc(50% - 70px);
      display: flex;
      align-items: center;
      justify-content: center;
      height: 16px;
    }

    .section-left {
      flex-wrap: wrap;
      height: fit-content;
    }

    .section-right {
      gap: 10px;
      border-left: 1px solid var(--kui-slate-300);

      .opinion-photographer {
        margin-left: 16px;
      }

      &:first-child {
        border-left: 0;
        justify-content: flex-start;
      }

      justify-content: space-between;
    }

    .section-left {
      margin-right: 16px;

      .opinion-photographer {
        padding-left: 4px;

        &.mobile-padding {
          @media screen and (max-width: 440px) {
            padding-left: 16px;
          }
        }
      }
    }
  }

  &-photographer,
  &-publish-date {
    font-size: 14px;
    font-weight: 400;
    line-height: 18px;
    color: var(--kui-blue-50);
    min-height: 16px;
  }

  &-publish-date {
    margin-left: 8px;
    border-left: 1px solid var(--kui-blue-50);
    padding-left: 8px;
    white-space: nowrap;

    &.with-label {
      padding-left: unset;
      margin-left: unset;
      border-left: unset;
    }
  }

  &-link-wrapper {
    display: flex;
    flex-direction: column;

    &:hover {
      .opinion-title,
      .opinion-lead {
        color: var(--kui-opinion-fg-hover-color);
        @include transition;
      }
    }
  }

  &-thumbnail {
    object-fit: contain;
    width: 100%;
    margin-bottom: 16px;
  }

  &-container {
    border-left: 1px solid var(--kui-opinion-border-color);
    border-right: 1px solid var(--kui-opinion-border-color);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    gap: 16px;
  }

  &-ornament {
    width: 100%;
    min-width: 100%;
    flex-shrink: 0;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;

    hr {
      border: 0;
      height: 1px;
      border-bottom: 1px solid var(--kui-opinion-border-color);
      width: 100%;
    }

    .icon {
      display: inline-block;
      flex-shrink: 0;
      width: 80px;
      background-color: var(--kui-opinion-bg-color);
      padding: 0 10px;
      margin-bottom: -4px;
    }
  }

  &-label {
    color: var(--kui-opinion-label-color);
    font-size: 14px;
    font-weight: 500;
    line-height: 18px;
    white-space: nowrap;
    padding-right: 12px;
    border-right: 1px solid var(--kui-slate-300);
    @include transition;

    &:last-child {
      border-right: 0;
      padding-right: 0;
    }
  }

  &-labels {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;

    [mno-icon] {
      margin-right: 4px;
    }

    @include media-breakpoint-down(sm) {
      flex-wrap: wrap;
    }
  }

  &-title {
    font-family: var(--kui-font-secondary);
    font-size: 36px;
    line-height: 44px;
    color: var(--kui-opinion-fg-color);
    @include transition;
    text-align: center;

    @include media-breakpoint-down(xs) {
      font-size: 26px;
    }
  }

  &-lead {
    color: var(--kui-opinion-fg-color);
    font-size: 20px;
    font-weight: 700;
    line-height: 26px;
    letter-spacing: 0.015em;
    text-align: center;
    @include transition;
  }

  &-badge-container {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    justify-content: flex-start;
    gap: 4px;

    &[data-icon-size='16'] {
      gap: 2px;
    }
  }

  &-author-name {
    @include transition;
    color: var(--kui-blue-500);
  }

  &-container-top {
    display: flex;
    gap: 8px;
    align-items: stretch;
    justify-content: stretch;
  }

  &-container-left {
    display: flex;
    flex-direction: column;
    gap: 4px;
    flex: 1;
  }
}

:host {
  &.style-Narrow {
    // max-width: 360px;

    .opinion {
      &-container {
        border: 0;
        gap: 10px;
      }

      &-title {
        font-size: 32px;
        font-weight: 700;
        line-height: 36px;
        letter-spacing: 0;
      }

      &-label {
        font-size: 12px;
        line-height: 16px;
      }

      &-lead {
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
        text-align: center;
      }

      &-ornament {
        // margin-top: -46px;
        margin-bottom: 16px;

        .icon {
          width: 55px;
          padding: 0 5px;
          top: 22px;
        }
      }
    }
  }

  &.style-SmallAuthorRight,
  &.style-SmallAuthorRightBorderBlue,
  &.style-SmallImgRightBorderBlack,
  &.style-SmallImgRightSplitBorderBlack,
  &.style-AuthorLabelTitleBadge,
  &.style-LabelTitleBadge {
    // max-width: 300px;
    .opinion {
      &-link-wrapper {
        gap: 8px;
      }

      &-author-name {
        font-weight: 500;
      }

      &-title {
        font-weight: 700;
        font-size: 16px;
        line-height: 20px;
        letter-spacing: 0;
        text-align: left;
      }

      &-lead {
        font-weight: 400;
        font-size: 14px;
        line-height: 18px;
        letter-spacing: 0;
        text-align: left;
      }

      &-label {
        font-weight: 500;
        font-size: 12px;
        line-height: 16px;
        letter-spacing: 0;
      }

      &-thumbnail {
        width: 108px;
        aspect-ratio: 1/1;
        object-fit: cover;
        margin-bottom: 0;
      }
    }
  }

  &.style-SmallImgRightBorderBlack.sidebar {
    @include media-breakpoint-up(lg) {
      div.opinion-container-top {
        flex-direction: column-reverse;
        align-items: center;
      }
    }
  }

  &.style-SmallAuthorRightBorderBlue,
  &.style-AuthorLabelTitleBadge,
  &.style-LabelTitleBadge {
    border-right: 1px solid var(--kui-blue-500) !important;
    border-bottom: 1px solid var(--kui-blue-500) !important;
    @include transition;

    &:hover {
      border-right-width: 2px;
      border-bottom-width: 2px;
      @include transition;
    }
  }

  &.style-SmallImgRightBorderBlack,
  &.style-SmallImgRightSplitBorderBlack {
    border-right: 1px solid var(--kui-slate-950) !important;
    border-bottom: 1px solid var(--kui-slate-950) !important;
    @include transition;

    &:hover {
      border-right-width: 2px;
      border-bottom-width: 2px;
      @include transition;
    }
  }

  &.style-SmallImgRightSplitBorderBlack {
    .opinion {
      &-thumbnail {
        max-width: 108px;
        max-height: 108px;
      }
    }
  }

  a.opinion-author-name:hover {
    color: var(--kui-blue-500);
    @include transition;
  }

  &.style-AuthorLabelTitleBadge,
  &.style-LabelTitleBadge {
    height: auto;

    mno-opinion-author::ng-deep {
      & {
        flex-direction: column;
        align-items: center;
        justify-content: stretch;
      }

      .author-link {
        flex-direction: column;
        align-items: center;
        justify-content: stretch;
      }

      .author-name {
        color: var(--kui-blue-500);
        font-size: 16px;
        font-weight: 500;
        line-height: 22px;
        letter-spacing: 0;
        text-align: left;
      }
    }
  }

  &.style-MinuteToMinuteHeader {
    padding-top: 24px;

    .opinion {
      &-container {
        padding-top: 0;
      }

      &-publish-date {
        margin-left: 0;
        padding-left: 16px;
      }

      &-info-block {
        .section-right {
          align-items: center;
          justify-content: flex-end;
        }
      }

      &-lead {
        font-size: 20px;
        font-style: normal;
        font-weight: 500;
        line-height: 26px; /* 130% */
        letter-spacing: 0.3px;
      }

      &-photographer {
        color: var(--kui-white);
        padding-left: 10px;
        border-left: 1px solid var(--kui-white);
        margin-top: -8px;
        height: 16px;
        display: block;
      }
    }
  }
}

.opinion-card-live-info {
  border: 1px solid var(--kui-blue-500);
  border-radius: 2px;
  padding: 4px;
  gap: 16px;
  display: flex;
  width: 100%;
  justify-content: flex-start;
  margin-bottom: 8px;

  &,
  & .opinion-publish-date {
    color: var(--kui-blue-500);
    font-size: 16px;
    font-weight: 700;
    line-height: 21px;
    letter-spacing: 0;
    align-items: center;
  }

  .opinion-publish-date {
    border-color: var(--kui-blue-500);
    display: inline-flex;
  }
}

.opinion-card-minute-to-minute-info {
  width: calc(100% + 32px);
  background-color: var(--kui-blue-800);
  margin: 0 -16px -24px;
  padding: 24px 84px;
  display: flex;
  align-items: stretch;
  justify-content: flex-start;
  gap: 16px;

  @include media-breakpoint-down(sm) {
    flex-direction: column;
    padding: 16px;
  }

  .minute-summary {
    font-family: var(--kui-font-secondary);
    color: var(--kui-white);
    font-size: 24px;
    font-weight: 700;
    line-height: 30px;
    letter-spacing: 0;
  }

  .minute-blocks {
    margin-top: -8px;

    li {
      list-style-position: inside;
      margin: 12px 0;
      background: url('/assets/images/icons/square-small-blue.svg') no-repeat left top;
      padding: 0 0 0 32px;
      list-style-type: none;

      a {
        color: var(--kui-white);
        font-size: 20px;
        font-weight: 700;
        line-height: 26px; /* 130% */
        letter-spacing: 0.3px;
        cursor: pointer;
      }
    }
  }

  .opinion-publish-date {
    white-space: pre;
  }
}
