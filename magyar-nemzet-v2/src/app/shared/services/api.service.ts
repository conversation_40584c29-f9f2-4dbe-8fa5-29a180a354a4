import { Injectable } from '@angular/core';
import { buildPhpArrayParam, IHttpOptions, ReqService } from '@trendency/kesma-core';
import {
  Advertisement,
  ApiListResult,
  ApiResponseMetaList,
  ApiResult,
  ArticleCard,
  ArticleCategoryParams,
  ArticleSearchResult,
  BackendArticle,
  BackendArticleSearchResult,
  BasicDossier,
  buildMenuItem,
  DossierArticle,
  DossierCard,
  FilterParams,
  GalleriesResponse,
  InitResponse,
  Layout,
  MenuTreeResponse,
  OlimpiaHungarianCompetitions,
  OlimpiaMedalsNational,
  PortalBasedMenuLinkOverride,
  PortfolioResponse,
  PrimaryColumn,
  PublicAuthor,
  Region,
  SearchQuery,
  SimplifiedMenuTree,
  TrendingTag,
  VariableDidYouKnowBox,
} from '@trendency/kesma-ui';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { MedalTable } from '@trendency/kesma-ui/lib/components/olimpia/olimpia-medal-table/olimpia-medal-table.definitions';
import { backendArticlesSearchResultsToArticleSearchResArticles } from '../utils';
import { AuthorData, BackendAuthorData, BrandingType } from '../definitions';

@Injectable({
  providedIn: 'root',
})
export class ApiService {
  constructor(private readonly reqService: ReqService) {}

  public init(): Observable<ApiResult<InitResponse>> {
    return this.reqService.get('init');
  }

  public getMenu(overrides?: PortalBasedMenuLinkOverride): Observable<SimplifiedMenuTree> {
    return this.reqService.get<ApiResult<MenuTreeResponse>>('menu/tree').pipe(
      map(
        ({ data }) =>
          ({
            header: (data?.header ?? []).map((item) => buildMenuItem(item, '', overrides)),
            header_0: (data?.header_0 ?? []).map((item) => buildMenuItem(item, '', overrides)),
            header_1: (data?.header_1 ?? []).map((item) => buildMenuItem(item, '', overrides)),
            footer: (data?.footer ?? []).map((item) => buildMenuItem(item, '', overrides)),
            footer_0: (data?.footer_0 ?? []).map((item) => buildMenuItem(item, '', overrides)),
            footer_1: (data?.footer_1 ?? []).map((item) => buildMenuItem(item, '', overrides)),
          }) as SimplifiedMenuTree
      )
    );
  }

  public getAllCommercials(): Observable<ApiResult<Advertisement[], ApiResponseMetaList>> {
    return this.reqService.get('portal/commercials');
  }

  public getLayoutPreview(hash: string): Observable<ApiResult<Layout>> {
    return this.reqService.get(`layout/preview/view?previewHash=${hash}`);
  }

  public getCategoryArticles(
    categorySlug: string,
    page = 0,
    itemsPerPage = 10,
    year?: string,
    month?: string,
    excludedIds: string[] = []
  ): Observable<ApiResult<BackendArticle[], ApiResponseMetaList>> {
    let params: ArticleCategoryParams = {
      columnSlug: categorySlug,
      rowCount_limit: itemsPerPage.toString(),
      page_limit: page.toString(),
      'excludedArticleIds[]': excludedIds ? excludedIds : [],
    };
    params = year ? { ...params, year } : params;
    params = month ? { ...params, month } : params;
    return this.reqService.get<ApiResult<BackendArticle[], ApiResponseMetaList>>('content-page/articles-by-column', { params: params }).pipe(
      map(({ data, meta }) => {
        return {
          meta,
          data: data.map((article) => {
            const [publishYear, publishMonth] = (article.publishDate as string).split('-');
            return {
              ...article,
              publishYear,
              publishMonth,
            };
          }),
        };
      })
    );
  }

  public getSidebarArticleRecommendations(
    count: number,
    columnSlug?: string,
    excludedIds: string[] = []
  ): Observable<ApiResult<ArticleCard[], ApiResponseMetaList>> {
    let { params }: IHttpOptions = {
      params: {
        rowCount_limit: count.toString(),
        'excludedArticleIds[]': excludedIds,
      },
    };
    params = columnSlug ? { ...params, columnSlug } : params;

    return this.reqService.get<ApiResult<ArticleCard[], ApiResponseMetaList>>(
      columnSlug ? 'content-page/articles-by-column?dev' : 'content-page/articles-by-last-day',
      { params }
    );
  }

  getAuthorFromPublicAuthor(authorSlug: string): Observable<ApiResult<PublicAuthor>> {
    return this.reqService.get('user/author_social', {
      params: {
        global_filter: authorSlug,
      },
    });
  }

  public getAuthor(authorSlug: string, page = 0, itemsPerPage = 12): Observable<Record<string, any>> {
    return this.reqService.get('user/authors', {
      params: {
        global_filter: authorSlug,
        rowCount_limit: itemsPerPage.toString(),
        page_limit: page.toString(),
      },
    });
  }

  public getOpinionAuthor(authorSlug: string, page = 0, itemsPerPage = 12): Observable<ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>> {
    return this.reqService.get('content-page/articles-by-opinion-type', {
      params: {
        author: authorSlug,
        rowCount_limit: itemsPerPage.toString(),
        page_limit: page.toString(),
      },
    });
  }

  public getPublicAuthors(authorId?: string, options?: IHttpOptions): Observable<ApiResult<BackendAuthorData[], ApiResponseMetaList>> {
    let params = { ...options?.params };
    params = authorId ? { ...params, 'public_author_id[]': authorId } : params;
    return this.reqService.get(`/user/authors`, { params });
  }

  public getArticlesByTag(slug: string): Observable<ApiResult<ArticleCard[], ApiResponseMetaList>> {
    return this.reqService.get(`content-page/articles-by-tag?tagSlug=${slug}`);
  }

  public getGalleries(page = 0, itemsPerPage = 21): Observable<ApiListResult<GalleriesResponse>> {
    return this.reqService.get('media/galleries', {
      params: {
        rowCount_limit: itemsPerPage.toString(),
        page_limit: page.toString(),
      },
    });
  }

  public getDossier(dossierSlug: string, page = 0, itemsPerPage = 21): Observable<ApiResult<DossierArticle[], ApiResponseMetaList & Partial<BasicDossier>>> {
    return this.reqService.get(`content-group/dossiers/${dossierSlug}`, {
      params: { rowCount_limit: itemsPerPage?.toString(), page_limit: page?.toString() },
    });
  }

  getDossiers(page = 0, itemsPerPage = 21): Observable<ApiResult<DossierCard[], ApiResponseMetaList>> {
    return this.reqService.get('content-group/dossiers', {
      params: { rowCount_limit: itemsPerPage?.toString(), page_limit: page?.toString() },
    });
  }

  public getRegionPage(regionSlug: string, page = 0, itemsPerPage = 21): Observable<ApiResult<BackendArticle[], ApiResponseMetaList & { regionName: string }>> {
    return this.reqService.get('content-page/articles-by-region', {
      params: { regionSlug: regionSlug, rowCount_limit: itemsPerPage?.toString(), page_limit: page?.toString() },
    });
  }

  public getRegions(
    page = 0,
    itemsPerPage = 50
  ): Observable<
    ApiResult<
      Region[],
      ApiResponseMetaList & {
        regionName: string;
      }
    >
  > {
    return this.reqService.get('source/content-group/regions', {
      params: { rowCount_limit: itemsPerPage?.toString(), page_limit: page?.toString() },
    });
  }

  searchByKeyword(page?: number, rowCount_limit?: number, extraParams: FilterParams = {}): Observable<ApiListResult<BackendArticleSearchResult>> {
    return this.reqService.get('content-page/search', {
      params: {
        rowCount_limit: rowCount_limit?.toString(),
        page_limit: page?.toString(),
        ...extraParams,
      },
    });
  }

  public getPortfolioFooter(): Observable<PortfolioResponse> {
    return this.reqService.get('portal/portfolio-footer');
  }

  getTagsOnHeaderBar(): Observable<ApiResult<TrendingTag[], ApiResponseMetaList>> {
    return this.reqService.get('content-group/tags-on-header-bar');
  }

  getSearch(searchQuery: SearchQuery, page = 0, itemsPerPage = 20): Observable<ApiListResult<ArticleSearchResult>> {
    return this.reqService.get(`content-page/search?`, {
      params: {
        ...searchQuery,
        rowCount_limit: itemsPerPage.toString(),
        page_limit: page.toString(),
      },
    });
  }

  getArticlesByFoundationTag(foundationTag: string): Observable<ApiResult<ArticleSearchResult[], ApiResponseMetaList>> {
    return this.reqService
      .get(`content-page/search`, {
        params: {
          foundationTagSelect: foundationTag,
          rowCount_limit: '1',
          page_limit: '0',
          isFoundationContent: '1',
        },
      })
      .pipe(
        map(({ data, meta }: ApiResult<BackendArticleSearchResult[], ApiResponseMetaList> | any) => ({
          data: data.map(backendArticlesSearchResultsToArticleSearchResArticles),
          meta,
        }))
      );
  }

  public searchArticleByTags(tags: string[], page = 0, itemsPerPage = 10): Observable<ApiResult<ArticleSearchResult[], ApiResponseMetaList>> {
    return this.reqService
      .get(`content-page/search`, {
        params: {
          ...buildPhpArrayParam(tags, 'tags'),
          rowCount_limit: itemsPerPage.toString(),
          page_limit: page.toString(),
        },
      })
      .pipe(
        map(({ data, meta }: ApiResult<BackendArticleSearchResult[], ApiResponseMetaList> | any) => ({
          data: data.map(backendArticlesSearchResultsToArticleSearchResArticles),
          meta,
        }))
      );
  }

  public getOlimpiaCompetitions(startDate: string, countryCode = 'hun'): Observable<ApiResult<OlimpiaHungarianCompetitions[], ApiResponseMetaList>> {
    return this.reqService.get(`/olympics/events/2024?countryCode_filter=${countryCode}&startDate_filter=${startDate}`);
  }

  getBrandingRecommendations(articleSlug: string): Observable<any> {
    return this.reqService.get(`content-page/branding-box/${articleSlug}/recommendation`);
  }

  getBrandingPreview(articleSlug: string, previewHash: string): Observable<any> {
    return this.reqService.get(`content-page/branding-box/${articleSlug}/preview/view?previewHash=${previewHash}`);
  }

  getBrandingArticle(category: string, year: string, month: string, articleSlug: string): Observable<any> {
    return this.reqService.get(`content-page/branding-box/${category}/${year}/${month}/${articleSlug}`);
  }

  public getBrandingList(brandingType: BrandingType, page = 0, itemsPerPage = 20): Observable<any> {
    return this.reqService.get<any>(`content-page/branding-boxes/${brandingType}`, {
      params: {
        rowCount_limit: itemsPerPage.toString(),
        page_limit: page.toString(),
      },
    });
  }

  getOlimpiaCountryMedals(year = '2024'): Observable<ApiResult<MedalTable[]>> {
    return this.reqService.get(`/olympics/country-medallist/${year}`);
  }

  getOlimpiaNationalMedals(year = '2024', countryCode = 'hun'): Observable<ApiResult<OlimpiaMedalsNational[]>> {
    return this.reqService.get(`/olympics/participant-medallist/${year}?countryCode_filter=${countryCode}`);
  }

  getAuthors(page = 0, perPage = 10, isInner?: boolean): Observable<ApiListResult<BackendAuthorData>> {
    const params = {
      rowCount_limit: perPage.toString(),
      page_limit: page.toString(),
      is_active: '1',
      is_inner: isInner ? '1' : '0',
    };

    return this.reqService.get('user/authors', {
      params,
    });
  }

  getPublicAuthorSocial(global_filter?: string, options?: IHttpOptions): Observable<ApiResult<AuthorData, ApiResponseMetaList>> {
    let params = { ...options?.params };
    params = global_filter ? { ...params, global_filter } : params;
    return this.reqService.get(`user/author_social`, { params });
  }

  searchArticles(page: number, perPage = 10, filters: any): Observable<ApiResult<ArticleSearchResult[], ApiResponseMetaList>> {
    const params = {
      rowCount_limit: perPage.toString(),
      page_limit: page.toString(),
      ...filters,
    };

    return this.reqService.get(`/content-page/search`, {
      params,
    });
  }

  getVariableSponsoredDidYouKnowBox(id: string): Observable<VariableDidYouKnowBox> {
    return this.reqService.get(`/content-group/did-you-know/${id}?getRandomData=1`);
  }

  getParentColumns(): Observable<ApiResult<PrimaryColumn[], ApiResponseMetaList>> {
    return this.reqService.get('/source/content-group/columns?parents_only=1');
  }
}
