@use '../../../../../scss/shared.scss' as *;

:host {
  display: flex;
  width: 100%;

  &.news-reception {
    display: flex;
    flex-direction: column;
    gap: 24px;

    mno-article-page-image {
      width: calc(100% - #{2 * $side-padding});
      margin: 0 #{$side-padding};
    }

    @include media-breakpoint-down(sm) {
      .article-header {
        width: 100%;

        &.wrapper {
          margin: 0 #{$mobile-side-padding};
          width: calc(100% - #{2 * $mobile-side-padding});
        }

        .wrapper {
          width: 100%;
          margin: 0;
        }
      }
      mno-article-page-image {
        width: 100%;
        margin: 0;
      }
      .sponsor-line {
        justify-content: center;
        font-size: 14px;
        line-height: 18px; /* 128.571% */
      }
    }

    .title,
    .lead {
      margin-bottom: 16px;
    }

    .lead {
      font-size: 20px;
      font-weight: 400;
      line-height: 24px; /* 120% */
    }

    .embed-pr-advert {
      width: 100%;
      display: flex;
      justify-content: center;
      margin: 10px auto;
    }

    .info-line {
      padding-top: 16px;

      .info-block {
        gap: 8px;
      }

      .author-container {
        padding-right: 8px;
      }
    }
  }

  .article-header {
    width: 100%;
  }

  .language-line {
    display: flex;
    flex-wrap: wrap;
    padding: 6px;
    justify-content: flex-end;

    .language {
      margin: auto 5px auto 5px;
      text-transform: uppercase;
      font-size: 12px;
      font-weight: 500;

      .icon-flag {
        width: 15px;
        height: 10px;
        margin-right: 2px;
        margin-left: 16px;
        position: relative;

        &.en {
          @include icon('flags/en.png');
        }

        &.de {
          @include icon('flags/de.png');
        }

        &.fr {
          @include icon('flags/fr.png');
        }

        &.hu {
          @include icon('flags/hu.png');
        }
      }
    }
  }

  .lead {
    font-size: 20px;
    font-weight: 400;
    line-height: 24px;
    overflow-wrap: break-word;

    @include media-breakpoint-down(xs) {
      font-size: 16px;
      font-weight: 400;
      line-height: 24px; /* 150% */
    }
  }
}

.title {
  margin-bottom: 8px;
  overflow-wrap: break-word;
}

.title,
.lead {
  @include media-breakpoint-down(xs) {
    hyphens: auto;
  }
}

.info-line {
  margin-top: 8px;
  border-top: 1px solid var(--kui-slate-300);
  display: flex;
  flex-wrap: nowrap;
  padding: 8px 0;
  justify-content: space-between;
  align-items: center;

  .info-block {
    display: flex;
    gap: 2px 16px;
    font-size: 14px;
    line-height: 18px;
    align-items: center;
    flex-wrap: wrap;

    @include media-breakpoint-down(md) {
      column-gap: 8px;
      font-size: 12px;
      line-height: 16px;
    }

    > div,
    > span {
      border-right: 1px solid var(--kui-slate-300);
      padding-right: 16px;
      display: inline-flex;
      vertical-align: baseline;
      align-items: center;
      max-height: 16px;

      @include media-breakpoint-down(md) {
        padding-right: 8px;
      }

      @include media-breakpoint-down(xs) {
        &.source {
          border-right: none;
          padding-right: 0;
        }
      }

      &:last-child {
        padding-right: 0;
        border-right: 0;
      }
    }

    .social-icons {
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
  }

  &.dossier {
    span {
      font-weight: 400;
      color: var(--kui-slate-900);
      display: inline-flex;
      margin-left: 10px;
    }
  }

  .author {
    font-weight: 700;
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    gap: 10px;
    color: var(--kui-slate-950);

    &-pic {
      width: 24px;
      height: 24px;
      object-fit: cover;
    }
  }

  a.author:hover {
    color: var(--kui-blue-700);
  }
}

.photo-data {
  width: 100%;
  flex: 1;
  color: var(--kui-slate-700);
  font-size: 14px;
  font-weight: 400;
  line-height: 18px; /* 128.571% */
  margin-top: 4px;
}

.sponsor-line {
  position: relative;
  margin-top: -35px;
  height: 35px;
  background-color: var(--kui-blue-500);
  color: var(--kui-blue-50);
  width: 100%;
  padding: 8px 8px 6px;
  align-items: center;
  gap: 16px;
  display: flex;

  font-size: 16px;
  font-weight: 700;
  line-height: 21px; /* 131.25% */

  .sponsor-name {
    display: inline-flex;
    padding-left: 16px;
    border-left: 1px solid var(--kui-blue-50);
    height: 16px;
    align-items: center;
  }
}

mno-tag-list {
  margin-bottom: 0;
}

.mt-15 {
  margin-top: 15px;
  display: block;
}
