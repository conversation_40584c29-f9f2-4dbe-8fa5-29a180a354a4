import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, TemplateRef, ViewChild } from '@angular/core';
import {
  AdvertisementAdoceanComponent,
  BlockWrapperTemplateData,
  BreakingNews,
  HtmlEmbedComponent,
  KoponyegComponent,
  KoponyegDefinitions,
  KoponyegType,
  LayoutComponent as KesmaLayoutComponent,
  LayoutContentItemWrapperTemplateData,
  LayoutContentParams,
  LayoutElement,
  LayoutElementContent,
  LayoutElementContentConfiguration,
  LayoutElementContentDetections,
  LayoutElementContentType,
  LayoutElementRow,
  LayoutPageType,
  mapRealEstateApiDataToRealEstateData,
  provideLayoutDataExtractors,
  RealEstateBazaarApiData,
  RealEstateBazaarBackendResponse,
  RealEstateBazaarBlockComponent,
  RealEstateBazaarData,
  RealEstateBazaarSearchBlockComponent,
  SponsoredBoxComponent,
  VoteService,
} from '@trendency/kesma-ui';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../../../../environments/environment';
import { backendDateToDate, EnvironmentType, UtilService } from '@trendency/kesma-core';
import {
  HeroAdapterComponent,
  KoponyegArticleCardComponent,
  KoponyegBlockTitleRowComponent,
  KoponyegDetectionCardsMasonryComponent,
  KoponyegMapRecommendation,
  KoponyegMapRecommendationsComponent,
  MedicalMeteorologyAdapterComponent,
  TextDetectionAdapterComponent,
  TwelveDaysForecastAdapterComponent,
  WeatherMapComponent,
  WeatherMapOsmAdapterComponent,
} from '../../../../shared';
import { AsyncPipe, NgIf } from '@angular/common';
import { KOPONYEG_EXTRACTORS_CONFIG } from '../../extractors/extractor.config';
import { VotingComponent } from 'src/app/shared/components/vote/vote.component';
import { VariableSponsoredDidYouKnowWrapperComponent } from '../../../../shared/components/variable-sponsored-did-you-know-wrapper/variable-sponsored-did-you-know-wrapper.component';

@Component({
  selector: 'app-layout',
  templateUrl: './layout.component.html',
  styleUrls: ['./layout.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    KesmaLayoutComponent,
    KoponyegBlockTitleRowComponent,
    NgIf,
    AdvertisementAdoceanComponent,
    KoponyegDetectionCardsMasonryComponent,
    TextDetectionAdapterComponent,
    MedicalMeteorologyAdapterComponent,
    KoponyegArticleCardComponent,
    KoponyegComponent,
    HeroAdapterComponent,
    HtmlEmbedComponent,
    RealEstateBazaarSearchBlockComponent,
    RealEstateBazaarBlockComponent,
    KoponyegMapRecommendationsComponent,
    TwelveDaysForecastAdapterComponent,
    WeatherMapComponent,
    WeatherMapOsmAdapterComponent,
    VotingComponent,
    SponsoredBoxComponent,
    AsyncPipe,
    VariableSponsoredDidYouKnowWrapperComponent,
  ],
  providers: [provideLayoutDataExtractors(KOPONYEG_EXTRACTORS_CONFIG, true)],
})
export class LayoutComponent {
  @Input() adPageType = '';
  @Input() structure: LayoutElementRow[];
  @Input() configuration: LayoutElementContentConfiguration[];
  @Input() layoutType: LayoutPageType = LayoutPageType.HOME;
  @Input() breakingNews: BreakingNews[] = [];
  @Input() contentComponentsWrapper: TemplateRef<LayoutContentItemWrapperTemplateData>;
  @Input() contentComponentsInnerWrapper: TemplateRef<LayoutContentItemWrapperTemplateData>;
  @Input() blockTitleWrapper: TemplateRef<BlockWrapperTemplateData>;
  @Input() editorFrameSize?: 'desktop' | 'mobile';

  @ViewChild('contentComponents', {
    read: TemplateRef,
    static: false,
  })
  contentComponents: TemplateRef<LayoutContentParams>;

  readonly LayoutPageTypes = LayoutPageType;
  readonly LayoutElementContentType = LayoutElementContentType;
  readonly KoponyegType: KoponyegType = KoponyegType.Light;
  readonly KoponyegDefinitions: KoponyegDefinitions = {
    width: 300,
    height: 100,
  };

  private extractedData: any = {};
  public realEstateData: RealEstateBazaarData[] = [];
  private realEstateDataLoading = false;
  envType: EnvironmentType = environment.type;

  readonly voteCache = this.voteService.voteCache;

  constructor(
    private readonly changeDetector: ChangeDetectorRef,
    public readonly voteService: VoteService,
    public readonly httpClient: HttpClient,
    private readonly utilsService: UtilService
  ) {}

  getData(type: string, layoutElement: LayoutElement, extractor: any, index?: number): any {
    const id = type + layoutElement.id + index;
    // Check for extractedData for cache. We check the cache for articles!!!!
    // If you encounter layout items that does not refresh between routes, their type should be added here.
    if (!this.extractedData[id] || (layoutElement as LayoutElementContent)['contentType'] === LayoutElementContentType.Article) {
      this.extractedData[id] = this.getExtractorData(type, layoutElement, extractor, index);
    }

    return this.extractedData[id];
  }

  getExtractorData(type: string, layoutElement: LayoutElement, extractor: any, index?: number): any {
    return index || index == 0 ? extractor[type]?.(layoutElement, index) : extractor[type]?.(layoutElement);
  }

  getArticleData(layoutElement: LayoutElement, extractor: any, index: number): any {
    const data = this.getData('getArticleData', layoutElement, extractor, index);
    if (data) {
      return { ...data, publishDate: backendDateToDate(data.publishDate) };
    }
  }

  handleRealEstateInitEvent(): void {
    if (this.utilsService.isBrowser() && !this.realEstateDataLoading && this.realEstateData.length < 1) {
      this.getRealEstateData();
    }
  }

  getRealEstateData(): void {
    this.realEstateDataLoading = true;
    const realEstates: Array<RealEstateBazaarData> = [];
    // eslint-disable-next-line max-len
    this.httpClient
      .get(
        'https://www.ingatlanbazar.hu/api/property-search?property_location=6,1000000004,' +
          '1000000005,1000000006,1000000007&amp;;property_newbuildonly=on&amp;property__2=3_2'
      )
      .subscribe((data: RealEstateBazaarBackendResponse) => {
        data?.hits?.forEach((realEstate: RealEstateBazaarApiData) => {
          realEstates.push(mapRealEstateApiDataToRealEstateData(realEstate));
        });
        this.realEstateData = realEstates;
        this.realEstateDataLoading = false;
        this.changeDetector.detectChanges();
      });
  }

  isAutoColsMasonry(element: LayoutElementContentDetections): boolean {
    return !(element.contentLength === 1 || element?.parentWidth <= element?.minParentWidth || this.layoutType === 'Sidebar');
  }

  clearExtractedData(): void {
    this.extractedData = {};
  }

  getMapRecommendations(config: KoponyegMapRecommendation[]): number[] {
    return config.map((item: KoponyegMapRecommendation) => item.styleId);
  }
}
