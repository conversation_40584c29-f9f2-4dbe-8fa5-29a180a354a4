<kesma-layout
  [breakingNews]="breakingNews"
  [configuration]="configuration"
  [layoutType]="layoutType"
  [structure]="structure"
  [adPageType]="adPageType"
  [blockTitleRef]="blockTitles"
  [blockTitleAfterComponentRef]="blockTitleAfterComponent"
  [contentComponentsRef]="contentComponents"
  [contentComponentWrapperRef]="contentComponentsWrapper"
  [contentComponentInnerWrapperRef]="contentComponentsInnerWrapper"
  [blockTitleWrapperRef]="blockTitleWrapper"
  [editorFrameSize]="editorFrameSize"
></kesma-layout>

<ng-template #blockTitles let-layoutType="layoutType" let-layoutElement="layoutElement">
  <koponyeg-block-title-row [data]="layoutElement.blockTitle" [isRow]="layoutType !== LayoutPageTypes.SIDEBAR"> </koponyeg-block-title-row>
</ng-template>

<ng-template #blockTitleAfterComponent let-layoutElement="layoutElement">
  <ng-container *ngIf="layoutElement?.blockTitle?.url && layoutElement?.blockTitle?.urlName">
    <koponyeg-block-title-row [data]="layoutElement.blockTitle" [isOnlyUrl]="true"></koponyeg-block-title-row>
  </ng-container>
</ng-template>

<ng-template #contentComponents let-layoutElement="layoutElement" let-index="index" let-first="first" let-last="last" let-desktopWidth="desktopWidth">
  <ng-container *ngIf="layoutElement?.config || layoutElement?.configurable === false">
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Ad">
      <kesma-advertisement-adocean
        *ngIf="layoutElement.ad"
        [ad]="layoutElement.ad"
        [isHidden]="layoutElement.contentType !== LayoutElementContentType.Ad && !layoutElement.ad"
      ></kesma-advertisement-adocean>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.DETECTIONS && index === 0">
      <ng-container *ngIf="layoutElement?.useComponent; else textDetection">
        <koponyeg-detection-cards-masonry [autoCols]="isAutoColsMasonry(layoutElement)" [data]="layoutElement.extractorData">
        </koponyeg-detection-cards-masonry>
      </ng-container>

      <ng-template #textDetection>
        <app-text-detection-adapter [layoutElement]="layoutElement" [data]="layoutElement.extractorData"></app-text-detection-adapter>
      </ng-template>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.MEDICAL_METEOROLOGY">
      <app-medical-meteorology-adapter></app-medical-meteorology-adapter>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Article">
      <koponyeg-article-card
        *ngIf="layoutElement.extractorData?.[index] as data"
        [styleID]="layoutElement.styleId"
        [desktopWidth]="layoutType !== LayoutPageTypes.SIDEBAR ? desktopWidth : 3"
        [data]="data"
      ></koponyeg-article-card>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.koponyeg">
      <kesma-koponyeg [data]="KoponyegDefinitions" [type]="KoponyegType"></kesma-koponyeg>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.HERO">
      <app-hero-adapter [data]="layoutElement.extractorData" [envType]="envType"></app-hero-adapter>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.HtmlEmbed">
      <kesma-html-embed *ngIf="layoutElement?.config?.htmlContent as data" [data]="data"></kesma-html-embed>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.IngatlanbazarSearch">
      <kesma-real-estate-bazaar-search-block
        [showBudapestLocations]="layoutElement.config.showBudapestLocations"
        [showCountyLocations]="layoutElement.config.showBudapestLocations"
        [showOtherLocations]="layoutElement.config.showOtherLocations"
        [showNewBuildButton]="layoutElement.config.showNewBuildButton"
        [showAdvertiseButton]="layoutElement.config.showAdvertiseButton"
        [defaultLocation]="layoutElement.config.defaultLocation"
        [defaultType]="layoutElement.config.defaultType"
        [utmSource]="layoutElement.config.utmSource"
      ></kesma-real-estate-bazaar-search-block>
    </ng-container>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Vote">
    @if (layoutElement.extractorData; as extractorData) {
      @if ((voteCache[extractorData?.data?.id] | async) || extractorData; as voteData) {
        <app-voting [voteData]="voteData" [data]="voteData?.data" [voteId]="voteData?.votedId" />
      }
    }
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.IngatlanbazarConfigurable">
    <kesma-real-estate-bazaar-block
      (initEvent)="handleRealEstateInitEvent()"
      [showHeader]="layoutElement.showHeader"
      [itemsToShow]="layoutElement.itemsToShow"
      [data]="realEstateData"
    ></kesma-real-estate-bazaar-block>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.INGATLANBAZAR">
    <kesma-real-estate-bazaar-block
      (initEvent)="handleRealEstateInitEvent()"
      [showHeader]="true"
      [itemsToShow]="1"
      [data]="realEstateData"
    ></kesma-real-estate-bazaar-block>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.MAP_RECOMMENDATIONS && index === 0">
    <koponyeg-map-recommendations [data]="getMapRecommendations(layoutElement?.mapConfig)"></koponyeg-map-recommendations>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.TWELVE_DAYS_FORECAST">
    <app-twelve-days-forecast-adapter></app-twelve-days-forecast-adapter>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.IMAGE_MAP_LIST">
    <koponyeg-weather-map [mapType]="layoutElement.mapType" [showTitle]="true" [speed]="1000"></koponyeg-weather-map>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.DRAWN_MAP_LIST">
    <app-weather-map-osm-adapter [layoutElement]="layoutElement"></app-weather-map-osm-adapter>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.CONFIGURABLE_SPONSORED_BOX">
    <kesma-sponsored-box [data]="layoutElement.config"></kesma-sponsored-box>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.DID_YOU_KNOW">
    <app-variable-sponsored-did-you-know-wrapper [id]="layoutElement.config?.selectedDidYouKnowBox?.[0]?.id"></app-variable-sponsored-did-you-know-wrapper>
  </ng-container>

  <!-- Blocktitle must be the last element!!! -->
  <ng-container *ngIf="last && layoutType === LayoutPageTypes.HOME && layoutElement.blockTitle?.url">
    <koponyeg-block-title-row [data]="layoutElement.blockTitle" [isOnlyUrl]="true"></koponyeg-block-title-row>
  </ng-container>
</ng-template>
