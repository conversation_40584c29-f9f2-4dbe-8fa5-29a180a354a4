import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { EmbeddingService, UtilService } from '@trendency/kesma-core';
import { ArticleCard, Layout, LayoutContent, LayoutElementRow, LayoutPageType, LayoutService, PAGE_TYPES } from '@trendency/kesma-ui';
import { forkJoin, of } from 'rxjs';
import { catchError, mergeMap, tap } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { LayoutComponent } from '../layout/layout.component';
import { ApiService } from '../../../../shared';
import { NgIf } from '@angular/common';

@Component({
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [LayoutComponent, NgIf],
})
export class SidebarComponent implements OnInit, OnChanges, AfterViewInit {
  @Input() adPageType = PAGE_TYPES.all_articles_and_sub_pages;
  @Input() categorySlug?: string;
  @Input() articleId?: string;
  @Input() articleSlug?: string;
  @Input() columnSlug?: string;
  @Input() excludedIds: string[] = [];
  @Output() populatedEvent = new EventEmitter<boolean>();
  @ViewChild('layout') layoutComponent: LayoutComponent;
  layoutApiData?: Layout;
  layoutReady = false;

  LayoutPageType = LayoutPageType;

  constructor(
    private readonly api: ApiService,
    private readonly layoutService: LayoutService,
    private readonly embedding: EmbeddingService,
    private readonly utilsService: UtilService,
    private readonly changeRef: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.populateSidebar();
  }

  get structure(): LayoutElementRow[] {
    return (this.layoutApiData?.struct ?? []) as LayoutElementRow[];
  }

  populateSidebar(): void {
    const allExcludedId = [...(this.articleId ? [this.articleId] : []), ...this.excludedIds];

    this.layoutService
      .getSidebar(this.categorySlug, this.articleSlug)
      .pipe(
        tap(() => {
          this.layoutApiData = undefined;
          this.changeRef.detectChanges();
        }),
        mergeMap((layoutApiResponse) =>
          forkJoin([
            of(layoutApiResponse),
            this.api.getSidebarArticleRecommendations(this.getArticleCount(layoutApiResponse?.data?.content), this.categorySlug, allExcludedId).pipe(
              catchError((error) => {
                if (!environment.production && this.utilsService.isBrowser()) {
                  console.warn('[dev] Unable to download sidebar: ', error);
                }
                return [];
              })
            ),
          ])
        )
      )
      .subscribe(([{ data: layoutApiData }, { data: recommendedArticles }]) => {
        this.layoutComponent?.clearExtractedData();
        this.layoutApiData = layoutApiData;
        this.fillLayoutContent(recommendedArticles, []);
        this.layoutReady = true;
        this.changeRef.detectChanges();
        setTimeout(() => {
          this.embedding.loadEmbedMedia();
        }, 0);
        this.populatedEvent.emit(true);
      });
  }

  ngAfterViewInit(): void {
    setTimeout(() => {
      this.embedding.loadEmbedMedia();
    }, 0);
  }

  ngOnChanges(changes: SimpleChanges): void {
    // need sidebar refetch to avoid showing the same article in sidebar that is currently displayed on detail page
    if (
      (changes['articleId']?.currentValue && !changes['articleId']?.firstChange) ||
      (changes['excludedIds']?.currentValue && !changes['excludedIds']?.firstChange)
    ) {
      this.populateSidebar();
    }
  }

  getArticleCount(content: LayoutContent[]): number {
    // no flat() ??? -> upgrade to es2019
    return content.map(({ selectedArticles }) => selectedArticles).reduce((acc, val) => acc.concat(val), []).length;
  }

  getOpinionCount(content: LayoutContent[]): number {
    // no flat() ??? -> upgrade to es2019
    return content.map(({ selectedOpinions }) => selectedOpinions).reduce((acc, val) => acc.concat(val), []).length;
  }

  fillLayoutContent(articles: ArticleCard[], opinions: ArticleCard[]): void {
    let articleCursor = 0;
    let opinionCursor = 0;
    this.layoutApiData?.content.forEach(({ selectedArticles, selectedOpinions }) => {
      if (selectedArticles?.length) {
        for (let i = 0; i < selectedArticles.length; i++) {
          // only overwrite null values
          if (!selectedArticles[i] && articles[articleCursor]) {
            selectedArticles[i] = {
              id: articles[articleCursor]?.id ?? '',
              // TODO: mismatching types????
              data: articles[articleCursor] as any,
            };
            articleCursor++;
          }
        }
      } else if (selectedOpinions?.length) {
        for (let i = 0; i < selectedOpinions.length; i++) {
          // only overwrite null values
          if (selectedOpinions[i]) {
            selectedOpinions[i] = {
              id: opinions[opinionCursor]?.id ?? '',
              // TODO: mismatching types????
              data: opinions[opinionCursor] as any,
            };
            opinionCursor++;
          }
        }
      }
    });
  }
}
