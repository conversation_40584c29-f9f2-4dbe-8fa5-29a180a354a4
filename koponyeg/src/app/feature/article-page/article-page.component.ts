import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>or<PERSON><PERSON>, <PERSON><PERSON>f, <PERSON><PERSON><PERSON>, Ng<PERSON><PERSON>C<PERSON>, NgTemplateOutlet } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { Dom<PERSON>anitizer, SafeHtml } from '@angular/platform-browser';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { FormatDatePipe, IMetaData, SchemaOrgService, SeoService, StorageService, UtilService } from '@trendency/kesma-core';
import {
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  AnalyticsService,
  Article,
  ArticleBody,
  ArticleBodyDetails,
  ArticleBodyType,
  ArticleCard,
  ArticleFileLinkDirective,
  ArticleResolverData,
  ArticleRouteParams,
  ArticleVideoComponent,
  FocusPointDirective,
  GalleryData,
  GalleryElementData,
  getStructuredDataForArticle,
  NativtereloComponent,
  PAGE_TYPES,
  SponsoredTag,
  ThumbnailImage,
  VoteData,
  VoteDataWithAnswer,
  VoteService,
} from '@trendency/kesma-ui';
import { combineLatest, forkJoin, Observable, Subject } from 'rxjs';
import { map, switchMap, takeUntil } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import {
  ArticleCardType,
  defaultMetaInfo,
  GalleryService,
  KoponyegArticleCardComponent,
  KoponyegPopup,
  KoponyegPopupComponent,
  KoponyegPopupTypes,
  KoponyegPublishDatePipe,
  KoponyegSliderGalleryComponent,
  KoponyegSocialButtonsComponent,
  KoponyegTagComponent,
  KoponyegWysiwygBoxComponent,
  MenuItemType,
  PersonalizedRecommendationService,
  previewBackendArticleToArticleCard,
  SponsoredTagBoxComponent,
  TagColor,
  TagType,
} from '../../shared';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { VotingComponent } from 'src/app/shared/components/vote/vote.component';

@Component({
  selector: 'app-article-page',
  templateUrl: './article-page.component.html',
  styleUrls: ['./article-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [FormatDatePipe],
  imports: [
    NativtereloComponent,
    FocusPointDirective,
    KoponyegPopupComponent,
    KoponyegPublishDatePipe,
    ArticleFileLinkDirective,
    KoponyegTagComponent,
    KoponyegSocialButtonsComponent,
    AdvertisementAdoceanComponent,
    KoponyegArticleCardComponent,
    KoponyegWysiwygBoxComponent,
    ArticleVideoComponent,
    KoponyegSliderGalleryComponent,
    NgIf,
    AsyncPipe,
    RouterLink,
    NgForOf,
    NgTemplateOutlet,
    NgSwitch,
    NgSwitchCase,
    SidebarComponent,
    VotingComponent,
    SponsoredTagBoxComponent,
  ],
})
export class ArticlePageComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('dataTrigger', { static: false }) readonly dataTrigger: ElementRef<HTMLDivElement>;
  @ViewChild('externalRecommendationsBlock', { static: false }) readonly externalRecommendationsBlock: ElementRef<HTMLDivElement>;

  adPageType = PAGE_TYPES.all_articles_and_sub_pages;
  adverts: AdvertisementsByMedium;
  article: Article;
  articleSlug = '';
  categorySlug = '';
  parentCategorySlug? = '';
  lowPriorityArticles: ArticleCard[] = [];
  highPriorityArticles: ArticleCard[] = [];
  lastThreeDaysMostReadArticles: ArticleCard[] = [];
  videos: ArticleCard[] = [];
  isUserAdultChoice: boolean;
  hideDate = false;
  metaData: IMetaData;
  galleries: Record<string, GalleryData> = {};
  categoryArticles: ArticleCard[];
  public readonly ArticleBodyType = ArticleBodyType;
  public readonly TagType = TagType;
  public readonly KoponyegPopupTypes = KoponyegPopupTypes;
  readonly ArticleCardType = ArticleCardType;
  readonly TagColor = TagColor;
  embedPrAdvert?: SafeHtml;
  // articleLink: string[] = [];
  adultWarningContent: KoponyegPopup = {
    icon: 'icon-warning',
    title: 'Figyelem!',
    content:
      'Az Ön által megnyitni kívánt oldal a nyugalom megzavarására alkalmas szöveget,' +
      'képeket, vagy hanghatást tartalmazhat. Csak erős idegzetű felhasználóinknak javasoljuk a cikk megnyitását.',
    acceptLabel: 'Cikk megnyitása',
    cancelLabel: 'Vissza a címlapra',
  };
  googleNews = 'https://news.google.com/publications/CAAqJQgKIh9DQklTRVFnTWFnMEtDMnR2Y0c5dWVXVm5MbWgxS0FBUAE?ceid=HU:hu&oc=3';
  tereloUrl: string =
    'https://terelo.mediaworks.hu/nativterelo/nativterelo.html?utmSource=koponyeg.hu' +
    '&traffickingPlatforms=K%C3%B6p%C3%B6nyeg%20Nat%C3%ADv' +
    '&domain=Köpönyeg';
  // Mobile app related parts:
  showCompleteArticlePage$: Observable<boolean> = this.route.data.pipe(
    map((data) => data['isMobileApp']),
    map((onlyBody) => !onlyBody)
  );
  skipArticleAnalytics$: Observable<boolean> = this.route.data.pipe(
    map((data) => data['skipArticleAnalytics']),
    map((onlyBody) => !!onlyBody)
  );
  externalRecommendation: ArticleCard[] = [];
  private url: string;
  private cannonicalUrl: string;
  private readonly unsubscribe$: Subject<boolean> = new Subject();
  isExceptionAdvertEnabled: boolean;
  sponsoredTag?: SponsoredTag;

  readonly voteCache = this.voteService.voteCache;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    public readonly seo: SeoService,
    private readonly utilsService: UtilService,
    private readonly storage: StorageService,
    private readonly adStore: AdvertisementAdoceanStoreService,
    private readonly schemaService: SchemaOrgService,
    private readonly analyticsService: AnalyticsService,
    private readonly changeDetectorRef: ChangeDetectorRef,
    private readonly galleryService: GalleryService, // @Inject(DOCUMENT) private readonly document: Document,
    private readonly formatDate: FormatDatePipe,
    private readonly sanitizer: DomSanitizer,
    private readonly personalizedRecommendationService: PersonalizedRecommendationService,
    private readonly voteService: VoteService
  ) {}

  get showCompleteArticlePage(): boolean {
    return !this.route.snapshot.data?.['isMobileApp'];
  }

  get skipArticleAnalytics(): boolean {
    return !!this.route.snapshot.data?.['skipArticleAnalytics'];
  }

  get externalRecommendations(): ArticleCard[] {
    return this.route.snapshot.data?.['data']?.recommendations?.data?.externalRecommendation;
  }

  ngOnInit(): void {
    (this.route.data as Observable<{ data: ArticleResolverData }>).pipe(takeUntil(this.unsubscribe$)).subscribe(
      ({
        data: {
          article: { data: article },
          recommendations,
          articleSlug,
          categorySlug,
          url,
          article: { meta },
        },
      }: {
        data: ArticleResolverData;
      }) => {
        this.article = {
          ...article,
          slug: articleSlug,
          body: this.prepareArticleBody(article.body),
          minuteToMinuteBlocks: article.minuteToMinuteBlocks?.map((block) => ({
            ...block,
            body: this.prepareArticleBody(block.body),
          })),
          excerpt: article?.lead || article?.excerpt,
        };
        this.embedPrAdvert = this.sanitizer.bypassSecurityTrustHtml(this.article?.embedPrAdvert ?? '');
        this.articleSlug = articleSlug;
        this.categorySlug = categorySlug ?? this.article?.primaryColumn?.slug;
        this.parentCategorySlug = this.article?.primaryColumn?.parent?.slug;
        this.hideDate = this.parentCategorySlug === MenuItemType.Tudasfelho || this.categorySlug === MenuItemType.Tudasfelho;
        this.sponsoredTag = meta?.['sponsoredTag'];

        this.url = url ?? '';
        this.cannonicalUrl = this.article.seo?.seoCanonicalUrl || this.article?.canonicalUrl || `${this.seo.hostUrl}/${this.url}`;
        this.isUserAdultChoice = (this.storage.getSessionStorageData('isAdultChoice', false) ?? false) && this.article.isAdultsOnly;
        this.adStore.setIsAdultPage(this.isUserAdultChoice);

        const { lowPriorityArticles, highPriorityArticles, lastThreeDaysMostReadArticles, videos, categoryArticles } = recommendations?.data ?? {
          videos: [],
          lowPriorityArticles: [],
          highPriorityArticles: [],
          lastThreeDaysMostReadArticles: [],
          categoryArticles: [],
        };
        this.lastThreeDaysMostReadArticles = lastThreeDaysMostReadArticles;
        this.lowPriorityArticles = lowPriorityArticles;
        this.highPriorityArticles = highPriorityArticles;
        this.externalRecommendation = [...this.highPriorityArticles, ...this.lowPriorityArticles];
        this.externalRecommendation = this.externalRecommendation.filter((article) => article.id !== this.article.id);
        this.getPersonalizedRecommendations();

        if (categoryArticles && categoryArticles.length > 0) {
          this.categoryArticles = categoryArticles
            .filter((article: ArticleCard) => article.id !== this.article.id)
            .map((article: ArticleCard) => {
              return {
                ...article,
                thumbnail: { url: article?.thumbnail ? article?.thumbnail : '', alt: article.title } as ThumbnailImage,
              };
            });
        } else {
          this.categoryArticles = [];
        }

        this.categoryArticles =
          categoryArticles && categoryArticles.length > 0
            ? categoryArticles
                .filter((article: ArticleCard) => article.id !== this.article.id)
                .map((article: ArticleCard) => ({
                  ...article,
                  thumbnail: { url: article?.thumbnail ? article?.thumbnail : '', alt: article.title } as ThumbnailImage,
                }))
            : [];
        this.videos = videos;
        this.seo.updateCanonicalUrl(this.cannonicalUrl, {
          skipSeoMetaCheck: true,
          addHostUrl: false,
        });
        this.loadEmbeddedGalleries();
        // this.checkIsLogged();
        if (this.article) {
          this.schemaService.insertSchema(getStructuredDataForArticle(this.article, this.seo.currentUrl, environment?.siteUrl ?? ''));
          this.voteService.initArticleVotes(this.article);
        }
        this.setMetaData();

        this.changeDetectorRef.markForCheck();

        setTimeout(() => {
          if (this.skipArticleAnalytics) {
            return;
          }
          this.analyticsService.sendPageView({
            pageCategory: this.categorySlug,
            customDim2: this.article?.topicLevel1,
            customDim1: this.article?.aniCode,
            title: this.article.title,
            articleSource: this.article.articleSource ? this.article.articleSource : 'no source',
            publishDate: this.formatDate.transform(this.article.publishDate as Date, 'dateTime'),
            lastUpdatedDate: this.formatDate.transform((this.article.lastUpdated ? this.article.lastUpdated : this.article.publishDate) as Date, 'dateTime'),
          });
        }, 0);
      }
    );

    (
      combineLatest([this.route.data as Observable<{ data: ArticleResolverData }>, this.adStore.isAdult.asObservable()]) as any as Observable<
        [{ data: ArticleResolverData }, boolean]
      >
    )
      .pipe(takeUntil(this.unsubscribe$))
      .pipe(
        map<[{ data: ArticleResolverData }, boolean], boolean | undefined>(
          ([
            {
              data: { article },
            },
          ]) => {
            this.isExceptionAdvertEnabled = article?.data.isExceptionAdvertEnabled;

            return article?.data?.withoutAds;
          }
        )
      )
      .pipe(
        switchMap((withoutAds) => {
          withoutAds ? this.adStore.disableAds() : this.adStore.enableAds();
          return this.adStore.advertisemenets$;
        }),
        map((adsCollection): AdvertisementsByMedium => this.adStore.separateAdsByMedium(adsCollection, this.adPageType))
      )
      .subscribe((ads: AdvertisementsByMedium): void => {
        this.adverts = ads;

        this.adStore.onArticleLoaded();
        this.changeDetectorRef.detectChanges();
      });
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
    this.adStore.onArticleDestroy();
    this.schemaService.removeStructuredData();
  }

  ngAfterViewInit(): void {
    if (this.utilsService.isBrowser()) {
      setTimeout(() => {
        this.route.data.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {
          if ('IntersectionObserver' in window) {
            this.observeExternalRecommendations();
            this.observeArticleEnd();
          }
        });
      }, 1000);
    }
  }

  onIsUserAdultChoose(isUserAdult: boolean): void {
    if (!isUserAdult) {
      this.router.navigate(['/']);
    } else {
      this.isUserAdultChoice = isUserAdult;
      this.adStore.setIsAdultPage(isUserAdult);
      // this.initDataLayer();
    }
  }

  getVoteData(value: VoteData): VoteDataWithAnswer {
    return this.voteService.getVoteData(value);
  }

  doubleArticleRecommendations(arr: ArticleBodyDetails[]): ArticleBodyDetails[] {
    return arr.filter((elem: ArticleBodyDetails) => elem?.value?.id);
  }

  doubleArticleRecommendation(articleBodyDetails: ArticleBodyDetails[]): ArticleCard[] {
    return this.doubleArticleRecommendations(articleBodyDetails)?.map((details: ArticleBodyDetails) => {
      const article = details.value;
      if (article.thumbnailUrl) {
        article.thumbnail = { url: article?.thumbnailUrl };
      }
      return {
        ...article,
        lead: article?.excerpt || article?.lead,
        category: {
          name: article?.primaryColumn?.title,
          slug: article?.primaryColumn?.slug,
        },
      };
    }) as ArticleCard[];
  }

  simpleArticleRecommendation(articleBodyDetail: ArticleBodyDetails): ArticleCard {
    const article = articleBodyDetail.value;
    return {
      ...article,
      columnSlug: article?.category?.slug,
    } as ArticleCard;
  }

  getDoubleArticleRecommendationTitle(arr: ArticleBodyDetails[]): string {
    return arr.filter((elem: ArticleBodyDetails) => elem?.key === 'title')[0].value || 'Kapcsolódó tartalom';
  }

  getDoubleArticleRecommendationLead(arr: ArticleBodyDetails[]): string {
    return arr.filter((elem: ArticleBodyDetails) => elem?.key === 'lead')[0].value;
  }

  getPersonalizedRecommendations(): void {
    if (this.utilsService.isBrowser()) {
      this.personalizedRecommendationService
        .getPersonalizedRecommendations()
        .pipe(takeUntil(this.unsubscribe$))
        .subscribe((data: ArticleCard[]): void => {
          this.externalRecommendation = data;
          this.changeDetectorRef.detectChanges();
        });
    }
  }

  private setMetaData(): void {
    const { thumbnail, publicAuthor, publishDate, alternativeTitle, metaThumbnail } = this.article || {};
    if (!this.article) {
      return;
    }

    const title = this.article.seo?.seoTitle || this.article.title;
    const finalTitle = alternativeTitle ?? title;
    const finalOgTitle = alternativeTitle && alternativeTitle.length > 0 ? alternativeTitle : this.article.title;
    this.metaData = {
      ...defaultMetaInfo,
      title: finalTitle,
      description: this.article.seo?.seoDescription || this.article.excerpt || this.article.lead || defaultMetaInfo.description,
      robots: this.article.seo?.seoRobotsMeta || 'index, follow, max-image-preview:large',
      keywords: '',
      ogTitle: finalOgTitle,
      ogImage: metaThumbnail || thumbnail,
      ogType: 'article',
      articleAuthor: publicAuthor,
      articlePublishedTime: publishDate?.toISOString(),
    };

    if (!this.showCompleteArticlePage) {
      this.metaData = { robots: 'noindex, nofollow' };
    }

    this.seo.setMetaData(this.metaData);
  }

  private loadEmbeddedGalleries(): void {
    const bodyElements = (this.article?.body as any) ?? [];
    const gallerySubs = ((bodyElements ?? []) as GalleryElementData[])
      .filter(({ type }) => type === ArticleBodyType.Gallery)
      .filter((bodyElem) => !!bodyElem.details[0].value)
      .map((bodyElem: GalleryElementData) => this.galleryService.getGalleryDetails(bodyElem.details[0].value.slug));

    forkJoin(gallerySubs)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((galleries) => {
        galleries.forEach((gallery) => {
          this.galleries[gallery.id] = {
            ...gallery,
            highlightedImageUrl: gallery.highlightedImage.url,
          } as GalleryData;
        });
        this.changeDetectorRef.markForCheck();
      });
  }

  private observeArticleEnd(): void {
    if (!this.dataTrigger?.nativeElement) {
      return;
    }

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(({ isIntersecting }) => {
        if (isIntersecting) {
          this.sendEcommerceEvent();
          observer.unobserve(this.dataTrigger.nativeElement);
        }
      });
    });
    observer.observe(this.dataTrigger.nativeElement);
  }

  private observeExternalRecommendations(): void {
    if (!this.externalRecommendationsBlock?.nativeElement) {
      return;
    }

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(({ isIntersecting }) => {
        if (isIntersecting && this.externalRecommendation) {
          this.personalizedRecommendationService.sendPersonalizedRecommendationAv(this.externalRecommendation).subscribe();
          observer.unobserve(this.externalRecommendationsBlock.nativeElement);
        }
      });
    });
    observer.observe(this.externalRecommendationsBlock.nativeElement);
  }

  private sendEcommerceEvent(): void {
    const routeParams: ArticleRouteParams = this.route.snapshot.params as ArticleRouteParams;
    this.analyticsService.sendEcommerceEvent({
      id: `T${this.article.id}`,
      title: this.article.title,
      articleSlug: routeParams.articleSlug ? routeParams.articleSlug : 'cikk-elonezet',
      category: this.article.columnTitle ?? '',
      articleSource: this.article.articleSource ? this.article.articleSource : 'no source',
      publishDate: this.formatDate.transform(this.article.publishDate as Date, 'dateTime'),
      lastUpdatedDate: this.formatDate.transform((this.article.lastUpdated ? this.article.lastUpdated : this.article.publishDate) as Date, 'dateTime'),
    });
  }

  private prepareArticleBody(body: ArticleBody[]): ArticleBody[] {
    return body
      .filter((bodyPart: ArticleBody) => !(!this.showCompleteArticlePage && bodyPart.type === ArticleBodyType.Advert))
      .map((bodyPart: ArticleBody) => ({
        ...bodyPart,
        details: (bodyPart.details ?? []).map((detail: ArticleBodyDetails) => ({
          ...detail,
          ...this.prepareArticleBodyDetail(detail, bodyPart.type),
        })),
      }));
  }

  private prepareArticleBodyDetail(detail: ArticleBodyDetails, type: ArticleBodyType): ArticleBodyDetails {
    let newDetail: ArticleBodyDetails;
    switch (type) {
      case ArticleBodyType.Article:
        newDetail = {
          ...detail,
          value: previewBackendArticleToArticleCard(detail.value),
        };
        break;
      default:
        newDetail = { ...detail };
    }
    return newDetail;
  }
}
