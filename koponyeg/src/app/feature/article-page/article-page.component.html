<ng-container *ngIf="!isUserAdultChoice && article?.isAdultsOnly && (showCompleteArticlePage$ | async); else adultContent" class="article-page">
  <koponyeg-popup (onUserAnswer)="onIsUserAdultChoose($event)" [data]="adultWarningContent" [styleID]="KoponyegPopupTypes.WARNING"></koponyeg-popup>
</ng-container>

<ng-template #adultContent>
  <section *ngIf="article" [class.mobile-app-article]="(showCompleteArticlePage$ | async) === false" class="article-page">
    <div [class.with-aside]="showCompleteArticlePage$ | async" class="wrapper">
      <div class="left-column">
        <ng-container *ngIf="showCompleteArticlePage$ | async">
          <h1 class="article-page-title">{{ article?.title }}</h1>
          <p class="article-page-lead">{{ article?.lead || article?.excerpt }}</p>

          <div *ngIf="embedPrAdvert" [innerHTML]="embedPrAdvert" class="article-page-embed-pr-advert"></div>

          <div class="article-page-social">
            <div class="side-left">
              <koponyeg-tag [label]="article?.primaryColumn?.title" [slug]="article?.primaryColumn?.slug" [styleID]="TagType.CategoryTag"></koponyeg-tag>

              <ng-container *ngFor="let tag of article.tags">
                <a [routerLink]="['/cimke', tag.slug]">
                  <koponyeg-tag [hasUrl]="false" [label]="tag.title"></koponyeg-tag>
                </a>
              </ng-container>

              <div *ngIf="!hideDate" class="article-page-published">{{ article?.publishDate | koponyegPublishDate }}</div>
            </div>

            <div class="social">
              <koponyeg-social-buttons [link]="seo.currentUrl" [title]="article?.title"></koponyeg-social-buttons>
            </div>
          </div>

          <figure *ngIf="article.thumbnail && !article.hideThumbnailFromBody">
            <img
              [alt]="article.thumbnailInfo?.altText"
              [data]="article?.thumbnailFocusedImages"
              [displayedAspectRatio]="{ desktop: '16:9' }"
              [displayedUrl]="article.thumbnail"
              class="article-page-thumbnail"
              loading="eager"
              withFocusPoint
            />
          </figure>

          <kesma-advertisement-adocean
            *ngIf="adverts?.desktop?.roadblock_1 as ad"
            [isExceptionAdvertEnabled]="isExceptionAdvertEnabled"
            [ad]="ad"
            [style]="{
              margin: 'var(--ad-margin)',
            }"
          />
        </ng-container>

        <ng-container *ngIf="showCompleteArticlePage$ | async">
          <kesma-advertisement-adocean
            *ngIf="adverts?.mobile?.box_1 as ad"
            [isExceptionAdvertEnabled]="isExceptionAdvertEnabled"
            [ad]="ad"
            [style]="{
              margin: 'var(--ad-margin)',
            }"
          ></kesma-advertisement-adocean>
        </ng-container>

        <!-- body -->
        <ng-container [ngTemplateOutletContext]="{ body: article.body }" [ngTemplateOutlet]="bodyContent"></ng-container>

        <kesma-nativterelo [tereloUrl]="tereloUrl"></kesma-nativterelo>

        <!-- Cimke alapú szponzorált cikkek -->
        @if (sponsoredTag) {
          <app-sponsored-tag-box [sponsoredTag]="sponsoredTag" [excludedSlug]="article?.slug || ''" />
        }

        <!-- external recommendation -->
        <section class="recommendation-wrapper">
          <div #externalRecommendationsBlock class="recommendation-block recommendation-block-external">
            <ng-container *ngFor="let article of externalRecommendations; let i = index">
              <koponyeg-article-card
                [data]="article"
                [hasTagUrl]="false"
                [styleID]="ArticleCardType.ImgTagTitle"
                [tagColor]="TagColor.Blue"
              ></koponyeg-article-card>

              <div *ngIf="i === 5" class="full-row">
                <kesma-advertisement-adocean
                  *ngIf="adverts?.mobile?.mobilrectangle_ottboxextra as ad"
                  [ad]="ad"
                  [style]="{
                    margin: 'var(--ad-margin)',
                  }"
                ></kesma-advertisement-adocean>
                <kesma-advertisement-adocean
                  *ngIf="adverts?.desktop?.roadblock_ottboxextra as ad"
                  [ad]="ad"
                  [style]="{
                    margin: 'var(--ad-margin)',
                  }"
                ></kesma-advertisement-adocean>
              </div>
            </ng-container>
          </div>
        </section>

        <div class="google-news-wrapper">
          <figure class="google-news">
            <a [href]="googleNews" target="_blank">
              <img loading="eager" alt="Google News" src="/assets/images/google-news.svg" />
            </a>
          </figure>
          <span class="google-news-text"
            >A legfrissebb hírekért kövess minket a
            <a class="google-news-link" target="_blank" [href]="googleNews"> Köpönyeg </a>
            Google News oldalán is!</span
          >
        </div>

        <div *ngIf="article" #dataTrigger></div>

        <ng-container *ngIf="showCompleteArticlePage$ | async">
          <kesma-advertisement-adocean
            *ngIf="adverts?.mobile?.box_2 as ad"
            [ad]="ad"
            [style]="{
              margin: 'var(--ad-margin)',
            }"
          ></kesma-advertisement-adocean>
        </ng-container>
      </div>

      <aside *ngIf="showCompleteArticlePage$ | async">
        <app-sidebar
          *ngIf="articleSlug && categorySlug"
          [adPageType]="adPageType"
          [articleId]="article?.id"
          [articleSlug]="articleSlug"
          [categorySlug]="categorySlug"
        ></app-sidebar>
      </aside>
    </div>
  </section>
</ng-template>

<ng-template #bodyContent let-body="body">
  <ng-container *ngFor="let element of body">
    <ng-container [ngSwitch]="element.type">
      <ng-container *ngSwitchCase="ArticleBodyType.Wysywyg">
        <ng-container *ngFor="let wysiwygDetail of element?.details">
          <koponyeg-wysiwyg-box [html]="wysiwygDetail?.value || ''" trArticleFileLink></koponyeg-wysiwyg-box>
        </ng-container>
      </ng-container>

      <div *ngSwitchCase="ArticleBodyType.MediaVideo" class="block-video">
        <kesma-article-video [data]="element?.details[0]?.value"></kesma-article-video>
      </div>

      <div *ngSwitchCase="ArticleBodyType.Advert">
        <!--        <kesma-advertisement-adocean-->
        <!--          *ngIf="adverts?.desktop?.roadblock_1 as ad"-->
        <!--          [isExceptionAdvertEnabled]="isExceptionAdvertEnabled"-->
        <!--          [ad]="ad"-->
        <!--          [style]="{-->
        <!--            margin: 'var(&#45;&#45;ad-margin)',-->
        <!--          }"-->
        <!--        ></kesma-advertisement-adocean>-->
      </div>

      <ng-container *ngSwitchCase="ArticleBodyType.Voting">
        @if (voteCache[element?.details?.[0]?.value?.id ?? ''] | async; as voteData) {
          <app-voting [data]="voteData.data" [voteData]="voteData" [voteId]="voteData.votedId" />
        }
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.Gallery">
        <koponyeg-slider-gallery
          *ngIf="galleries[element?.details[0]?.value?.id] as gallery"
          [data]="gallery"
          [isInsideAdultArticleBody]="article?.isAdultsOnly"
        >
        </koponyeg-slider-gallery>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.Article">
        <koponyeg-article-card [data]="simpleArticleRecommendation(element?.details[0])" [styleID]="ArticleCardType.ImgTagTitleDateBeside">
        </koponyeg-article-card>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.DoubleArticleRecommendation">
        <div class="double-article-header">
          {{ getDoubleArticleRecommendationTitle(element?.details) }}
          <div class="double-article-lead">
            {{ getDoubleArticleRecommendationLead(element?.details) }}
          </div>
        </div>
        <div class="double-article">
          <ng-container *ngFor="let detail of doubleArticleRecommendations(element?.details); index as i">
            <koponyeg-article-card [data]="doubleArticleRecommendation(element?.details)[i]" [styleID]="ArticleCardType.ImgTagTitleDateBesideBelongsToHeader">
            </koponyeg-article-card>
          </ng-container>
        </div>
      </ng-container>
    </ng-container>
  </ng-container>
</ng-template>
