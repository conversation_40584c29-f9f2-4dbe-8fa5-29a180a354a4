import { Injectable } from '@angular/core';
import { generateSlug, IHttpOptions, ReqService } from '@trendency/kesma-core';
import {
  Advertisement,
  ApiResponseMetaList,
  ApiResult,
  ArticleCard,
  ArticleCategoryParams,
  BackendArticle,
  BackendArticleSearchResult,
  buildMenuItem,
  GalleriesResponse,
  InitResponse,
  Layout,
  MenuTreeResponse,
  PortalBasedMenuLinkOverride,
  PortfolioResponse,
  Region,
  SimplifiedMenuTree,
  VariableDidYouKnowBox,
} from '@trendency/kesma-ui';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import {
  AstronomyCity,
  BackendAllowedLoginMethodsResponse,
  BackendSocialLoginResponse,
  BackendUserLoginResponse,
  BackendWeatherDetection,
  LoginFormData,
  MedicalMeteorology,
  RegistrationFormData,
  TextForecast,
  UserProfileDetails,
} from '../definitions';
import { stationRegistrationFormDataToBackendRequest } from '../../feature/station-registration/station-registration.utils';
import { StationRegistrationFormData } from '../../feature/station-registration/station-registration.definitions';
import { requestPasswordResetDataToBackendRequest, resetPasswordDataToBackendRequest } from '../../feature/forgot-password/forgot-password.utils';
import { loginFormDataToBackendRequest, registrationFormDataToBackendRequest } from '../utils';

@Injectable({
  providedIn: 'root',
})
export class ApiService {
  constructor(private readonly reqService: ReqService) {}

  public init(): Observable<ApiResult<InitResponse>> {
    return this.reqService.get('/init');
  }

  public getMenu(overrides?: PortalBasedMenuLinkOverride): Observable<SimplifiedMenuTree> {
    return this.reqService.get<ApiResult<MenuTreeResponse>>('menu/tree').pipe(
      map(
        ({ data }) =>
          ({
            header: (data?.header ?? []).map((item) => buildMenuItem(item, '', overrides)),
            header_0: (data?.header_0 ?? []).map((item) => buildMenuItem(item, '', overrides)),
            header_1: (data?.header_1 ?? []).map((item) => buildMenuItem(item, '', overrides)),
            footer: (data?.footer ?? []).map((item) => buildMenuItem(item, '', overrides)),
            footer_0: (data?.footer_0 ?? []).map((item) => buildMenuItem(item, '', overrides)),
            footer_1: (data?.footer_1 ?? []).map((item) => buildMenuItem(item, '', overrides)),
          }) as SimplifiedMenuTree
      )
    );
  }

  public getAllCommercials(): Observable<ApiResult<Advertisement[], ApiResponseMetaList>> {
    return this.reqService.get('portal/commercials');
  }

  public getLayoutPreview(hash: string): Observable<ApiResult<Layout>> {
    return this.reqService.get(`layout/preview/view?previewHash=${hash}`);
  }

  public getCategoryLayout(categorySlug: string): Observable<ApiResult<Layout>> {
    return this.reqService.get<ApiResult<Layout>>(`column-layout/${categorySlug}`);
  }

  public getArticles(
    itemsPerPage = 4,
    isOrderedByPageViews = false,
    isHiddenOnLayout?: boolean,
    from_date?: string,
    to_date?: string
  ): Observable<ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>> {
    let params: Record<string, string | boolean> = {
      rowCount_limit: itemsPerPage.toString(),
    };
    params = isOrderedByPageViews ? { ...params, ...{ mode: 'top_viewed' } } : params;
    params = isHiddenOnLayout ? { ...params, isHiddenOnLayout } : params;
    params = from_date ? { ...params, from_date } : params;
    params = to_date ? { ...params, to_date } : params;
    return this.reqService.get<ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>>(`content-page/articles-by-any`, {
      params,
    });
  }

  public getCategoryArticles(
    categorySlug: string,
    page = 0,
    itemsPerPage = 10,
    year?: string,
    month?: string,
    excludedIds: string[] = []
  ): Observable<ApiResult<BackendArticle[], ApiResponseMetaList>> {
    const columnSlugify = categorySlug ? generateSlug(categorySlug) : '';
    let params: ArticleCategoryParams = {
      columnSlug: columnSlugify,
      rowCount_limit: itemsPerPage.toString(),
      page_limit: page.toString(),
      'excludedArticleIds[]': excludedIds ? excludedIds : [],
    };
    params = year ? { ...params, year } : params;
    params = month ? { ...params, month } : params;
    return this.reqService.get<ApiResult<BackendArticle[], ApiResponseMetaList>>(`content-page/articles-by-column`, { params: params }).pipe(
      map(({ data, meta }) => {
        return {
          meta,
          data: data.map((article) => {
            const [publishYear, publishMonth] = (article.publishDate as string).split('-');
            return {
              ...article,
              publishYear,
              publishMonth,
            };
          }),
        };
      })
    );
  }

  public getSidebarArticleRecommendations(
    count: number,
    columnSlug?: string,
    excludedIds: string[] = []
  ): Observable<ApiResult<ArticleCard[], ApiResponseMetaList>> {
    let { params }: IHttpOptions = {
      params: {
        rowCount_limit: count.toString(),
        'excludedArticleIds[]': excludedIds,
      },
    };
    params = columnSlug ? { ...params, columnSlug } : params;

    return this.reqService.get<ApiResult<ArticleCard[], ApiResponseMetaList>>(
      columnSlug ? '/content-page/articles-by-column?dev' : '/content-page/articles-by-last-day',
      { params }
    );
  }

  public getOpinionAuthor(authorSlug: string, page = 0, itemsPerPage = 12): Observable<ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>> {
    return this.reqService.get(`content-page/articles-by-opinion-type`, {
      params: {
        author: authorSlug,
        rowCount_limit: itemsPerPage.toString(),
        page_limit: page.toString(),
      },
    });
  }

  public getOrvosmetData(): Observable<ApiResult<MedicalMeteorology>> {
    return this.reqService.get(`forecast/met/orvosmet/view`);
  }

  public getArticlesByTag(slug: string): Observable<ApiResult<ArticleCard[], ApiResponseMetaList>> {
    return this.reqService.get(`content-page/articles-by-tag?tagSlug=${slug}`);
  }

  public getGalleries(page = 0, itemsPerPage = 21): Observable<ApiResult<GalleriesResponse[], ApiResponseMetaList>> {
    return this.reqService.get(`/media/galleries`, {
      params: {
        rowCount_limit: itemsPerPage.toString(),
        page_limit: page.toString(),
      },
    });
  }

  public getRegionPage(regionSlug: string, page = 0, itemsPerPage = 21): Observable<ApiResult<BackendArticle[], ApiResponseMetaList & { regionName: string }>> {
    return this.reqService.get(`/content-page/articles-by-region`, {
      params: { regionSlug: regionSlug, rowCount_limit: itemsPerPage?.toString(), page_limit: page?.toString() },
    });
  }

  public getRegions(
    page = 0,
    itemsPerPage = 50
  ): Observable<
    ApiResult<
      Region[],
      ApiResponseMetaList & {
        regionName: string;
      }
    >
  > {
    return this.reqService.get(`/source/content-group/regions`, {
      params: { rowCount_limit: itemsPerPage?.toString(), page_limit: page?.toString() },
    });
  }

  public searchByKeyword(
    searchQuery: string,
    page?: number,
    rowCount_limit?: number
  ): Observable<ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>> {
    return this.reqService.get(`/content-page/search`, {
      params: {
        global_filter: searchQuery,
        rowCount_limit: rowCount_limit?.toString(),
        page_limit: page?.toString(),
      },
    });
  }

  public getPortfolioFooter(): Observable<PortfolioResponse> {
    return this.reqService.get('portal/portfolio-footer');
  }

  register(formData: RegistrationFormData, recaptchaToken: string): Observable<void> {
    return this.reqService.post(`user/register`, registrationFormDataToBackendRequest(formData, recaptchaToken));
  }

  verifyRegister(id: string, hashedEmail: string, expiration: string, signature: string): Observable<void> {
    return this.reqService.get(`user/register/verify/${id}/${hashedEmail}`, { params: { expiration, signature } });
  }

  login(formData: LoginFormData, recaptchaToken: string): Observable<BackendUserLoginResponse> {
    return this.reqService.post(`portal_user/auth/login_check`, loginFormDataToBackendRequest(formData, recaptchaToken));
  }

  requestPasswordReset(email: string, recaptchaToken: string): Observable<void> {
    return this.reqService.post(`user/password/forget`, requestPasswordResetDataToBackendRequest(email, recaptchaToken));
  }

  resetPassword(email: string, password: string, resetPasswordToken: string): Observable<void> {
    return this.reqService.post(`user/password/reset`, resetPasswordDataToBackendRequest(email, password, resetPasswordToken));
  }

  getAllowedLoginMethods(): Observable<BackendAllowedLoginMethodsResponse> {
    return this.reqService.get(`user/auth/allowed-logins`);
  }

  loginWithFacebook(code: string, redirectUri: string): Observable<BackendSocialLoginResponse> {
    return this.reqService.post(`user/auth/login-facebook`, { code, redirectUri });
  }

  loginWithGoogle(code: string, redirectUri: string): Observable<BackendSocialLoginResponse> {
    return this.reqService.post(`user/auth/login-google`, { code, redirectUri });
  }

  getCities(searchTerm?: string | null, page = 0, itemsPerPage = 50): Observable<ApiResult<AstronomyCity[], ApiResponseMetaList>> {
    const filterParams = searchTerm ? { global_filter: searchTerm } : {};
    return this.reqService.get(`forecast/area/cities`, {
      params: { rowCount_limit: itemsPerPage?.toString(), page_limit: page?.toString(), ...filterParams },
    });
  }

  getUserProfileDetails(userId: string): Observable<ApiResult<UserProfileDetails>> {
    return this.reqService.get(`portal-users/portal-user/${userId}/details`);
  }

  getPredictionText(date: string): Observable<ApiResult<TextForecast>> {
    return this.reqService.get(`forecast/prediction-text/date/${date}`);
  }

  getUserPerceptions(userId: string): Observable<ApiResult<BackendWeatherDetection[], ApiResponseMetaList>> {
    return this.reqService.get(`forecast/perceptions/portal-user/${userId}`);
  }

  registerStation(data: StationRegistrationFormData, recaptchaToken: string): Observable<void> {
    return this.reqService.post(
      `forecast/station-registration/submit?recaptcha=${recaptchaToken}`,
      stationRegistrationFormDataToBackendRequest(data, recaptchaToken)
    );
  }

  getPerceptions(): Observable<ApiResult<BackendWeatherDetection[], ApiResponseMetaList>> {
    return this.reqService.get(`forecast/perceptions/all`);
  }

  getVariableSponsoredDidYouKnowBox(id: string): Observable<VariableDidYouKnowBox> {
    return this.reqService.get(`/content-group/did-you-know/${id}?getRandomData=1`);
  }

  // TODO: Feel free to use these endpoints

  /*  Lista (varos szuressel)
    forecast/perceptions/all?city_filter=l0r3m-1p5um


    A varosokat lekero url a tobbi listahoz hasonloan, ott lesz a lista response-ban.
    Csak kepes filter (varos mellett)
    forecast/perceptions/all?city_filter=l0r3m-1p5um&hasImage_filter

    Kep nelkuli filter (varos mellett) (ha megis kene)
    forecast/perceptions/all?city_filter=l0r3m-1p5um&emptyImage_filter
    */
}
