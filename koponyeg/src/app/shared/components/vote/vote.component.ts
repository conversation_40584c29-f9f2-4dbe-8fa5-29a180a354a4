import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, inject, input } from '@angular/core';
import { VoteDataWithAnswer, VoteService, VotingComponent as KesmaVotingComponent } from '@trendency/kesma-ui';
import { first } from 'rxjs';

@Component({
  selector: 'app-voting',
  imports: [NgIf, NgFor, AsyncPipe],
  templateUrl: './vote.component.html',
  styleUrl: './vote.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class VotingComponent extends KesmaVotingComponent {
  voteData = input<any>();

  private readonly voteService = inject(VoteService);
  private readonly changeDetector = inject(ChangeDetectorRef);

  override onVote(): void {
    if (this.vm.state.showResults) {
      return;
    }
    this.showResults = true;
    this.onVotingSubmit(this.voteId ?? '', this.voteData());
  }

  onVotingSubmit($event: string, voteData: VoteDataWithAnswer): void {
    this.voteService
      .onVotingSubmit($event, voteData)
      .pipe(first())
      .subscribe(() => this.changeDetector.detectChanges());
  }
}
