import { AppComponent } from './app/app.component';
import { bootstrapApplication } from '@angular/platform-browser';
import { appConfigWithSentry } from './app/app.config';
import { environment } from './environments/environment';
import * as Sentry from '@sentry/angular';
import { ErrorEvent, Exception, StackFrame } from '@sentry/angular';

bootstrapApplication(AppComponent, appConfigWithSentry).catch((err) => console.error(err));

const { sentry, type } = environment;

if (sentry && sentry.dsn) {
  Sentry.init({
    dsn: sentry.dsn,

    denyUrls: [/inmobi/, /googleapis/, /cdn/, /ads/, /adocean/, /ado/, /gemius/, /polyfills.*\.js/],

    ignoreErrors: [
      'pp_gemius_hit is not defined',
      'Script error.',
      'Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.',
    ],

    // Drop errors from non-first-party script files
    beforeSend(event: ErrorEvent): ErrorEvent | null {
      const exception: Exception | undefined = event?.exception?.values?.[0];
      const stackFrames: StackFrame[] = exception?.stacktrace?.frames || [];

      const isFromAllowedDomain = (filename: string): boolean => {
        return (sentry.tracingOrigins ?? []).some((domain: string) => filename.includes(domain));
      };

      const isAllowed = stackFrames.every((frame: StackFrame): boolean => {
        const filename: string = frame.filename || '';
        return !filename.startsWith('http') || isFromAllowedDomain(filename);
      });

      if (!isAllowed) {
        return null;
      }

      return event;
    },

    // Adds request headers and IP for users, for more info visit:
    // https://docs.sentry.io/platforms/javascript/guides/angular/configuration/options/#sendDefaultPii
    sendDefaultPii: true,

    integrations: [
      // Registers and configures the Tracing integration,
      // which automatically instruments your application to monitor its
      // performance, including custom Angular routing instrumentation
      Sentry.browserTracingIntegration(),
      // Registers the Replay integration,
      // which automatically captures Session Replays
      Sentry.replayIntegration(),
    ],

    // Set tracesSampleRate to 1.0 to capture 100%
    // of transactions for performance monitoring.
    // We recommend adjusting this value in production
    tracesSampleRate: sentry.tracesSampleRate,

    // Set `tracePropagationTargets` to control for which URLs trace propagation should be enabled
    tracePropagationTargets: sentry.tracingOrigins,

    // Capture Replay for 10% of all sessions,
    // plus for 100% of sessions with an error
    // Learn more at
    // https://docs.sentry.io/platforms/javascript/session-replay/configuration/#general-integration-configuration
    replaysSessionSampleRate: sentry.sampleRate,
    replaysOnErrorSampleRate: sentry.sampleRate,

    // Other
    sampleRate: sentry.sampleRate,
    profilesSampleRate: sentry.profilesSampleRate,
    environment: type,
  });
}
