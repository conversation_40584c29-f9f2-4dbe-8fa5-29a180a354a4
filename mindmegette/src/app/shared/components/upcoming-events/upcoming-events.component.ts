import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { RouterLink } from '@angular/router';
import { BaseComponent, KesmaSwipeComponent, SwipeBreakpoints } from '@trendency/kesma-ui';
import { GastroExperienceOccasionCard, GastroExperienceOccasionCardType } from '../../definitions';
import { GastroExperienceOccasionCardComponent } from '../gastro-experience-occasion-card/gastro-experience-occasion-card.component';
import { MindmegetteSimpleButtonComponent } from '../simple-button/simple-button.component';

@Component({
  selector: 'app-upcoming-events',
  imports: [RouterLink, GastroExperienceOccasionCardComponent, MindmegetteSimpleButtonComponent, KesmaSwipeComponent],
  templateUrl: './upcoming-events.component.html',
  styleUrl: './upcoming-events.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class UpcomingEventsComponent extends BaseComponent<GastroExperienceOccasionCard[]> {
  @Input() title = 'Közelgő élmények';

  GastroExperienceOccasionCardType = GastroExperienceOccasionCardType;
  breakpoints: SwipeBreakpoints = {
    default: {
      itemCount: 1.5,
      gap: '16px',
    },
    400: {
      itemCount: 2,
      gap: '16px',
    },
    560: {
      itemCount: 3,
      gap: '16px',
    },
  };
}
