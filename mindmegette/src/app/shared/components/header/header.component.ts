import { ChangeDetectionStrategy, Component, HostBinding, inject, Input, On<PERSON>estroy, OnInit } from '@angular/core';
import { buildArticleUrl, IconComponent, RelatedType, SimplifiedMenuItem, Tag, User } from '@trendency/kesma-ui';
import { BehaviorSubject, Subject } from 'rxjs';
import { LoginPopupService, ScreenWidthService } from '../../services';
import { buildBlankUrlFromArray, relatedArticleToArticleCard, relatedArticleToRecipeCard } from '../../utils';
import { UtilService } from '@trendency/kesma-core';
import { AsyncPipe, DOCUMENT, Ng<PERSON>lass, NgFor, NgIf, NgSwitch, NgSwitchCase, NgSwitchDefault, NgTemplateOutlet } from '@angular/common';
import { NavigationStart, Router, RouterLink } from '@angular/router';
import { filter, takeUntil } from 'rxjs/operators';
import { HeaderMenuComponent } from './components/header-menu/header-menu.component';
import { StickyProfileHeaderComponent } from './components/sticky-profile-header/sticky-profile-header.component';
import { HeaderProfileMenuComponent } from './components/header-profile-menu/header-profile-menu.component';
import { HeaderSearchBarComponent } from './components/header-search-bar/header-search-bar.component';
import { MindmegetteArticleCardType, RecipeCardType } from '../../definitions';
import { TagComponent } from '../tag/tag.component';
import { ArticleCardComponent } from '../article-card/article-card.component';
import { RecipeCardComponent } from '../recipe-card/recipe-card.component';
import { HeaderScrollableMenuComponent } from './components/header-scrollable-menu/header-scrollable-menu.component';

enum MobileMenuLevels {
  FirstLevel,
  SecondLevel,
  ThirdLevel,
}

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgClass,
    RouterLink,
    NgIf,
    HeaderSearchBarComponent,
    HeaderProfileMenuComponent,
    NgTemplateOutlet,
    StickyProfileHeaderComponent,
    HeaderMenuComponent,
    NgFor,
    NgSwitch,
    NgSwitchCase,
    IconComponent,
    NgSwitchDefault,
    AsyncPipe,
    TagComponent,
    ArticleCardComponent,
    RecipeCardComponent,
    HeaderScrollableMenuComponent,
  ],
})
export class HeaderComponent implements OnInit, OnDestroy {
  @Input() @HostBinding('class.has-breaking-news') hasBreakingNews = false;
  @Input() mainMenu: SimplifiedMenuItem[] = [];
  @Input() rightMenu: SimplifiedMenuItem[] = [];
  @Input() user: User | undefined;
  @Input() showStickyHeader = false;
  @Input() isHomePage = false;
  isMobileMenuOpen = false;
  currentLevel: MobileMenuLevels | undefined = MobileMenuLevels.FirstLevel;
  selectedMenuItem$ = new BehaviorSubject<SimplifiedMenuItem | null>(null);
  parentMenuItem: SimplifiedMenuItem | undefined;
  tags: Array<Tag> = [];
  fullArticles: Array<SimplifiedMenuItem> = [];
  isDesktopView = false;
  readonly MobileMenuLevels = MobileMenuLevels;
  readonly articleCardType = MindmegetteArticleCardType;
  readonly recipeCardType = RecipeCardType;
  readonly relatedTypes = RelatedType;
  readonly buildArticleUrl = buildArticleUrl;
  readonly buildBlankUrlFromArray = buildBlankUrlFromArray;
  readonly relatedArticleToArticleCard = relatedArticleToArticleCard;
  readonly relatedArticleToRecipeCard = relatedArticleToRecipeCard;
  private readonly document = inject(DOCUMENT);
  private readonly loginPopupService = inject(LoginPopupService);
  private readonly isDesktopWidth$ = inject(ScreenWidthService).isDesktopWidth$;
  private readonly utils = inject(UtilService);
  protected readonly router = inject(Router);
  private readonly unsubscribe$ = new Subject<void>();

  ngOnInit(): void {
    this.mapMenuItemsToTags();
    if (!this.utils.isBrowser()) {
      return;
    }
    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationStart),
        takeUntil(this.unsubscribe$)
      )
      .subscribe(() => this.selectedMenuItem$.next(null));
    this.isDesktopWidth$.pipe(takeUntil(this.unsubscribe$)).subscribe((res: boolean) => (this.isDesktopView = res));
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  redirectToLogin(): void {
    const redirect = this.router.routerState.snapshot.url;
    this.router
      .navigate(['/', 'bejelentkezes'], {
        queryParams: {
          redirect: redirect.startsWith('/bejelentkezes') || redirect === '/' ? null : redirect,
        },
      })
      .then();
  }

  mapMenuItemsToTags(): void {
    // TODO: ha image bekerül, bővíteni + slug logika
    this.tags = this.rightMenu.map((item) => {
      return {
        id: item.id,
        title: item.title,
        name: item.title,
      } as Tag;
    });
  }

  toggleMobileMenu(): void {
    this.fullArticles = [];
    this.isMobileMenuOpen = !this.isMobileMenuOpen;
    this.currentLevel = MobileMenuLevels.FirstLevel;
    if (!this.isMobileMenuOpen) {
      this.selectedMenuItem$.next(null);
    }
    this.updateBodyOverflow();
  }

  closeHamburgerMenu(): void {
    this.isMobileMenuOpen = false;
    this.fullArticles = [];
    this.currentLevel = this.MobileMenuLevels.FirstLevel;
    this.updateBodyOverflow();
  }

  updateBodyOverflow(): void {
    if (this.isMobileMenuOpen) {
      this.document.body.classList.add('no-scroll');
    } else {
      this.document.body.classList.remove('no-scroll');
    }
  }

  scrollToTop(): void {
    if (this.utils.isBrowser()) {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }

  activateItem(menuItem: SimplifiedMenuItem | null, nextLevel?: MobileMenuLevels, parentMenuItem?: SimplifiedMenuItem): void {
    this.fullArticles = [];
    if (nextLevel === MobileMenuLevels.ThirdLevel) {
      this.findFullArticles(menuItem!);
    }
    if (parentMenuItem) {
      this.parentMenuItem = parentMenuItem;
    }
    //Close dropdown if selected menu gets clicked again
    if (!menuItem) {
      this.selectedMenuItem$.next(null);
      this.currentLevel = nextLevel;
      return;
    }
    if (this.selectedMenuItem$.getValue()?.id === menuItem?.id) {
      this.selectedMenuItem$.next(null);
    } else {
      this.selectedMenuItem$.next(menuItem);
      this.currentLevel = nextLevel;
    }
  }

  handleMenuLinkClick(): void {
    this.selectedMenuItem$.next(null);
  }

  findFullArticles(menuItem: SimplifiedMenuItem | undefined): void {
    if (!menuItem) {
      this.fullArticles = [];
      return;
    }
    menuItem?.children?.forEach((thirdLevelMenuItem) => {
      thirdLevelMenuItem?.children?.forEach((item) => {
        if (item.relatedType === 'FullArticle' || item.relatedType === 'FullRecipe') {
          this.fullArticles.push(item);
        }
      });
    });
  }

  openAuthenticatedRoute(route: string): void {
    this.loginPopupService.openLoginPopupIfNeeded(route).pipe(takeUntil(this.unsubscribe$)).subscribe();
  }
}
