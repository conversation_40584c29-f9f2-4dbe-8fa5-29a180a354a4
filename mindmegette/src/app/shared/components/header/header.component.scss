@use 'shared' as *;

$white-border-with-opacity: rgba(255, 255, 255, 0.25);

:host {
  &.has-breaking-news {
    top: 32px;
  }
  .icon {
    &.andante-button {
      height: 12px;
      width: 20px;
      vertical-align: middle;
      @include icon('icons/Andante_Piskota_Mindmegette_button-white.svg');
      @include media-breakpoint-down(md) {
        @include icon('icons/Andante_Piskota_Mindmegette_button-green.svg');
      }
    }
  }

  .header-search-bar {
    max-width: unset !important;
  }

  h2.title {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    white-space: pre-wrap;
    font-size: 14px !important;
  }
}

.header {
  min-height: 88px;
  width: 100%;
  position: sticky;
  top: 0;
  z-index: 1000;
}

.bottom-header {
  background-color: var(--kui-green-700);
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  &-wrapper {
    padding: 0 28px 12px 28px;
    background-color: var(--kui-green-700);
    width: 100%;
    height: 100%;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    gap: 20px;
    @include media-breakpoint-down(md) {
      display: none;
    }
  }

  &-menu {
    width: 50%;

    &-list {
      display: flex;
      width: 100%;

      &-item {
        width: fit-content;
        margin: auto 20px auto 0;

        &-link {
          display: flex;
          justify-content: space-between;
          margin: auto 0;
          padding: 6px 0;
          color: var(--kui-white);
          text-align: center;
          font-family: var(--kui-font-secondary);
          font-size: 13px;
          font-style: normal;
          font-weight: 700;
          line-height: 20px;
          letter-spacing: 0.52px;
          text-transform: uppercase;
          cursor: pointer;
          width: max-content;

          &:hover {
            color: var(--kui-green-200);
          }

          &.active {
            color: var(--kui-green-400);
          }

          a {
            @extend .bottom-header-menu-list-item-link;
          }

          kesma-icon {
            transform: rotate(90deg);

            &.active {
              transform: rotate(-90deg);
            }
          }
        }
      }
    }

    @include media-breakpoint-down(md) {
      display: none;
    }
  }

  &-right-menu {
    width: 50%;
    display: flex;
    justify-content: flex-end;

    .bottom-header-right-menu-item {
      color: var(--kui-white);
      border: 1px solid $white-border-with-opacity;
      border-radius: 100px;
      padding: 10px 12px;
      text-align: center;
      font-family: var(--kui-font-secondary);
      font-size: 12px;
      font-weight: 500;
      line-height: 16px;
      letter-spacing: 0.12px;
      cursor: pointer;
      height: fit-content;
      white-space: nowrap;
      display: block;

      a {
        color: var(--kui-white);
      }
    }
  }
}

.mobile-menu {
  &-right-menu {
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 40px;
    gap: 8px;
    overflow-x: scroll;
    padding: 16px;
    flex-shrink: 0;

    .bottom-header-right-menu-item {
      a {
        color: var(--kui-green-700);
      }

      color: var(--kui-green-700);
      outline: 1px solid var(--kui-green-700);
      border-radius: 100px;
      padding: 10px 12px;
      white-space: nowrap;
      text-align: center;
      font-family: var(--kui-font-secondary);
      font-size: 12px;
      font-weight: 500;
      line-height: 16px;
      letter-spacing: 0.12px;
      cursor: pointer;
    }
  }
}

.top-header {
  background-color: var(--kui-green-700);
  width: 100%;
  height: 56px;
  display: flex;
  align-items: center;
  position: relative;

  @include media-breakpoint-up(md) {
    height: 60px;
  }

  &-wrapper {
    padding: 0 28px;
    background-color: var(--kui-green-700);
    width: 100%;
    height: 100%;
    margin: 8px auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;

    @include media-breakpoint-down(md) {
      padding: 0 16px;
    }

    .mindmegette-header-hamburger-icon {
      display: none;
      @include media-breakpoint-down(md) {
        display: block;
        width: 24px;
        height: 24px;
      }
    }

    .mindmegette-header-close-hamburger-icon {
      display: none;
      @include media-breakpoint-down(md) {
        display: block;
        width: 24px;
        height: 24px;
      }
    }

    &-icons {
      display: flex;
      align-items: center;
      gap: 8px;

      i {
        cursor: pointer;
      }
    }
  }

  &-logo {
    width: 124px;
    height: 40px;
    margin-right: 8px;
    cursor: pointer;

    &.experience-page {
      display: flex;
      gap: 24px;
      width: auto;
      align-items: center;

      @include media-breakpoint-down(md) {
        gap: 16px;
      }
    }

    h1 {
      line-height: unset;
      font-size: initial;
    }

    @include media-breakpoint-up(lg) {
      z-index: 1;
      width: 178px;
      height: 48px;
      margin-right: 0;
    }
  }

  &-search-bar {
    width: 40%;

    @include media-breakpoint-down(md) {
      display: none;
    }
  }

  &-actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 8px;

    @include media-breakpoint-up(md) {
      gap: 10px;
    }

    &-send-recipe {
      color: var(--kui-gray-950);
      background-color: var(--kui-white);
      padding: 8px 12px;
      font-family: var(--kui-font-secondary);
      font-size: 14px;
      font-weight: 500;
      line-height: 20px;
      border-radius: 100px;
      cursor: pointer;

      &:hover {
        background-color: var(--kui-green-50);
      }

      @include media-breakpoint-down(md) {
        display: none;
      }
    }

    &-icon {
      width: 32px;
      height: 32px;
      background-color: var(--kui-white);
      padding: 6px;
      border-radius: 100px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      @include media-breakpoint-up(md) {
        width: 36px;
        height: 36px;
      }

      i {
        width: 20px;
        height: 20px;
      }

      &:hover {
        background-color: var(--kui-green-50);
      }
    }
  }

  &-menu {
    display: flex;
    margin-left: 20px;
    align-content: center;

    &-list {
      display: flex;
      flex-wrap: nowrap;
      gap: 20px;

      &-item {
        margin: auto 0;
        padding: 20px 0;

        &-link {
          color: var(--kui-white);
          font-family: var(--kui-font-primary);
          font-size: 16px;
          font-weight: 700;
          cursor: pointer;

          &.active {
            img {
              rotate: 180deg;
            }
          }
        }
      }
    }

    &:hover &-list-item-link:not(:hover) {
      color: rgba(255, 255, 255, 0.7);

      img {
        opacity: 0.7;
      }
    }

    @include media-breakpoint-down(md) {
      display: none;
    }
  }

  &-right {
    position: relative;
    display: flex;
    align-content: center;
    align-self: center;
    align-items: center;
    gap: 20px;
    margin-left: auto;

    @include media-breakpoint-down(md) {
      gap: 12px;
    }

    &-button {
      font-family: var(--kui-font-primary);
      color: var(--kui-white);
      font-weight: 700;
      font-size: 16px;
      padding: 10px 10px;
      border-right: 1px solid var(--kui-white);

      &:hover {
        border-right: 1px solid var(--kui-pink-200);
        border-radius: 4px;
        background-color: var(--kui-pink-200);
      }

      @include media-breakpoint-down(md) {
        border: none;
        padding: 0;
      }

      .login-text {
        font-size: 16px;
        @include media-breakpoint-down(sm) {
          display: none;
        }
      }
    }

    &-radio {
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px;
      background-color: var(--kui-white);
      border-radius: 4px;

      img {
        height: 24px;
        width: 24px;
      }

      p {
        font-size: 16px;
        font-family: var(--kui-font-primary);
        color: var(--kui-red-400);
        font-weight: 700;

        @include media-breakpoint-down(md) {
          display: none;
        }
      }
    }

    &-icon {
      cursor: pointer;
      height: 100%;
      padding: 20px 0;

      img {
        width: 24px;
        height: 24px;
      }

      &.active {
        padding-bottom: 16px;
        border-bottom: 4px solid var(--kui-white);
      }

      &.hamburger-mobile {
        @include media-breakpoint-down(md) {
          display: block;
        }
        @include media-breakpoint-up(lg) {
          display: none;
        }
      }
    }
  }
}

.mobile-menu,
.mobile-menu-increased-padding {
  width: 100%;
  // 100vh - (The mobile height of the header + Height of the fixed button at the bottom of the menu)
  height: calc(100vh - (112px + 68px));
  z-index: 100;
  background-color: var(--kui-white);
  overflow-y: scroll;

  &-tags {
    padding: 16px;
  }

  &-send-recipe {
    padding: 16px;
    height: 68px;
    border-top: 1px solid var(--kui-gray-100);
    margin-top: auto;
    position: fixed;
    width: 100%;
    background-color: var(--kui-white);
    bottom: 0;

    &-button {
      color: var(--kui-white);
      background-color: var(--kui-green-700);
      display: flex;
      padding: 8px 12px;
      justify-content: center;
      align-items: center;
      gap: 4px;
      align-self: stretch;
      border-radius: 100px;
      font-family: var(--kui-font-secondary);
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: 20px;
      cursor: pointer;
    }
  }

  &-bottom-header {
    display: none;
    @include media-breakpoint-down(md) {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      padding: 0 16px 16px 16px;
    }

    &-search-bar {
      width: 100%;
    }
  }

  &-list {
    padding: 4px 8px;

    &-item {
      display: flex;
      flex-direction: column;
      gap: 4px;

      &-category {
        padding: 12px 8px;
        font-family: var(--kui-font-secondary);
        font-size: 14px;
        font-style: normal;
        font-weight: 700;
        line-height: 20px;
        color: var(--kui-gray-950);
      }

      &-sub-category {
        padding: 6px 8px;
        color: var(--kui-gray-950);
        font-feature-settings: 'salt' on;
        font-family: var(--kui-font-secondary);
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
      }

      &-link {
        width: 100%;
        padding: 10px 4px 10px 8px;
        display: flex;
        justify-content: space-between;
        font-family: var(--kui-font-secondary);
        color: var(--kui-gray-400);
        font-size: 14px;
        font-style: normal;
        font-weight: 700;
        line-height: 20px;
        letter-spacing: 0.7px;
        text-transform: uppercase;

        &:hover {
          color: var(--kui-green-700);
          background-color: var(--kui-green-50);
          cursor: pointer;
        }

        &.active {
          color: var(--kui-green-700);
          background-color: var(--kui-green-50);
          cursor: pointer;

          kesma-icon {
            rotate: 180deg;
          }
        }

        &-child-list {
          margin-top: 18px;
          display: flex;
          flex-direction: column;
          gap: 16px;

          &-item {
            display: flex;
            flex-direction: column;
            gap: 12px;
            width: 100%;

            &-link {
              font-size: 16px;
              color: var(--kui-gray-600);
            }
          }

          &-grand {
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin-top: 0;
          }
        }
      }

      &-link-highlighted {
        color: var(--kui-orange-600);

        &:hover {
          background-color: var(--kui-orange-50);
          color: var(--kui-orange-600);
          cursor: pointer;
        }
      }
    }
  }

  .justify-content-flex-start {
    justify-content: flex-start;
  }

  .mindmegette-header-highlighted-item {
    margin-top: 2px;
    height: 20px;
    width: 20px;
  }

  .title-wrapper {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .highlighted {
    color: var(--kui-orange-600);

    &.active {
      background-color: var(--kui-orange-50);
    }
  }
}

.mobile-menu-increased-padding {
  padding-bottom: 145px;
}

@supports (-webkit-tap-highlight-color: transparent) {
  @media screen and (orientation: portrait) and (hover: none) {
    .mobile-menu {
      padding-bottom: 85px;
    }
  }
  @media screen and (orientation: landscape) and (hover: none) {
    .mobile-menu {
      padding-bottom: 85px;
    }
  }
}

.mobile-menu-container {
  display: flex;
  white-space: nowrap;
  padding: 16px;
}

.mobile-menu-inner-container {
  display: flex;
  gap: 16px;
  overflow-x: auto;
}

.mobile-menu-article-card {
  display: flex;
  min-width: 180px;
  padding-bottom: 16px;

  ::ng-deep {
    mindmegette-recipe-card {
      margin-bottom: unset;
      max-width: 180px;

      .vertical-card {
        .thumbnail-wrapper {
          .mindmegette-icon-guarantee {
            width: 64px;
            height: 64px;
          }
        }

        .title {
          font-size: 14px;
          line-height: 20px;
          white-space: normal;
          text-overflow: ellipsis;
        }
      }
    }

    mindmegette-article-card,
    mindmegette-recipe-card.recipe {
      .thumbnail-wrapper .thumbnail {
        aspect-ratio: 1/1;
      }
    }

    mindmegette-article-card {
      max-width: 180px;

      .vertical-card {
        .content {
          padding-left: 0;
          border: none;

          .label-wrapper {
            max-height: 20px;

            &-label {
              text-overflow: ellipsis;
              overflow: hidden;
            }
          }
        }

        .title {
          font-size: 14px;
          line-height: 20px;
          white-space: normal;
          text-overflow: ellipsis;
        }
      }
    }
  }
}

.mindmegette-small-emblem {
  width: 20px;
  height: 20px;
}

.guaranteed-recipes-title {
  display: flex;
  flex-direction: row;
  color: var(--kui-gray-950);
  text-align: center;
  font-family: var(--kui-font-secondary);
  font-size: 18px;
  font-style: normal;
  font-weight: 700;
  line-height: 24px;
  letter-spacing: 0.18px;
  padding: 10px 0 0 16px;
  gap: 6px;
}
