import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { MindmegetteArticleCardType } from '../../definitions';
import { ArticleCard, BaseComponent, BlockTitle } from '@trendency/kesma-ui';
import { ArticleCardComponent } from '../article-card/article-card.component';
import { NgFor, NgIf } from '@angular/common';

@Component({
  selector: 'mindmegette-offer-list',
  templateUrl: './offer-list.component.html',
  styleUrls: ['./offer-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgFor, ArticleCardComponent],
})
export class OfferListComponent extends BaseComponent<ArticleCard[]> {
  blockTitle = input<BlockTitle>();
  readonly ArticleCardType = MindmegetteArticleCardType;
}
