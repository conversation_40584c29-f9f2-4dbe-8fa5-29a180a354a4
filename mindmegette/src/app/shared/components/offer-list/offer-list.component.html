@if (blockTitle()?.url) {
  <h2 [style.color]="blockTitle()?.overrideColor || 'var(--kui-white)'" class="block-title">{{ blockTitle()?.url }}</h2>
} @else if (blockTitle()) {
  <h2 class="block-title">{{ blockTitle() }}</h2>
}
<div class="article-wrapper" *ngIf="data">
  <ng-container *ngFor="let article of data; trackBy: trackByFn">
    <mindmegette-article-card
      [data]="article"
      [styleID]="ArticleCardType.LeftImageShortCard"
      [hasBackground]="false"
      [isInOfferBox]="true"
      [isPrBlock]="true"
      [overrideColumnTitle]="article?.columnTitle"
    ></mindmegette-article-card>
  </ng-container>
</div>
