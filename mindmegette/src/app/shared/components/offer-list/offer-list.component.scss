@use 'shared' as *;

:host {
  display: block;
  width: 100%;
  background-color: var(--kui-pink-500);
  border-radius: 6px;
  padding: 16px;

  .block-title {
    font-size: 24px;
    line-height: 28px;
    font-weight: 600;
    margin-bottom: 16px;
    letter-spacing: -0.12px;
    color: var(--kui-white);
  }

  ::ng-deep {
    .article-wrapper {
      .content {
        display: flex;
        flex-direction: column;
        justify-content: center;

        .title {
          word-break: break-word;
        }
      }
    }

    .style-LeftImageShortCard {
      margin-bottom: 0;

      .title {
        min-height: 40px;
      }

      .content {
        padding: 0 0 0 16px !important;
      }
    }
  }

  mindmegette-article-card.style-LeftImageShortCard::ng-deep {
    .content {
      gap: 4px;
    }

    .pr-label-wrapper {
      margin-bottom: 0;

      &-label {
        letter-spacing: 0.48px;
      }
    }

    .title {
      -webkit-line-clamp: 3;
    }
  }

  .article-wrapper {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
}
