<ng-template #itemTemplate let-menu="data">
  <mindmegette-ingredient-card [data]="menu"> </mindmegette-ingredient-card>
</ng-template>
<ng-template #previousNavigation><kesma-icon name="mindmegette-icon-green-left-arrow" /></ng-template>
<ng-template #nextNavigation><kesma-icon name="mindmegette-icon-green-right-arrow" /></ng-template>
<div
  kesma-swipe
  [itemTemplate]="itemTemplate"
  [previousNavigationTemplate]="previousNavigation"
  [nextNavigationTemplate]="nextNavigation"
  [useNavigation]="true"
  [usePagination]="true"
  [data]="data"
  [breakpoints]="breakpoints"
  [class.ingredient-mobile]="styleID === IngredientListStyle.MOBILE"
  [class.ingredient-desktop]="styleID === IngredientListStyle.DESKTOPSMALL || styleID === IngredientListStyle.DESKTOPLARGE"
  [class.align-slides-center]="(desktopWidth > 3 && (data?.length || 0) < 5) || (data?.length || 0) < 5"
></div>
