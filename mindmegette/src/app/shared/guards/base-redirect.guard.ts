import { inject } from '@angular/core';
import { CanActivateFn } from '@angular/router';
import { SeoService } from '@trendency/kesma-core';
import { map } from 'rxjs/operators';
import { ArticleService, RedirectService } from '../services';

/**
 * This may be necessary if you just need to call redirect api without any hacking.
 */
export const BaseRedirectGuard: CanActivateFn = () => {
  const redirectService = inject(RedirectService);
  const currentUrl = inject(SeoService).currentUrl;

  return inject(ArticleService)
    .getArticleRedirect(currentUrl)
    .pipe(
      map(({ url }) => {
        if (url) {
          redirectService.redirectOldUrl(url, true);
          return false;
        }
        return true;
      })
    );
};
