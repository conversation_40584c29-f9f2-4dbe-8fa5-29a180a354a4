import { Injectable } from '@angular/core';
import { IHttpOptions, ReqService } from '@trendency/kesma-core';
import {
  Advertisement,
  ApiListResult,
  ApiResponseMeta,
  ApiResponseMetaList,
  ApiResult,
  ArticleCard,
  ArticleCategoryParams,
  ArticleSocial,
  BackendArticle,
  BackendArticleSearchResult,
  BackendComment,
  BackendHighlightedSelection,
  BasicDossier,
  buildMenuItem,
  CalendarDay,
  Column,
  CommentListResponse,
  CommentRequestParams,
  DossierArticle,
  GalleriesResponse,
  HighlightedSelection,
  InitResponse,
  Layout,
  mapBackendHighlightedSelectionToHighlightedSelection,
  mapToCommentListResponse,
  MenuTreeResponse,
  Ordering,
  PortalBasedMenuLinkOverride,
  PortfolioResponse,
  Region,
  SecretDaysCalendar,
  SimplifiedMenuTree,
  Sponsorship,
  VariableDidYouKnowBox,
} from '@trendency/kesma-ui';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import {
  ArticleSearchQueryParams,
  AuthorData,
  AuthorSearchQueryParams,
  BackendAllowedLoginMethodsResponse,
  BackendArticleSocial,
  BackendAuthorData,
  BackendIngredientSearchResponse,
  BackendRecipeCategorySearchResponse,
  BackendRecipeSearchResponse,
  BackendSocialLoginResponse,
  BackendUserLoginResponse,
  ComplaintHandlingFormData,
  DailyMenu,
  LoginFormData,
  RegistrationFormData,
  UserProfileDetails,
} from '../definitions';
import { Params } from '@angular/router';
import { BestPractise } from '../../feature/best-practices/best-practices.definitions';
import { requestPasswordResetDataToBackendRequest, resetPasswordDataToBackendRequest } from '../../feature/forgot-password/forgot-password.utils';
import { Recipe, TopListAutoWord } from '../../feature/recipe-page/recipe-page.definitions';
import {
  backendArticleSocialToSocial,
  complaintHandlingFormDataToBackendRequest,
  loginFormDataToBackendRequest,
  registrationFormDataToBackendRequest,
} from '../utils';

@Injectable({
  providedIn: 'root',
})
export class ApiService {
  constructor(private readonly reqService: ReqService) {}

  public init(): Observable<ApiResult<InitResponse>> {
    return this.reqService.get('/init');
  }

  public getMenu(overrides?: PortalBasedMenuLinkOverride): Observable<SimplifiedMenuTree> {
    return this.reqService.get<ApiResult<MenuTreeResponse>>('menu/tree').pipe(
      map(
        ({ data }) =>
          ({
            header: (data?.header ?? []).map((item) => buildMenuItem(item, '', overrides, true)),
            header_0: (data?.header_0 ?? []).map((item) => buildMenuItem(item, '', overrides, true)),
            header_1: (data?.header_1 ?? []).map((item) => buildMenuItem(item, '', overrides, true)),
            header_gastro: (data?.header_gastro ?? []).map((item) => buildMenuItem(item, '', overrides, true)),
            footer: (data?.footer ?? []).map((item) => buildMenuItem(item, '', overrides, true)),
            footer_0: (data?.footer_0 ?? []).map((item) => buildMenuItem(item, '', overrides, true)),
            footer_1: (data?.footer_1 ?? []).map((item) => buildMenuItem(item, '', overrides, true)),
            footer_gastro: (data?.footer_gastro ?? []).map((item) => buildMenuItem(item, '', overrides, true)),
          }) as SimplifiedMenuTree
      )
    );
  }

  public getAllCommercials(): Observable<ApiResult<Advertisement[], ApiResponseMetaList>> {
    return this.reqService.get('portal/commercials');
  }

  public getLayoutPreview(hash: string): Observable<ApiResult<Layout>> {
    return this.reqService.get(`layout/preview/view?previewHash=${hash}`);
  }

  public getCategoryLayout(categorySlug: string): Observable<ApiResult<Layout>> {
    return this.reqService.get<ApiResult<Layout>>(`column-layout/${categorySlug}`);
  }

  public getBestPractices(queryParams: Params): Observable<ApiListResult<BestPractise>> {
    return this.reqService.get<ApiListResult<BestPractise>>(`bestpractices/list`, {
      params: queryParams,
    });
  }

  public getArticles(
    itemsPerPage = 1,
    isOrderedByPageViews = false,
    from_date?: string,
    to_date?: string
  ): Observable<ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>> {
    let params: Record<string, string> = {
      rowCount_limit: itemsPerPage.toString(),
    };
    params = isOrderedByPageViews ? { ...params, ...{ mode: 'top_viewed' } } : params;
    params = from_date ? { ...params, from_date } : params;
    params = to_date ? { ...params, to_date } : params;

    // We need to encode the params to the URL, because the core request service uses transfer state based on the url,
    // ignoring the other params.
    const appendedParams = Object.keys(params)
      .map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&');

    return this.reqService.get<ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>>(
      `content-page/articles-by-any${appendedParams ? `?${appendedParams}` : ''}`
    );
  }

  public getCategoryArticles(
    categorySlug: string,
    page = 0,
    itemsPerPage = 10,
    year?: string,
    month?: string,
    excludedIds: string[] = [],
    publishDateOrder?: string
  ): Observable<ApiResult<BackendArticle[], ApiResponseMetaList>> {
    let params: ArticleCategoryParams & { 'publishDate_order[]'?: string } = {
      columnSlug: categorySlug,
      rowCount_limit: itemsPerPage.toString(),
      page_limit: page.toString(),
      'excludedArticleIds[]': excludedIds ? excludedIds : [],
    };
    params = year ? { ...params, year } : params;
    params = month ? { ...params, month } : params;
    params = publishDateOrder ? { ...params, 'publishDate_order[]': publishDateOrder } : params;
    return this.reqService.get<ApiResult<BackendArticle[], ApiResponseMetaList>>(`content-page/articles-by-column`, { params: params }).pipe(
      map(({ data, meta }) => {
        return {
          meta,
          data: data.map((article) => {
            const [publishYear, publishMonth] = (article.publishDate as string).split('-');
            return {
              ...article,
              publishYear,
              publishMonth,
            };
          }),
        };
      })
    );
  }

  public getSidebarArticleRecommendations(
    count: number,
    columnSlug?: string,
    excludedIds: string[] = []
  ): Observable<ApiResult<ArticleCard[], ApiResponseMetaList>> {
    let { params }: IHttpOptions = {
      params: {
        rowCount_limit: count.toString(),
        'excludedArticleIds[]': excludedIds,
      },
    };
    params = columnSlug ? { ...params, columnSlug } : params;

    return this.reqService.get<ApiResult<ArticleCard[], ApiResponseMetaList>>(
      columnSlug ? '/content-page/articles-by-column?dev' : '/content-page/articles-by-last-day',
      { params }
    );
  }

  public getOpinionAuthor(authorSlug: string, page = 0, itemsPerPage = 12): Observable<ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>> {
    return this.reqService.get(`content-page/articles-by-opinion-type`, {
      params: {
        author: authorSlug,
        rowCount_limit: itemsPerPage.toString(),
        page_limit: page.toString(),
      },
    });
  }

  public getArticlesByTag(slug: string): Observable<ApiResult<ArticleCard[], ApiResponseMetaList>> {
    return this.reqService.get(`content-page/articles-by-tag?tagSlug=${slug}`);
  }

  public getGalleries(page = 0, itemsPerPage = 21): Observable<ApiResult<GalleriesResponse[], ApiResponseMetaList>> {
    return this.reqService.get(`/media/galleries`, {
      params: {
        rowCount_limit: itemsPerPage.toString(),
        page_limit: page.toString(),
      },
    });
  }

  public getDossier(dossierSlug: string, page = 0, itemsPerPage = 21): Observable<ApiResult<DossierArticle[], ApiResponseMetaList & Partial<BasicDossier>>> {
    return this.reqService.get(`/content-group/dossiers/${dossierSlug}`, {
      params: { rowCount_limit: itemsPerPage?.toString(), page_limit: page?.toString() },
    });
  }

  public getRegionPage(regionSlug: string, page = 0, itemsPerPage = 21): Observable<ApiResult<BackendArticle[], ApiResponseMetaList & { regionName: string }>> {
    return this.reqService.get(`/content-page/articles-by-region`, {
      params: { regionSlug: regionSlug, rowCount_limit: itemsPerPage?.toString(), page_limit: page?.toString() },
    });
  }

  public getRegions(
    page = 0,
    itemsPerPage = 50
  ): Observable<
    ApiResult<
      Region[],
      ApiResponseMetaList & {
        regionName: string;
      }
    >
  > {
    return this.reqService.get(`/source/content-group/regions`, {
      params: { rowCount_limit: itemsPerPage?.toString(), page_limit: page?.toString() },
    });
  }

  /**
   * Creates string form a given query params object. This is used to create a query string that already contains
   * the params in the url. This is need for the requestService, as it makes a key only from the provided url, ignoring
   * the other params.
   * @param params
   * @private
   */
  private paramsToString(params: Record<string, string | string[] | undefined | null>): string {
    const paramsPrepared: Record<string, string>[] = [];
    if (params) {
      Object.keys(params).map((key) => {
        const value = params[key];
        if (value === undefined || value === null) {
          return;
        }
        if (Array.isArray(value)) {
          value.map((item) => {
            // Add [] to end of array params, but only if it is not already there.
            const itemKey = key.endsWith(']') ? key : `${key}[]`;
            paramsPrepared.push({ [itemKey]: encodeURIComponent(item) });
          });
          return;
        }
        paramsPrepared.push({ [key]: encodeURIComponent(value) });
      });
    }
    const paramsString = paramsPrepared
      .map((item) =>
        Object.keys(item)
          .map((key) => `${key}=${item[key]}`)
          .join('&')
      )
      .join('&');
    return paramsString;
  }

  /**
   * Creates a query string from the given query params and extra params. This is used to create a query string that
   * already contains the params in the url. This is need for the requestService, as it makes a key only from the provided url, ignoring
   * the other params.
   * @param queryParams
   * @param extraParams
   * @private
   */
  private searchKeywordParamsToString(
    queryParams: Record<string, string | string[] | undefined | null>,
    extraParams?: Record<string, string | string[]>
  ): string {
    const queryParamsString = queryParams ? this.paramsToString(queryParams) : '';
    const extraParamsString = extraParams ? this.paramsToString(extraParams) : '';

    return queryParamsString?.length && extraParamsString?.length ? `${queryParamsString}&${extraParamsString}` : queryParamsString || extraParamsString;
  }

  public searchByKeyword(
    searchQuery: string,
    page?: number,
    rowCount_limit?: number,
    extraParams?: Record<string, string | string[]>
  ): Observable<ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>> {
    const queryParams: Record<string, string> = {
      global_filter: searchQuery,
      rowCount_limit: rowCount_limit?.toString() || '',
      page_limit: page?.toString() || '',
    };
    const paramsAsString = this.searchKeywordParamsToString(queryParams, extraParams);

    return this.reqService.get(`/content-page/search${paramsAsString ? `?${paramsAsString}` : ''}`);
  }

  public getPortfolioFooter(): Observable<PortfolioResponse> {
    return this.reqService.get('portal/portfolio-footer');
  }

  getAuthors(page = 0, perPage = 10, isInner: boolean = true): Observable<ApiListResult<BackendAuthorData>> {
    const params = {
      rowCount_limit: perPage.toString(),
      page_limit: page.toString(),
      is_active: '1',
      is_inner: isInner ? '1' : '0',
    };

    return this.reqService.get('user/authors', {
      params,
    });
  }

  getSingleAuthorBySlug(params: AuthorSearchQueryParams): Observable<ApiResult<BackendAuthorData>> {
    return this.reqService.get('user/author_social', {
      params,
    });
  }

  getSingleAuthorByName(params: AuthorSearchQueryParams): Observable<ApiResult<BackendAuthorData>> {
    return this.reqService
      .get<ApiListResult<BackendAuthorData>>('user/authors', {
        params,
      })
      .pipe(
        map((res) => ({
          data: res.data[0],
          meta: {} as ApiResponseMeta,
        }))
      );
  }

  searchArticles(params: ArticleSearchQueryParams): Observable<ApiResult<BackendRecipeSearchResponse[], ApiResponseMetaList>> {
    params = {
      page_limit: '0',
      rowCount_limit: '10',
      ...params,
    };
    const paramsAsString = this.searchKeywordParamsToString(params);
    return this.reqService.get(`/content-page/search${paramsAsString?.length ? `?${paramsAsString}` : ''}`);
  }

  register(formData: RegistrationFormData, recaptchaToken: string): Observable<void> {
    return this.reqService.post(`user/register`, registrationFormDataToBackendRequest(formData, recaptchaToken));
  }

  verifyRegister(id: string, hashedEmail: string, expiration: string, signature: string): Observable<void> {
    return this.reqService.get(`user/register/verify/${id}/${hashedEmail}`, { params: { expiration, signature } });
  }

  login(formData: LoginFormData, recaptchaToken: string): Observable<BackendUserLoginResponse> {
    return this.reqService.post(`portal_user/auth/login_check`, loginFormDataToBackendRequest(formData, recaptchaToken));
  }

  getAllowedLoginMethods(): Observable<BackendAllowedLoginMethodsResponse> {
    return this.reqService.get(`user/auth/allowed-logins`);
  }

  loginWithFacebook(code: string, redirectUri: string): Observable<BackendSocialLoginResponse> {
    return this.reqService.post(`user/auth/login-facebook`, { code, redirectUri });
  }

  loginWithGoogle(code: string, redirectUri: string): Observable<BackendSocialLoginResponse> {
    return this.reqService.post(`user/auth/login-google`, { code, redirectUri });
  }

  requestPasswordReset(email: string, recaptchaToken: string): Observable<void> {
    return this.reqService.post(`user/password/forget`, requestPasswordResetDataToBackendRequest(email, recaptchaToken));
  }

  resetPassword(email: string, password: string, resetPasswordToken: string): Observable<void> {
    return this.reqService.post(`user/password/reset`, resetPasswordDataToBackendRequest(email, password, resetPasswordToken));
  }

  getUserProfileDetails(userId: string): Observable<ApiResult<UserProfileDetails>> {
    return this.reqService.get(`portal-users/portal-user/${userId}/details`);
  }

  getUserUploadedRecipes(userId: string, params: Params): Observable<ApiListResult<Recipe>> {
    return this.reqService.get<ApiListResult<Recipe>>(`recipes/user-uploaded/${userId}`, { params });
  }

  getIngredients(itemsPerPage = 10, titleFilter?: string): Observable<ApiResult<BackendIngredientSearchResponse[], ApiResponseMetaList>> {
    let params: Record<string, string> = {
      rowCount_limit: itemsPerPage.toString(),
    };
    params = titleFilter ? { ...params, title_filter: titleFilter } : params;
    return this.reqService.get(`ingredients`, { params });
  }

  getRecipesByIngredients(
    itemsPerPage = 10,
    ingredientSlug?: string,
    extraOptions?: Record<string, string>
  ): Observable<ApiResult<BackendRecipeSearchResponse[], ApiResponseMetaList>> {
    let params: Record<string, string | string[]> = {
      rowCount_limit: itemsPerPage.toString(),
      'content_types[]': ['recipe'],
    };
    params = ingredientSlug ? { ...params, ingredient: ingredientSlug } : params;
    params = extraOptions ? { ...params, ...extraOptions } : params;
    const paramsAsString = this.searchKeywordParamsToString(params, extraOptions);
    return this.reqService.get(`/content-page/search${paramsAsString?.length ? `?${paramsAsString}` : ''}`);
  }

  getRecipeCategories(
    itemsPerPage = 10,
    isRootFilter?: number,
    titleFilter?: string
  ): Observable<ApiResult<BackendRecipeCategorySearchResponse[], ApiResponseMetaList>> {
    let params: Record<string, string> = {
      rowCount_limit: itemsPerPage.toString(),
    };
    params = titleFilter ? { ...params, title_filter: titleFilter } : params;
    params = isRootFilter ? { ...params, isRoot_filter: isRootFilter.toString() } : params;

    return this.reqService.get(`recipe/categories`, { params });
  }

  getRecipes(itemsPerPage = 10, titleFilter?: string): Observable<ApiResult<BackendRecipeSearchResponse[], ApiResponseMetaList>> {
    let params: Record<string, string> = {
      rowCount_limit: itemsPerPage.toString(),
    };
    params = titleFilter ? { ...params, title_filter: titleFilter } : params;
    return this.reqService.get(`recipe/categories`, { params });
  }

  getAllRecipes(
    itemsPerPage = 10,
    filter?: string,
    extraOptions?: Record<string, string | string[]>
  ): Observable<ApiResult<BackendRecipeSearchResponse[], ApiResponseMetaList>> {
    let params: Record<string, string | string[]> = {
      rowCount_limit: itemsPerPage.toString(),
      'content_types[]': ['recipe'],
    };
    params = filter ? { ...params, global_filter: filter } : params;
    params = extraOptions ? { ...params, ...extraOptions } : params;
    const paramsAsString = this.searchKeywordParamsToString(params, extraOptions);
    return this.reqService.get(`/content-page/search${paramsAsString?.length ? `?${paramsAsString}` : ''}`);
  }

  getRecipes$(itemsPerPage?: number): Observable<Recipe[]> {
    let params: Record<string, string | undefined> = {};
    params = itemsPerPage ? { ...params, rowCount_limit: itemsPerPage.toString() } : params;
    return this.reqService.get<ApiResult<Recipe[]>>(`recipes`, { params }).pipe(
      map(({ data }) =>
        data?.map((recipe) => ({
          ...recipe,
          thumbnail: {
            url: recipe?.coverImage,
            alt: '',
          },
        }))
      )
    );
  }

  getCommentListFor(
    articleId: string,
    sort: Ordering = 'latest',
    params: Partial<CommentRequestParams> = {},
    type: 'article' | 'recipe' | 'comment' = 'article'
  ): Observable<CommentListResponse> {
    const sorting = {
      'most-popular': { 'likeCount_order[0]': 'desc', 'createdAt_order[1]': 'desc' },
      latest: { 'createdAt_order[0]': 'desc' },
      oldest: { 'createdAt_order[0]': 'asc' },
    }[sort];
    return this.reqService
      .get<ApiResult<BackendComment[], ApiResponseMetaList>>(`comments/${type}/${articleId}/comments`, {
        params: { ...params, ...sorting },
      })
      .pipe(mapToCommentListResponse());
  }

  getAnswersForComment(
    commentId: string,
    sort: Ordering = 'latest',
    params?: { page_limit?: number; rowCount_limit?: number }
  ): Observable<CommentListResponse> {
    const sorting = {
      'most-popular': { 'likeCount_order[0]': 'desc', 'createdAt_order[1]': 'desc' },
      latest: { 'createdAt_order[0]': 'desc' },
      oldest: { 'createdAt_order[0]': 'asc' },
    }[sort];
    return this.reqService
      .get<ApiResult<BackendComment[], ApiResponseMetaList>>(`comments/comment/${commentId}/answers`, { params: { ...params, ...sorting } })
      .pipe(mapToCommentListResponse());
  }

  getHighlightedSelectionData(selectionSlug: string): Observable<ApiResult<HighlightedSelection>> {
    return this.reqService.get<ApiResult<BackendHighlightedSelection>>(`content-group/selection/${selectionSlug}`).pipe(
      map((response) => ({
        ...response,
        data: mapBackendHighlightedSelectionToHighlightedSelection(response.data),
      }))
    );
  }

  getDailyMenu(date: string): Observable<DailyMenu> {
    return this.reqService.get(`/weekly-menu/item-by-date/${date}`);
  }

  getColumns(options?: IHttpOptions): Observable<ApiResult<Column[], ApiResponseMetaList>> {
    return this.reqService.get('/source/content-group/columns', options);
  }

  getHighlightedSponsorhip(): Observable<ApiResult<Sponsorship>> {
    return this.reqService.get('/content-group/sponsorship/is-highlighted').pipe(catchError<any, Observable<Sponsorship[]>>(() => of([] as Sponsorship[])));
  }

  getSocialDataByType(id: string, type: 'article' | 'recipe'): Observable<ArticleSocial> {
    const request$ =
      type === 'article'
        ? this.reqService.get<ApiResult<BackendArticleSocial>>(`/content-page/${type}/${id}/social-count`)
        : this.reqService.get<ApiResult<BackendArticleSocial>>(`${type}/${id}/social-count`);

    return request$.pipe(
      map((result: ApiResult<BackendArticleSocial>) => {
        return {
          ...backendArticleSocialToSocial(result.data),
        };
      })
    );
  }

  getTopList(): Observable<ApiResult<TopListAutoWord[]>> {
    return this.reqService.get(`top-ranking-glossary/layout/auto-words-list?rowCount_limit=50`);
  }

  getPublicAuthorSocial(global_filter?: string, options?: IHttpOptions): Observable<ApiResult<AuthorData, ApiResponseMetaList>> {
    let params = { ...options?.params };
    params = global_filter ? { ...params, global_filter } : params;
    return this.reqService.get(`user/author_social`, { params });
  }

  getSecretDaysCalendar(id: string): Observable<ApiResult<SecretDaysCalendar, ApiResponseMetaList>> {
    return this.reqService.get(`secret-days-calendar/${id}`);
  }

  getSecretDaysCalendarDay(id: string): Observable<ApiResult<CalendarDay, ApiResponseMetaList>> {
    return this.reqService.get(`secret-days-calendar-day/${id}`);
  }

  getVariableSponsoredDidYouKnowBox(id: string): Observable<VariableDidYouKnowBox> {
    return this.reqService.get(`/content-group/did-you-know/${id}?getRandomData=1`);
  }

  sendComplain(formData: ComplaintHandlingFormData, recaptchaToken: string): Observable<any> {
    return this.reqService.post(`digital-services-act-form`, complaintHandlingFormDataToBackendRequest(formData, recaptchaToken));
  }
}
