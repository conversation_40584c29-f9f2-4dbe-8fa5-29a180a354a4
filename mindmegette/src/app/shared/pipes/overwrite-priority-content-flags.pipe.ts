import { Pipe, PipeTransform } from '@angular/core';
import { PriorityContentFlags } from '@trendency/kesma-ui';

const DESKTOP_LIMIT = 7;
const MOBILE_LIMIT = 7;

@Pipe({
  name: 'overwritePriorityContentFlags',
})
export class OverwritePriorityContentFlagsPipe implements PipeTransform {
  transform<T extends PriorityContentFlags>(value: T, index: number, desktopLimit: number = DESKTOP_LIMIT, mobileLimit: number = MOBILE_LIMIT): T {
    if (index < desktopLimit) {
      value.priorityContentDesktop = true;
    }
    if (index < mobileLimit) {
      value.priorityContentMobile = true;
    }
    return value;
  }
}
