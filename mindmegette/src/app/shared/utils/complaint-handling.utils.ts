import { BackendComplaintHandlingRequest, ComplaintHandlingFormData } from '../definitions/copmlaint-handlind.definitions';

export function complaintHandlingFormDataToBackendRequest(formData: ComplaintHandlingFormData, recaptchaToken: string): BackendComplaintHandlingRequest {
  return {
    url: formData.url,
    complaint: formData.complaint,
    isCrime: formData.isCrime,
    consent: formData.consentCheckbox,
    name: formData.name,
    email: formData.email,
    recaptcha: recaptchaToken,
  };
}
