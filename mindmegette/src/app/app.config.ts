import { environment } from '../environments/environment';
import { DateFnsConfigurationService } from 'ngx-date-fns';
import { hu } from 'date-fns/locale';
import {
  ApplicationConfig,
  Error<PERSON><PERSON><PERSON>,
  importProvidersFrom,
  inject,
  mergeApplicationConfig,
  provideAppInitializer,
  provideZoneChangeDetection,
} from '@angular/core';
import { AppEnvironment, provideEncodedTransferState } from '@trendency/kesma-core';
import { provideAnimations } from '@angular/platform-browser/animations';
import { appRoutes } from './app.routing';
import { provideRouter, Router, withEnabledBlockingInitialNavigation, withInMemoryScrolling } from '@angular/router';
import { provideHttpClient, withInterceptors, withInterceptorsFromDi } from '@angular/common/http';
import { GoogleTagManagerModule } from 'angular-google-tag-manager';
import { adDebugFactory, DEV_AD_DEBUG } from '@trendency/kesma-ui';
import { authInterceptor, portalHeaderHttpInterceptor } from './shared';
import { provideClientHydration } from '@angular/platform-browser';
import { provideLoadingBarInterceptor } from '@ngx-loading-bar/http-client';
import * as Sentry from '@sentry/angular';
import { TraceService } from '@sentry/angular';

//const ROUTE_TRACING_ENABLED = false;

const GTAG_PROVIDER = [{ provide: 'googleTagManagerId', useValue: environment.googleTagManager }];
const huConfig = new DateFnsConfigurationService();
huConfig.setLocale(hu);

export const appConfig: ApplicationConfig = {
  providers: [
    provideEncodedTransferState(),
    provideAnimations(),
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(appRoutes, withEnabledBlockingInitialNavigation(), withInMemoryScrolling({ scrollPositionRestoration: 'top', anchorScrolling: 'enabled' })),
    provideHttpClient(withInterceptorsFromDi(), withInterceptors([portalHeaderHttpInterceptor, authInterceptor])),
    provideLoadingBarInterceptor(),
    importProvidersFrom(GoogleTagManagerModule),
    provideClientHydration(),
    {
      provide: AppEnvironment,
      useValue: environment,
    },
    {
      provide: DateFnsConfigurationService,
      useValue: huConfig,
    },
    {
      provide: DEV_AD_DEBUG,
      useFactory: adDebugFactory,
      deps: [AppEnvironment],
    },
    ...GTAG_PROVIDER,
  ],
};

// Make sure that the Sentry providers are not accidentally passed to the SSR config (app.config.server.ts).
// The Sentry Angular SDK can only be used in the browser.
export const sentryConfig: ApplicationConfig = {
  providers: [
    {
      provide: ErrorHandler,
      useValue: Sentry.createErrorHandler(),
    },
    {
      provide: Sentry.TraceService,
      deps: [Router],
    },
    provideAppInitializer(() => {
      inject(TraceService);
    }),
  ],
};

export const appConfigWithSentry = mergeApplicationConfig(appConfig, sentryConfig);
