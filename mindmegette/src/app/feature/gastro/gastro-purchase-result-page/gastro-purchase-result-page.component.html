@if (isLoading()) {
  <section class="content">
    <mindmegette-spinner></mindmegette-spinner>
    <p class="content-text">A fizetés folyamatban, kér<PERSON><PERSON>k ne zárd be az oldalt...</p>
  </section>
} @else {
  <h1 class="title" [class.error]="isError()">
    @if (status(); as status) {
      {{ simplePayStatusMessage[status] }}
    } @else {
      A fizetési folyamat megszakadt
    }
    @if (!isError()) {
      <kesma-icon class="title-icon" name="icon-circle-checkmark" />
    }
  </h1>

  <section class="content">
    <p class="content-text">
      @if (isError()) {
        A vásárlásod sikertelen fizetési tranzakcióval zárult. Nem történt meg a bankkártyád terhelése, ezért kér<PERSON>nk, hogy próbálkozz újra a vásárlással.
      } @else {
        <PERSON><PERSON>szönjük, hogy a Mindmegette Élmények szolgáltatást választottad. E-mailben elküldtük az eseményre történő belépéshez szükséges információkat.
      }
    </p>

    <div class="purchase-result-details">
      @if (responseCode(); as responseCode) {
        <div class="purchase-result-detail">
          <div class="purchase-result-detail-name">Válaszkód:</div>
          <div class="purchase-result-detail-value">{{ responseCode }}</div>
        </div>
      }
      @if (transactionId(); as transactionId) {
        <div class="purchase-result-detail">
          <div class="purchase-result-detail-name">Tranzakcióazonosító:</div>
          <div class="purchase-result-detail-value">{{ transactionId }}</div>
        </div>
      }
      @if (status(); as status) {
        <div class="purchase-result-detail">
          <div class="purchase-result-detail-name">Státusz:</div>
          <div class="purchase-result-detail-value" [ngClass]="{ error: isError() }">{{ status }}</div>
        </div>
      }
    </div>

    @if (responseCode(); as responseCode) {
      @if (simplePayResponseCodeMessage[responseCode]; as message) {
        <div class="purchase-result-simple-message">
          {{ message }}
        </div>
      }
    }

    <div class="action">
      @if (isError()) {
        <mindmegette-simple-button
          (click)="retry()"
          color="primary"
          icon="mindmegette-icon-arrow-right-no-color"
          iconPosition="right"
          [useKesmaIcon]="true"
          [kesmaIconSize]="24"
          >Adatok ellenőrzése
        </mindmegette-simple-button>
      } @else {
        <mindmegette-simple-button
          [routerLink]="['/', 'elmenyek', 'esemenyek']"
          color="primary"
          icon="mindmegette-icon-arrow-right-no-color"
          iconPosition="right"
          [useKesmaIcon]="true"
          [kesmaIconSize]="24"
          >Tovább az élményekhez
        </mindmegette-simple-button>
      }
    </div>
  </section>
  <section class="content events">
    <app-upcoming-events [data]="occasionList()"></app-upcoming-events>
  </section>
}
