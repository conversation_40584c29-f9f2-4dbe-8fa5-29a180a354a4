<h1 class="notification-title">Értesítést kérek a(z) {{ experience()?.title }} eseményre!</h1>

<form class="notification-form" [formGroup]="formGroup()" (ngSubmit)="subscribeToNotifications()">
  <div class="row mindmegette-form-row">
    <div class="col-12 col-md-6">
      <kesma-form-control>
        <label class="mindmegette-form-label" for="lastName">Vezetéknév<strong>*</strong></label>
        <input class="mindmegette-form-input" formControlName="lastName" id="lastName" type="text" />
      </kesma-form-control>
    </div>
    <div class="col-12 col-md-6">
      <kesma-form-control>
        <label class="mindmegette-form-label" for="firstName">Keresztnév<strong>*</strong></label>
        <input class="mindmegette-form-input" formControlName="firstName" id="firstName" type="text" />
      </kesma-form-control>
    </div>
  </div>

  <div class="mindmegette-form-row">
    <kesma-form-control>
      <label class="mindmegette-form-label" for="email">E-mail cím<strong>*</strong></label>
      <input class="mindmegette-form-input" formControlName="email" id="email" type="text" />
    </kesma-form-control>
  </div>

  <div class="registration-checkboxes">
    <kesma-form-control class="checkbox">
      <label class="mindmegette-form-checkbox" for="terms">
        <input formControlName="terms" id="terms" type="checkbox" />
        <span
          >Megismertem és elfogadom a Mindmegette.hu weboldal
          <a class="mindmegette-form-checkbox-link" href="/felhasznalasi-feltetelek" target="_blank">felhasználási feltételeit</a>. A honlapra történő
          regisztrációval elfogadom a Mediaworks Hungary Zrt. (1082 Budapest, Üllői út 82.) Mindmegette.hu weboldallal kapcsolatos adatkezelésre vonatkozó
          <a class="mindmegette-form-checkbox-link" href="/adatvedelmi-tajekoztato" target="_blank">adatvédelmi tájékoztatóját</a> és hozzájárulok ahhoz, hogy
          az általam közölt adatokat a regisztrációval összefüggő célokból és az általam használt szolgáltatások működtetése érdekében a Mediaworks Hungary Zrt.
          kezelje.
        </span>
      </label>
    </kesma-form-control>
    <kesma-form-control class="checkbox">
      <label class="mindmegette-form-checkbox" for="marketing">
        <input formControlName="marketing" id="marketing" type="checkbox" />
        <span>
          Érdekelnek a Mediaworks Hungary Zrt. és más harmadik felek különleges ajánlatai és reklámjai (ideértve például a recept és más tartalmi ajánlókat),
          ezért szeretnék direkt marketing - közvetlen üzletszerzési célú - hírleveleket kapni e-mailen. Hozzájárulok a megadott személyes adataim kezeléséhez
          az adatkezelési tájékoztatóban foglaltak szerint (a személyes adatokat csak a Mediaworks Hungary Zrt. fogja kezelni). Adatvédelmi tájékoztató:
          <a class="mindmegette-form-checkbox-link" href="/adatvedelmi-tajekoztato" target="_blank">link</a>
        </span>
      </label>
    </kesma-form-control>
  </div>

  <div *ngIf="error()" class="mindmegette-form-general-error">
    {{ error() }}
  </div>

  <mindmegette-simple-button [disabled]="isLoading()" [isSubmit]="true" class="w-100" color="primary">
    {{ isLoading() ? 'Kérjük várj...' : 'Feliratkozom' }}
  </mindmegette-simple-button>
</form>

@if (isSubmitted()) {
  <mindmegette-popup (resultEvent)="onPopupResult()" acceptButtonLabel="Vissza az eseményekhez" [showCancelButton]="false" title="Sikeres feliratkozás!">
  </mindmegette-popup>
}
