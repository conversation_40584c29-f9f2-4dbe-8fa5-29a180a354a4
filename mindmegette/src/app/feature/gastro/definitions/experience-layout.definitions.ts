import { LayoutElementContentConfiguration, ThumbnailImage } from '@trendency/kesma-ui';
import { BackendExperienceOccasion } from './occasion-definitions';

export interface LayoutElementContentConfigurationGastroBaseSelectedOccasion {
  id?: string;
  data?: BackendExperienceOccasion;
  [key: string]: any;
}

export interface LayoutElementContentConfigurationGastroBase extends LayoutElementContentConfiguration {
  autoFill: {
    newest?: boolean;
    filterExperience?: {
      id: string;
    };
    filterExperienceCategories?: ExperienceCategory | ExperienceCategory[];
  };
  selectedOccasions?: LayoutElementContentConfigurationGastroBaseSelectedOccasion[];
  remainingNumberOfSeatsOrder?: boolean;
}
export interface LayoutElementContentConfigurationGastroOccasionRecommender extends LayoutElementContentConfigurationGastroBase {
  backgroundImage?: ThumbnailImage;
  buttonText?: string;
  buttonUrl?: string;
  description?: string;
}

export interface ExperienceCategory {
  id: string;
  name: string;
}

export interface LayoutElementContentConfigurationGastroThematicRecommender extends LayoutElementContentConfigurationGastroBase {
  blockTitle?: string;
  buttonUrl?: string;
}
