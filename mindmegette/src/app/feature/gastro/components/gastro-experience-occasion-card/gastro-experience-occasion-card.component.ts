import { ChangeDetectionStrategy, Component, HostBinding, Input, OnInit } from '@angular/core';
import { BackendExperienceOccasionListItem, ExperienceOccasionCard } from '../../definitions/experience.definitions';
import { backendDateToDate, toBool } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';
import { addHours } from 'date-fns';
import { NgIf, NgTemplateOutlet } from '@angular/common';
import { FormatDatePipe } from '@trendency/kesma-core';

@Component({
  selector: 'app-gastro-experience-occasion-card',
  imports: [RouterLink, NgTemplateOutlet, NgIf, FormatDatePipe],
  templateUrl: './gastro-experience-occasion-card.component.html',
  styleUrl: './gastro-experience-occasion-card.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GastroExperienceOccasionCardComponent implements OnInit {
  @HostBinding('attr.style') hostStyle: string;
  @HostBinding('class') hostClass: string = '';
  readonly ExperienceOccasionCard = ExperienceOccasionCard;

  @Input({ required: true }) occasion: BackendExperienceOccasionListItem;
  @Input() showDate = false;
  @Input() isOccasionListPage = false;
  @Input() set styleID(style: ExperienceOccasionCard) {
    this._styleID = style;
    this.hostClass = ExperienceOccasionCard[style];
  }

  private _styleID: ExperienceOccasionCard | undefined = undefined;

  get styleID(): ExperienceOccasionCard | undefined {
    return this._styleID;
  }

  endDate: Date | null = null;

  ngOnInit(): void {
    this.hostStyle = `
      --occasion-status-color: var(
        ${this.isComingSoon ? `--kui-orange-600` : this.hasRemainingNumberOfSeats ? `--kui-green-700` : `--kui-red-500`}
      );
    `;

    if (this.isOccasionListPage) {
      this.hostClass = 'occasion-list-page';
    }
    if (this.occasion.occasionStartOfExperienceEvent) {
      this.endDate = backendDateToDate(this.occasion.occasionStartOfExperienceEvent);
      if (this.endDate && this.occasion.occasionDuration) {
        this.endDate = addHours(this.endDate, Number(this.occasion.occasionDuration));
      }
    }
  }

  protected get hasRemainingNumberOfSeats(): boolean {
    return !!Number(this.occasion.remainingNumberOfSeats);
  }

  protected get isComingSoon(): boolean {
    return toBool(this.occasion.occasionIsComingSoon);
  }
}
