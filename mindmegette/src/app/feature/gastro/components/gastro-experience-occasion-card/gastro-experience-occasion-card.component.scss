@use 'shared' as *;

:host {
  display: block;
  padding: 16px 24px;
  // The --occasion-status-color variable is set in the OnInit function of the component.
  border: 1px solid var(--occasion-status-color);
  border-radius: 12px;
  font-family: var(--kui-font-secondary);
  color: var(--kui-gray-950);
  container-type: inline-size;

  .occasion-card {
    display: flex;
    align-items: center;
    gap: 16px;

    @include container-breakpoint-down(xs) {
      flex-direction: column;
      align-items: flex-start;
    }

    &-places {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      width: 68px;
      flex-shrink: 0;

      @include container-breakpoint-down(xs) {
        flex-direction: row;
        margin-bottom: -10px;
        width: 100%;
      }

      &-count {
        background-color: var(--occasion-status-color);
        color: var(--kui-white);
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 6px;
        font-family: var(--kui-font-primary);
        font-size: 24px;
        font-weight: 600;
        line-height: 28px;

        @include container-breakpoint-down(xs) {
          width: 32px;
          height: 32px;
          font-size: 20px;
          line-height: 26px;
          font-weight: 700;
        }
      }

      &-text {
        font-size: 12px;
        text-align: center;
        line-height: 16px;
        font-weight: normal;
        font-family: var(--kui-font-secondary);

        &.coming-soon {
          display: none;
        }

        @include container-breakpoint-down(xs) {
          text-transform: lowercase;

          &.coming-soon {
            display: block;
            text-align: start;
            text-transform: capitalize;
            margin-bottom: -10px;
          }
        }
      }
    }

    &-divider {
      width: 1px;
      height: 88px;
      background-color: var(--kui-gray-200);

      @include container-breakpoint-down(xs) {
        display: none;
      }
    }

    &-thumbnail {
      aspect-ratio: 1/1;
      width: 88px;
      flex-shrink: 0;
      object-fit: cover;
      border-radius: 8px;

      @include container-breakpoint-down(xs) {
        display: none;
      }
    }

    &-info {
      display: flex;
      flex-direction: column;
      gap: 2px;
      width: 100%;
    }

    &-inner {
      display: flex;
      flex-direction: column;
    }

    &-title {
      font-size: 24px;
      font-weight: 600;
      line-height: 28px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      text-overflow: ellipsis;
      overflow: hidden;
      color: var(--kui-gray-950);

      @include container-breakpoint-down(xs) {
        font-size: 20px;
        line-height: 26px;
        font-weight: 700;
      }
    }

    &-author,
    h4 {
      font-family: var(--kui-font-secondary);
      font-size: 14px;
      line-height: 20px;
      color: var(--kui-gray-600);
      font-weight: 400;

      @include container-breakpoint-down(xs) {
        font-size: 12px;
        line-height: 16px;
      }
    }

    &-action {
      width: 137px;
      flex-shrink: 0;
      padding-block: 10px;
      border-radius: 8px;
      font-family: var(--kui-font-secondary);
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      text-align: center;

      &.outline {
        border: 1px solid var(--kui-gray-950);
        color: var(--kui-gray-950);
      }

      &.primary {
        background-color: var(--kui-green-700);
        color: var(--kui-white);
        transition: background-color 0.5s ease;

        &:hover {
          background-color: var(--kui-green-900);
        }
      }

      @include container-breakpoint-down(xs) {
        width: 100%;
        font-size: 14px;
        line-height: 20px;
        border-radius: 6px;
      }
    }
  }

  &.AuthorExperienceOccasion {
    padding: 24px;

    @include container-breakpoint-down(xs) {
      padding: 16px;
    }
    .occasion-card {
      gap: 32px;

      @include container-breakpoint-down(xs) {
        gap: 16px;
      }

      &-info {
        gap: 8px;

        @include container-breakpoint-down(xs) {
          a {
            display: flex;
            gap: 16px;
            align-items: center;
          }
        }
      }

      &-thumbnail {
        width: 180px;
        aspect-ratio: unset;
        height: 134px;

        &.mobile {
          display: none;
          @include container-breakpoint-down(xs) {
            display: block;
            height: 64px;
            width: 64px;
          }
        }
      }

      &-title {
        letter-spacing: 0.12px;

        @include container-breakpoint-down(xs) {
          font-family: var(--kui-font-secondary);
          font-size: 18px;
          font-weight: 700;
          line-height: 24px;
        }
      }

      &-date,
      &-place {
        font-family: var(--kui-font-secondary);
        font-size: 16px;
        font-weight: 400;
        line-height: 22px;
        letter-spacing: 0.16px;
        text-overflow: ellipsis;

        @include container-breakpoint-down(xs) {
          font-size: 14px;
          line-height: 20px;
          letter-spacing: unset;
        }
      }

      &-author-box {
        display: flex;
        gap: 8px;
        align-items: center;
        justify-content: flex-start;

        img {
          height: 24px;
          width: 24px;
          object-fit: cover;
          border-radius: 50%;
        }

        h4 {
          font-size: 12px;
          line-height: 16px;
          letter-spacing: 0.12px;

          display: flex;
          align-items: center;
          gap: 4px;
        }

        .icon {
          height: 16px;
          width: 16px;
        }
      }

      &-places {
        gap: 24px;

        @include container-breakpoint-down(xs) {
          gap: 8px;
          margin-bottom: unset;
        }

        &-count {
          width: 44px;
          height: 44px;
          letter-spacing: 0.12px;

          @include container-breakpoint-down(xs) {
            width: 32px;
            height: 32px;
            font-family: var(--kui-font-secondary);
            letter-spacing: unset;
          }
        }

        h4 {
          font-size: 12px;
          line-height: 16px;
          letter-spacing: 0.12px;
          color: var(--kui-gray-950);
        }
      }

      &-divider {
        height: 134px;

        @include container-breakpoint-down(xs) {
          display: block;
          height: 1px;
          width: 100%;
        }
      }
    }
  }

  &.occasion-list-page {
    .occasion-card {
      &-title {
        font-size: 24px;
        font-weight: 600;
        line-height: 28px;
        letter-spacing: 0.12px;

        @include media-breakpoint-down(sm) {
          font-size: 20px;
          font-weight: 700;
          line-height: 26px;
          letter-spacing: normal;
        }
      }

      &-author,
      h4 {
        font-size: 14px;
        line-height: 20px;
        @include media-breakpoint-down(sm) {
          font-size: 12px;
          line-height: 16px;
          letter-spacing: 0.12px;
        }
      }
    }
  }
}
