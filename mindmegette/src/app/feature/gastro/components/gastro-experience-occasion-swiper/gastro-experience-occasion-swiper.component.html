<div class="occasion-swiper">
  <ng-template #itemTemplate let-occasion="data">
    <mindmegette-gastro-experience-occasion-card
      [data]="occasion"
      [styleId]="GastroExperienceOccasionCardType.Centered"
    ></mindmegette-gastro-experience-occasion-card>
  </ng-template>
  <ng-template #previousNavigation><kesma-icon name="mindmegette-icon-green-left-arrow" /></ng-template>
  <ng-template #nextNavigation><kesma-icon name="mindmegette-icon-green-right-arrow" /></ng-template>
  <div
    kesma-swipe
    [itemTemplate]="itemTemplate"
    [previousNavigationTemplate]="previousNavigation"
    [nextNavigationTemplate]="nextNavigation"
    [useNavigation]="true"
    [usePagination]="true"
    [data]="data"
    [breakpoints]="{
      default: {
        itemCount: 1,
        gap: '20px',
      },
    }"
  ></div>
</div>
