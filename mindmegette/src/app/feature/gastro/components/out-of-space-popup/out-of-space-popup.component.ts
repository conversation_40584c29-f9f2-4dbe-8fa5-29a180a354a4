import { ChangeDetectionStrategy, Component, input, output } from '@angular/core';
import { MindmegettePopupComponent, MindmegetteSimpleButtonComponent } from '../../../../shared';

@Component({
  selector: 'app-out-of-space-popup',
  templateUrl: './out-of-space-popup.component.html',
  styleUrl: './out-of-space-popup.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [MindmegettePopupComponent, MindmegetteSimpleButtonComponent],
})
export class OutOfSpacePopupComponent {
  readonly hasSpaceForAnotherOccasion = input.required<boolean>();

  readonly anotherDateClicked = output<void>();
  readonly eventsListClicked = output<void>();
  readonly closeClicked = output<void>();
  readonly notifyMeClicked = output<void>();
}
