<div class="wrapper">
  <h1 class="page-title">
    Jo<PERSON><PERSON>s tartalmi probléma <br />
    jelentése - Mindmegette
  </h1>
  <div class="form-wrapper">
    <form (ngSubmit)="save()" *ngIf="formGroup" [formGroup]="formGroup">
      <kesma-form-control>
        <label class="mindmegette-form-label" for="url">Jogellenesnek vélt tartalom pontos linkje</label>
        <input class="mindmegette-form-input" formControlName="url" id="url" type="text" placeholder="Link helye" />
      </kesma-form-control>
      <kesma-form-control>
        <label class="mindmegette-form-label" for="complaint">Kérjük itt részletezze, hogy miért jogellenes Ön szerint a tartalom</label>
        <input class="mindmegette-form-input" formControlName="complaint" id="complaint" type="text" placeholder="Leírás" />
      </kesma-form-control>
      <kesma-form-control class="checkbox">
        <label class="mindmegette-form-label" for="isCrime">
          <input formControlName="isCrime" id="isCrime" type="checkbox" />
          <span>
            Kijelentem, hogy a bejelentésem a 2011/93/ EU irányelv cikkében megjelölt bűncselekményre
            <span
              class="tooltip"
              data-tooltip="Gyermekek szexuális bántalmazásával, kizsákmányolásával, 
              gyermekpornográfiával kapcsolatos bűncselekmények,  gyermekkel való, 
              szexuális céllal történő kapcsolatfelvétel, illetve ezekre való felbujtás, 
              ezeknél nyújtott bűnsegély vagy ezen bűncselekmények megkísérlése."
              ><i class="icon mindmegette-icon-info"></i
            ></span>
            vonatkozik.
          </span>
        </label>
      </kesma-form-control>
      <kesma-form-control>
        <label class="mindmegette-form-label" for="name">Név</label>
        <input class="mindmegette-form-input" formControlName="name" id="name" type="text" placeholder="Név" />
      </kesma-form-control>
      <kesma-form-control>
        <label class="mindmegette-form-label" for="email">E-mail cím</label>
        <input class="mindmegette-form-input" formControlName="email" id="email" type="text" placeholder="E-mail cím" />
      </kesma-form-control>
      <kesma-form-control class="checkbox">
        <label class="mindmegette-form-label" for="consentCheckbox">
          <input formControlName="consentCheckbox" id="consentCheckbox" type="checkbox" />
          <span>* Kijelentem, hogy jóhiszeműen járok el, és bejelentésemben szereplő információk és állítások pontosak és hiánytalanok. </span>
        </label>
      </kesma-form-control>
      <br />
      <div>
        A Küldés gomb megnyomásával elfogadom a bejelentésben megadott személyes adataim
        <a [routerLink]="['/', 'adatvedelmi-tajekoztato']">Adatvédelmi tájékoztatóban</a>
        szereplő feltételek szerinti kezelését.
      </div>
      <div class="submit">
        <mindmegette-simple-button
          [isSubmit]="true"
          class="w-100"
          color="primary"
          icon="edit-white"
          iconPosition="left"
          [disabled]="!formGroup.valid || isSaving()"
        >
          Elküldés
        </mindmegette-simple-button>
        @if (isError()) {
          <div class="error">Hiba a panaszbeküldés során!</div>
        }
        <a class="back" [routerLink]="['/']"> <kesma-icon name="header-arrow-right" [size]="20"></kesma-icon> vissza </a>
      </div>
    </form>
  </div>
</div>
