@use 'shared' as *;

:host {
  margin: 30px auto;
  display: block;
  padding: 10px;
  width: calc(100% - 200px);
  @include media-breakpoint-down(md) {
    width: calc(100% - 8px);
  }
  .page-title {
    color: var(--kui-gray-950);
    text-transform: uppercase;
    font-size: 40px;
    font-weight: 600;
    line-height: 48px;
    text-align: center;
    letter-spacing: -0.4px;
    padding-bottom: 10px;
  }
  .page-description {
    p {
      font-family: var(--kui-font-primary);
      color: var(--kui-black);
      font-size: 18px;
      line-height: 26px;
      padding-bottom: 20px;
    }
  }
  .submit {
    width: 50%;
    margin: 20px auto;
    @include media-breakpoint-down(md) {
      width: 70%;
    }
  }
  .tooltip {
    color: blue;
  }
  .icon {
    width: 14px;
    height: 14px;
  }
  [data-tooltip]:hover::after {
    display: block;
    position: absolute;
    content: attr(data-tooltip);
    border: 1px solid black;
    background: #eee;
    padding: 0.25em;
    z-index: 1000;
    border-radius: 8px;
    font-size: 12px;
    color: var(--kui-black);
    line-height: 16px;
    font-family: var(--kui-font-primary);
  }
  .back {
    kesma-icon {
      transform: rotate(-180deg);
      display: inline-block;
    }
  }

  .error {
    color: var(--kui-pink-600);
    font-size: 11px;
    line-height: 14px;
    letter-spacing: 0.11px;
  }
}
