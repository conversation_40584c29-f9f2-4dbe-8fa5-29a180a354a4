<section *ngIf="pageData$ | async as pageData" class="category">
  <app-breadcrumb [data]="[{ label: pageData.pageData?.columnTitle ?? '' }]" />
  <div class="wrapper with-aside">
    <div class="left-column">
      <ng-container *ngIf="pageData?.pageData?.layoutApiResponse as layoutData">
        <app-layout [adPageType]="pageData.adPageType" [configuration]="layoutData.content" [layoutType]="LayoutPageType.COLUMN" [structure]="pageData.struct">
        </app-layout>
      </ng-container>

      <mindmegette-block-title
        [data]="{ text: pageData?.pageData?.columnTitle ?? '' }"
        [headingLevel]="1"
        [styleID]="MindmegetteBlockTitleType.PageTitle"
      ></mindmegette-block-title>

      <div class="results">
        <div class="results-label">
          Összes találat <sup>({{ limitable.rowAllCount }} db)</sup>
        </div>
        <div class="sorter">
          <app-search-result-sorter
            (changeValue)="onChangeSort($event)"
            [hideSelects]="['contentType']"
            [searchBackendValue]="publishDateOrder"
            searchBackendKey="publishDate_order[]"
          >
          </app-search-result-sorter>
        </div>
      </div>

      <div class="article-card-wrapper">
        <mindmegette-article-card
          *ngFor="let article of articles | slice: 0 : 6; index as i"
          [data]="article | overwritePriorityContentFlags: i"
          [hasBackground]="true"
          [styleID]="ArticleCardType.TopImageLeftAlignedCard"
        >
        </mindmegette-article-card>
      </div>

      <!-- AD -->
      <ng-container *ngIf="articles?.length && articles.length >= 4">
        <kesma-advertisement-adocean *ngIf="showDesktopAd && adverts?.desktop?.['roadblock_1'] as ad" [ad]="ad" [style]="{ margin: 'var(--ad-margin)' }">
        </kesma-advertisement-adocean>
        <kesma-advertisement-adocean *ngIf="!showDesktopAd && box_1_tablet as ad" [ad]="ad" [style]="{ margin: 'var(--ad-margin)' }">
        </kesma-advertisement-adocean>

        <kesma-advertisement-adocean
          *ngIf="adverts?.mobile?.['mobilrectangle_1'] as ad"
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
          }"
        ></kesma-advertisement-adocean>
      </ng-container>
      <!-- END AD -->

      <div *ngIf="articles?.length && articles.length > 6" class="article-card-wrapper">
        <mindmegette-article-card
          *ngFor="let article of articles | slice: 6 : 12; index as i"
          [data]="article | overwritePriorityContentFlags: i + 6"
          [hasBackground]="true"
          [styleID]="ArticleCardType.TopImageLeftAlignedCard"
        >
        </mindmegette-article-card>
      </div>

      <!-- AD -->
      <ng-container *ngIf="articles?.length && articles.length >= 10">
        <kesma-advertisement-adocean *ngIf="!showDesktopAd && box_2_tablet as ad" [ad]="ad" [style]="{ margin: 'var(--ad-margin)' }">
        </kesma-advertisement-adocean>
        <kesma-advertisement-adocean *ngIf="showDesktopAd && adverts?.desktop?.['roadblock_2'] as ad" [ad]="ad" [style]="{ margin: 'var(--ad-margin)' }">
        </kesma-advertisement-adocean>
        <kesma-advertisement-adocean
          *ngIf="adverts?.mobile?.['mobilrectangle_2'] as ad"
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
          }"
        ></kesma-advertisement-adocean>
      </ng-container>
      <!-- END AD -->

      <div *ngIf="articles?.length && articles.length > 12" class="article-card-wrapper">
        <mindmegette-article-card
          *ngFor="let article of articles | slice: 12 : 18"
          [data]="article"
          [hasBackground]="true"
          [styleID]="ArticleCardType.TopImageLeftAlignedCard"
        >
        </mindmegette-article-card>
      </div>

      <mindmegette-pager
        *ngIf="limitable && limitable.pageMax! > 0"
        [allowAutoScrollToTop]="true"
        [hasFirstLastButton]="false"
        [hasSkipButton]="true"
        [isCountPager]="false"
        [isListPager]="true"
        [maxDisplayedPages]="5"
        [rowAllCount]="limitable.rowAllCount!"
        [rowOnPageCount]="limitable.rowOnPageCount!"
      ></mindmegette-pager>

      <app-external-recommendations [externalRecommendations]="recommendations"></app-external-recommendations>
    </div>
    <aside>
      <app-sidebar [adPageType]="adPageType" [categorySlug]="columnSlug"></app-sidebar>
    </aside>
  </div>
</section>
