import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, SlicePipe } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { isDesktopAdShared } from '@shared/utils/advert.utils';
import { IMetaData, SeoService, UtilService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  ALL_BANNER_LIST,
  AnalyticsService,
  ArticleCard,
  createCanonicalUrlForPageablePage,
  LayoutElementRow,
  LayoutPageType,
  LimitableMeta,
  mapBackendArticleDataToArticleCard,
  PAGE_TYPES,
  SecondaryFilterAdvertType,
} from '@trendency/kesma-ui';
import { ArticleSeoFields } from '@trendency/kesma-ui/lib/definitions/article-card.definitions';
import { Observable, Subject } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';
import {
  ArticleCardComponent,
  BlockTitleComponent,
  capitalize,
  categoriesMetaInfo,
  createKeywords,
  defaultMetaInfo,
  ExternalRecommendationsComponent,
  MindmegetteArticleCardType,
  MindmegetteBlockTitleType,
  MindmegettePagerComponent,
  OverwritePriorityContentFlagsPipe,
  RssFeedService,
  SearchResultSorterComponent,
} from '../../../../shared';
import { LayoutComponent } from '../../../layout/components/layout/layout.component';
import { SidebarComponent } from '../../../layout/components/sidebar/sidebar.component';
import { CategoryResolverResponse } from '../../api/category.definitions';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';

@Component({
  selector: 'app-category-page',
  templateUrl: './category-page.component.html',
  styleUrls: ['./category-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    LayoutComponent,
    SearchResultSorterComponent,
    NgFor,
    AdvertisementAdoceanComponent,
    ExternalRecommendationsComponent,
    SidebarComponent,
    AsyncPipe,
    SlicePipe,
    BlockTitleComponent,
    ArticleCardComponent,
    MindmegettePagerComponent,
    BreadcrumbComponent,
    OverwritePriorityContentFlagsPipe,
  ],
})
export class CategoryPageComponent implements OnInit, OnDestroy {
  limitable: LimitableMeta;
  page = 0;
  recommendations?: ArticleCard[];
  seoData: ArticleSeoFields;
  LayoutPageType = LayoutPageType;
  box_1_tablet?: Advertisement;
  box_2_tablet?: Advertisement;
  publishDateOrder: string;

  public adPageType = PAGE_TYPES.other_pages;
  public columnSlug: string;
  public articles: ArticleCard[];

  adverts?: AdvertisementsByMedium;
  showDesktopAd: boolean;
  readonly MindmegetteBlockTitleType = MindmegetteBlockTitleType;
  readonly ArticleCardType = MindmegetteArticleCardType;
  excludedIds: string[] = [];

  private readonly destroy$: Subject<boolean> = new Subject();

  public pageData$: Observable<{
    pageData: CategoryResolverResponse;
    adPageType: string;
    struct: LayoutElementRow[];
  }> = this.route.data.pipe(
    map((result: any) => {
      const pageData: CategoryResolverResponse = result?.pageData as CategoryResolverResponse;
      const articlesData = result?.pageData?.category;
      this.excludedIds = pageData?.excludedIds;
      this.columnSlug = pageData?.slug;
      this.articles = articlesData?.data.map(mapBackendArticleDataToArticleCard);
      this.limitable = articlesData?.meta?.limitable;
      this.publishDateOrder = this.route.snapshot.queryParams?.['publishDate_order[]'] || 'desc';
      this.recommendations = result?.recommendations;
      this.seoData = result?.pageData?.category?.meta?.column?.seo as ArticleSeoFields;

      // Send a sendPageView analytics.
      this.analytics.sendPageView({
        pageCategory: this.columnSlug,
      });

      this.initAds();

      // Set RSS Feed
      this.rssFeedService.removeRssFeed(true);
      this.rssFeedService.addRssFeed(false, this.columnSlug, pageData.columnTitle);

      this.setMetaData(pageData.columnTitle);
      this.adStore.setArticleParentCategory(this.adPageType);
      return {
        pageData,
        adPageType: this.adPageType,
        struct: pageData.layoutApiResponse?.struct as LayoutElementRow[],
      };
    })
  );

  constructor(
    private readonly route: ActivatedRoute,
    private readonly analytics: AnalyticsService,
    private readonly cdr: ChangeDetectorRef,
    private readonly seo: SeoService,
    private readonly adStore: AdvertisementAdoceanStoreService,
    private readonly router: Router,
    private readonly rssFeedService: RssFeedService,
    private readonly utilsService: UtilService
  ) {}

  ngOnInit(): void {
    this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe(({ page }) => {
      this.page = page;
    });

    this.showDesktopAd = isDesktopAdShared(this.utilsService.isBrowser(), 1030);
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
    this.adStore.setArticleParentCategory('');
  }

  protected initAds(): void {
    this.resetAds();

    this.adStore.advertisemenets$.pipe(takeUntil(this.destroy$)).subscribe((ads) => {
      this.adverts = this.adStore.separateAdsByMedium(ads, this.adPageType, ALL_BANNER_LIST, SecondaryFilterAdvertType.REPLACEABLE, PAGE_TYPES.other_pages);

      this.box_1_tablet = { ...this.adverts?.mobile?.['mobilrectangle_1'], medium: 'desktop' };
      this.box_2_tablet = { ...this.adverts?.mobile?.['mobilrectangle_2'], medium: 'desktop' };
      this.cdr.detectChanges();
    });
  }

  private resetAds(): void {
    this.adverts = undefined;
    this.cdr.detectChanges();
  }

  private setMetaData(columnTitle: string): void {
    const page = (this.limitable?.pageCurrent || 0) + 1;
    const pageTitle = page > 1 ? page + '. oldal - ' : '';
    const seoColumnTitle = this.seoData?.seoTitle ?? columnTitle;
    const defaultTitle = `${capitalize(seoColumnTitle)} | ${defaultMetaInfo.ogSiteName}`;
    const title = pageTitle.concat(defaultTitle);
    const overrideDescription = this.seoData?.seoDescription ?? categoriesMetaInfo?.[this.columnSlug]?.description;
    //eslint-disable-next-line max-len
    const description = `${capitalize(
      columnTitle
    )} gasztronómia témában cikket keresel? Jó helyen jársz: tippek, szakértői tanácsok, bevált főzési trükkök, bevált sütési
    technikák összegyűjtve!`;
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title,
      ogTitle: title,
      description: overrideDescription ?? description,
      ogDescription: overrideDescription ?? description,
      twitterDescription: overrideDescription ?? description,
      robots: this.seoData?.seoRobotsMeta ?? 'index, nofollow',
      keywords: createKeywords(this.seoData),
    };
    this.seo.setMetaData(metaData);
    const canonical = this.seoData?.seoCanonicalUrl ?? createCanonicalUrlForPageablePage('rovat', this.route.snapshot);
    if (canonical) this.seo.updateCanonicalUrl(canonical);
  }

  onChangeSort(sort: Record<string, string>): void {
    const contentTypeKey = 'content_types[]';
    if (contentTypeKey in sort) {
      delete sort[contentTypeKey];
    }
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { ...sort, page: null },
      queryParamsHandling: 'merge',
    });
  }
}
