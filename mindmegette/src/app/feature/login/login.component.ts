import { AsyncPipe, DOCUMENT, NgIf } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { IMetaData, SeoService, StorageService, UtilService } from '@trendency/kesma-core';
import { createCanonicalUrlForPageablePage, KesmaFormControlComponent, markControlsTouched } from '@trendency/kesma-ui';
import { ReCaptchaV3Service } from 'ngx-captcha';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import {
  ApiService,
  AuthService,
  BackendAllowedLoginMethodsResponse,
  createMMETitle,
  defaultMetaInfo,
  LatestRecipesRecommendationComponent,
  MindmegetteSimpleButtonComponent,
  MindmegetteSocialLoginButtonsComponent,
  SocialProvider,
  StickyImageWrapperComponent,
  WelcomeLayerService,
} from '../../shared';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    StickyImageWrapperComponent,
    NgIf,
    FormsModule,
    ReactiveFormsModule,
    KesmaFormControlComponent,
    RouterLink,
    LatestRecipesRecommendationComponent,
    AsyncPipe,
    MindmegetteSimpleButtonComponent,
    MindmegetteSocialLoginButtonsComponent,
  ],
})
export class LoginComponent implements OnInit {
  formGroup: UntypedFormGroup;
  showPassword = false;
  isLoading = false;
  error: string | null = null;
  SocialProvider = SocialProvider;
  allowedLoginMethods$: Observable<BackendAllowedLoginMethodsResponse> = this.apiService.getAllowedLoginMethods();
  showNewPasswordTitle = false;

  constructor(
    private readonly authService: AuthService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly router: Router,
    private readonly cdr: ChangeDetectorRef,
    private readonly reCaptchaV3Service: ReCaptchaV3Service,
    private readonly seo: SeoService,
    @Inject(DOCUMENT) private readonly document: Document,
    private readonly utilsService: UtilService,
    private readonly apiService: ApiService,
    private readonly route: ActivatedRoute,
    private readonly storageService: StorageService,
    private readonly welcomeLayerService: WelcomeLayerService
  ) {}

  ngOnInit(): void {
    if (this.utilsService.isBrowser()) {
      this.showNewPasswordTitle = this.document.defaultView?.history.state.withNewPassword;
    }

    this.initForm();
    this.setMetaData();
  }

  initForm(): void {
    this.formGroup = this.formBuilder.group({
      email: [null, [Validators.required]],
      password: [null, [Validators.required]],
    });
  }

  login(): void {
    if (this.formGroup) {
      markControlsTouched(this.formGroup);
    }

    if (!this.formGroup.valid) {
      return;
    }

    this.error = null;
    this.isLoading = true;

    this.reCaptchaV3Service.execute(
      environment.googleSiteKey ?? '',
      'app_publicapi_portal_user_login',
      (recaptchaToken: string) => {
        this.authService.authenticate(this.formGroup.value, recaptchaToken).subscribe({
          next: () => {
            this.isLoading = false;
            this.cdr.detectChanges();
            const redirectUrl = this.route.snapshot.queryParams['redirect'] ?? this.storageService.getLocalStorageData('loginRedirectUrl');
            this.storageService.setLocalStorageData('loginRedirectUrl', null);
            if (redirectUrl) {
              const url = new URL(decodeURIComponent(redirectUrl), this.seo.hostUrl);
              const queryParams = Object.fromEntries(url.searchParams);
              this.router.navigate([url.pathname], {
                queryParams,
              });
            } else {
              this.router.navigate(['/']);
              this.welcomeLayerService.openModal();
            }
          },
          error: (err) => {
            if (err?.error?.data?.message === 'Email not verified') {
              this.error = 'Kérjük, erősítsd meg a regisztrációd az e-mail-ben kapott link segítségével!';
            } else {
              this.error = 'Hibás bejelentkezési adatok, kérjük próbáld újra!';
            }
            this.isLoading = false;
            this.cdr.detectChanges();
          },
        });
      },
      {
        useGlobalDomain: false,
      },
      () => {
        this.error = 'Captcha: Robot ellenőrzés hiba!';
        this.isLoading = false;
        this.cdr.detectChanges();
      }
    );
  }

  loginWithSocialProvider(provider: SocialProvider): void {
    if (this.utilsService.isBrowser()) {
      this.document.location.href = this.authService.getSocialProviderAuthUrl(provider);
    }
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('bejelentkezes');
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createMMETitle('Bejelentkezés');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      robots: '',
    };
    this.seo.setMetaData(metaData, { canRobotsBeEmpty: true });
  }
}
