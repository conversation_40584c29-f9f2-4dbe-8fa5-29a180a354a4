<article class="article" *ngIf="data as article">
  <div class="article-header">
    <div class="article-wrapper">
      <a *ngIf="article?.columnTitle" [routerLink]="['/rovat', article?.columnSlug]" class="article-wrapper-category">{{ article?.columnTitle }} </a>
      <a
        *ngIf="article?.minuteToMinute === MinuteToMinuteState.RUNNING"
        [routerLink]="['/rovat', article?.columnSlug]"
        class="article-wrapper-category article-wrapper-live"
      >
        <span>Élő</span>
        <i class="icon mindmegette-icon-live"></i>
      </a>
    </div>

    <h1 class="article-title">{{ article?.title }}</h1>

    <ng-container *ngIf="article?.minuteToMinute === MinuteToMinuteState.RUNNING">
      <div class="article-pre-title" *ngIf="article?.lead">{{ article?.lead }}</div>
    </ng-container>

    <ng-container *ngIf="article?.minuteToMinute !== MinuteToMinuteState.RUNNING">
      <div class="article-pre-title" *ngIf="article?.preTitle">{{ article?.preTitle }}</div>
    </ng-container>

    <div class="article-author">
      <a *ngIf="article?.publicAuthor" [routerLink]="['/szerzo', article?.publicAuthorSlug || article?.publicAuthor]">
        <img [src]="article?.avatar || '/assets/images/author-placeholder.svg'" alt="" class="article-author-img" />
        <span class="article-author-name">{{ article?.publicAuthor }}</span>
      </a>
      @if (publishDates; as pub) {
        <div *ngIf="pub.original" class="separator"></div>
        <div *ngIf="pub.original" class="article-publish-date">{{ pub.original | publishDate: 'yyyy. MM. dd.' }}</div>
        <div *ngIf="pub.updated" class="separator"></div>
        <div *ngIf="pub.updated && (!article.minuteToMinuteBlocks || !article.minuteToMinuteBlocks?.[0])" class="article-publish-date">
          Frissítve: {{ pub.updated | publishDate: 'yyyy. MM. dd.' }}
        </div>
      }
      <div *ngIf="article?.minuteToMinuteBlocks?.length" class="article-publish-date">
        Frissítve: {{ (article?.minuteToMinuteBlocks)![0].date | updatedAt }}
      </div>
    </div>
  </div>

  <div class="article-thumbnail-wrapper" *ngIf="!article?.hideThumbnailFromBody && article?.thumbnail">
    <img
      withFocusPoint
      [data]="article?.thumbnailFocusedImages"
      class="article-thumbnail"
      *ngIf="!article?.hideThumbnailFromBody"
      [displayedUrl]="article?.thumbnail"
      [displayedAspectRatio]="{ desktop: '16:9' }"
      [alt]="article?.thumbnailInfo?.altText || ''"
      loading="eager"
      fetchpriority="high"
    />
    <i *ngIf="article?.isGuaranteeType" class="icon mindmegette-icon-guarantee"></i>
    <div class="article-thumbnail-meta" *ngIf="article?.thumbnailInfo?.photographer || article?.thumbnailInfo?.source">
      <ng-container *ngIf="article?.thumbnailInfo?.photographer as photographer"> Fotó: {{ photographer }} </ng-container>
      <ng-container *ngIf="article?.thumbnailInfo?.source as source">
        <ng-container *ngIf="article?.thumbnailInfo?.photographer">/ &copy;</ng-container> {{ source }}
      </ng-container>
    </div>
  </div>
</article>
