<app-sticky-image-wrapper>
  <section class="finalize-registration">
    <h2 *ngIf="isLoading">K<PERSON>rjük várj...</h2>

    <ng-container *ngIf="!isLoading && error">
      <h1>Upsz!</h1>
      <h2>Hiba történt!</h2>
      <div class="finalize-registration-text">
        {{ error }}
      </div>
      <div class="finalize-registration-navigation">
        <mindmegette-simple-button color="primary" icon="mindmegette-icon-white-left-arrow" iconPosition="left" routerLink="/bejelentkezes">
          Vissza a bejelentkezésre
        </mindmegette-simple-button>
        <mindmegette-simple-button color="primary" icon="mindmegette-icon-white-left-arrow" iconPosition="left" routerLink="/regisztracio">
          Vissza a regisztrációra
        </mindmegette-simple-button>
      </div>
    </ng-container>

    <ng-container *ngIf="!isLoading && !error">
      <h1 class="success">Fiók véglegesítése</h1>
      <div class="finalize-registration-text success">
        Mindmegette fiókodat sikeresen összekapcsoltuk közösségi profiloddal, kérjük a véglegesítéshez add meg az alábbi adatokat!
      </div>
      <form (ngSubmit)="finalizeRegistration()" *ngIf="formGroup" [formGroup]="formGroup" class="mindmegette-form">
        <!-- Vezetéknév és keresztnév -->
        <div class="mindmegette-form-row row">
          <div class="col-12 col-md-6">
            <kesma-form-control>
              <label class="mindmegette-form-label" for="lastName">Vezetéknév<strong>*</strong></label>
              <input class="mindmegette-form-input" formControlName="lastName" id="lastName" type="text" />
            </kesma-form-control>
          </div>
          <div class="col-12 col-md-6">
            <kesma-form-control>
              <label class="mindmegette-form-label" for="firstName">Keresztnév<strong>*</strong></label>
              <input class="mindmegette-form-input" formControlName="firstName" id="firstName" type="text" />
            </kesma-form-control>
          </div>
        </div>

        <!-- Felhasználónév -->
        <div class="mindmegette-form-row">
          <kesma-form-control>
            <label class="mindmegette-form-label" for="username">Felhasználónév<strong>*</strong> </label>
            <input class="mindmegette-form-input" formControlName="username" id="username" type="text" />
          </kesma-form-control>
          <small class="mindmegette-form-small"> Ez a becenév fog megjelenni a hozzászólásoknál. Legalább 6 karakterből kell állnia. </small>
        </div>

        <!-- Checkboxok -->
        <div class="finalize-registration-checkboxes">
          <kesma-form-control class="checkbox">
            <label class="mindmegette-form-checkbox" for="terms">
              <input formControlName="terms" id="terms" type="checkbox" />
              <span
                >Megismertem és elfogadom a Mindmegette.hu weboldal
                <a class="mindmegette-form-checkbox-link" href="/felhasznalasi-feltetelek" target="_blank">felhasználási feltételeit</a>. A honlapra történő
                regisztrációval elfogadom a Mediaworks Hungary Zrt. (1082 Budapest, Üllői út 82.) Mindmegette.hu weboldallal kapcsolatos adatkezelésre vonatkozó
                <a class="mindmegette-form-checkbox-link" href="/adatvedelmi-tajekoztato" target="_blank">adatvédelmi tájékoztatóját</a> és hozzájárulok ahhoz,
                hogy az általam közölt adatokat a regisztrációval összefüggő célokból és az általam használt szolgáltatások működtetése érdekében a Mediaworks
                Hungary Zrt. kezelje.
              </span>
            </label>
          </kesma-form-control>
          <kesma-form-control class="checkbox">
            <label class="mindmegette-form-checkbox" for="marketing">
              <input formControlName="marketing" id="marketing" type="checkbox" />
              <span>
                Érdekelnek a Mediaworks Hungary Zrt. és más harmadik felek különleges ajánlatai és reklámjai (ideértve például a recept és más tartalmi
                ajánlókat), ezért szeretnék direkt marketing - közvetlen üzletszerzési célú - hírleveleket kapni e-mailen. Hozzájárulok a megadott személyes
                adataim kezeléséhez az adatkezelési tájékoztatóban foglaltak szerint (a személyes adatokat csak a Mediaworks Hungary Zrt. fogja kezelni).
                Adatvédelmi tájékoztató: <a class="mindmegette-form-checkbox-link" href="/adatvedelmi-tajekoztato" target="_blank">link</a>
              </span>
            </label>
          </kesma-form-control>
        </div>

        <div *ngIf="formError" class="mindmegette-form-general-error">
          {{ formError }}
        </div>

        <div class="finalize-registration-action">
          <mindmegette-simple-button [disabled]="isLoading" [isSubmit]="true" class="w-100" color="primary">
            {{ isLoading ? 'Kérjük várj...' : 'Fiók véglegesítése' }}
          </mindmegette-simple-button>
        </div>
      </form>
    </ng-container>

    <app-latest-recipes-recommendation></app-latest-recipes-recommendation>
  </section>
</app-sticky-image-wrapper>
