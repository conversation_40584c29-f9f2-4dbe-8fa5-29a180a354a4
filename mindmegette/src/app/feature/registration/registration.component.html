<app-sticky-image-wrapper>
  <section *ngIf="!isSubmitted" class="registration">
    <h1><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></h1>
    <ng-container *ngIf="allowedLoginMethods$ | async as allowedLoginMethods">
      <form (ngSubmit)="register()" *ngIf="formGroup" [formGroup]="formGroup" class="mindmegette-form">
        <ng-container *ngIf="allowedLoginMethods.email">
          <!-- Vezetéknév és keresztnév -->
          <div class="row mindmegette-form-row">
            <div class="col-12 col-md-6">
              <kesma-form-control>
                <label class="mindmegette-form-label" for="lastName">Vezetéknév<strong>*</strong></label>
                <input class="mindmegette-form-input" formControlName="lastName" id="lastName" type="text" />
              </kesma-form-control>
            </div>
            <div class="col-12 col-md-6">
              <kesma-form-control>
                <label class="mindmegette-form-label" for="firstName">Keresztnév<strong>*</strong></label>
                <input class="mindmegette-form-input" formControlName="firstName" id="firstName" type="text" />
              </kesma-form-control>
            </div>
          </div>

          <!-- Felhasználónév -->
          <div class="mindmegette-form-row">
            <kesma-form-control>
              <label class="mindmegette-form-label" for="username">Felhasználónév<strong>*</strong> </label>
              <input class="mindmegette-form-input" formControlName="username" id="username" type="text" />
            </kesma-form-control>
            <small class="mindmegette-form-small"> Ez a becenév fog megjelenni a hozzászólásoknál. Legalább 6 karakterből kell állnia. </small>
          </div>

          <!-- E-mail cím -->
          <div class="mindmegette-form-row">
            <kesma-form-control>
              <label class="mindmegette-form-label" for="email">E-mail cím<strong>*</strong></label>
              <input class="mindmegette-form-input" formControlName="email" id="email" type="text" />
            </kesma-form-control>
          </div>

          <!-- Jelszó -->
          <div class="mindmegette-form-row">
            <kesma-form-control>
              <label class="mindmegette-form-label" for="password"> Jelszó<strong>*</strong> </label>
              <div class="mindmegette-form-input-password">
                <input [type]="showPassword ? 'text' : 'password'" class="mindmegette-form-input" formControlName="password" id="password" />
                <img
                  (click)="showPassword = !showPassword"
                  [src]="showPassword ? '/assets/images/icons/icon-no-eye.svg' : '/assets/images/icons/icon-eye.svg'"
                  alt="Jelszó megtekintése"
                  class="mindmegette-form-input-password-img"
                />
              </div>
            </kesma-form-control>
            <small class="mindmegette-form-small"
              >A választott jelszónak legalább 6 karakterből kell állnia és tartalmaznia kell kisbetűt, nagybetűt és számot.</small
            >
          </div>

          <!-- Checkboxok -->
          <div class="registration-checkboxes">
            <kesma-form-control class="checkbox">
              <label class="mindmegette-form-checkbox" for="terms">
                <input formControlName="terms" id="terms" type="checkbox" />
                <span
                  >Megismertem és elfogadom a Mindmegette.hu weboldal
                  <a class="mindmegette-form-checkbox-link" href="/felhasznalasi-feltetelek" target="_blank">felhasználási feltételeit</a>. A honlapra történő
                  regisztrációval elfogadom a Mediaworks Hungary Zrt. (1082 Budapest, Üllői út 82.) Mindmegette.hu weboldallal kapcsolatos adatkezelésre
                  vonatkozó <a class="mindmegette-form-checkbox-link" href="/adatvedelmi-tajekoztato" target="_blank">adatvédelmi tájékoztatóját</a> és
                  hozzájárulok ahhoz, hogy az általam közölt adatokat a regisztrációval összefüggő célokból és az általam használt szolgáltatások működtetése
                  érdekében a Mediaworks Hungary Zrt. kezelje.
                </span>
              </label>
            </kesma-form-control>
            <kesma-form-control class="checkbox">
              <label class="mindmegette-form-checkbox" for="marketing">
                <input formControlName="marketing" id="marketing" type="checkbox" />
                <span>
                  Érdekelnek a Mediaworks Hungary Zrt. és más harmadik felek különleges ajánlatai és reklámjai (ideértve például a recept és más tartalmi
                  ajánlókat), ezért szeretnék direkt marketing - közvetlen üzletszerzési célú - hírleveleket kapni e-mailen. Hozzájárulok a megadott személyes
                  adataim kezeléséhez az adatkezelési tájékoztatóban foglaltak szerint (a személyes adatokat csak a Mediaworks Hungary Zrt. fogja kezelni).
                  Adatvédelmi tájékoztató: <a class="mindmegette-form-checkbox-link" href="/adatvedelmi-tajekoztato" target="_blank">link</a>
                </span>
              </label>
            </kesma-form-control>
          </div>

          <div *ngIf="error" class="mindmegette-form-general-error">
            {{ error }}
          </div>

          <div class="registration-action">
            <mindmegette-simple-button [disabled]="isLoading" [isSubmit]="true" class="w-100" color="primary">
              {{ isLoading ? 'Kérjük várj...' : 'Létrehozom a fiókom' }}
            </mindmegette-simple-button>
          </div>
        </ng-container>
        <div class="registration-social-buttons">
          <div class="registration-form-or">vagy</div>
          <mindmegette-social-login-buttons
            (facebookClickEvent)="registerWithSocialProvider(SocialProvider.FACEBOOK)"
            (googleClickEvent)="registerWithSocialProvider(SocialProvider.GOOGLE)"
            [isFacebookAllowed]="allowedLoginMethods[SocialProvider.FACEBOOK]"
            [isGoogleAllowed]="allowedLoginMethods[SocialProvider.GOOGLE]"
            [isLoading]="isLoading"
          ></mindmegette-social-login-buttons>
        </div>
      </form>
      <app-latest-recipes-recommendation></app-latest-recipes-recommendation>
    </ng-container>
  </section>

  <section *ngIf="isSubmitted" class="registration-submitted">
    <div class="registration-submitted-title">
      <h1>Sikeres regisztráció</h1>
      <div class="registration-submitted-checkmark">
        <i class="icon mindmegette-icon-checkmark"></i>
      </div>
    </div>
    <h2>Üdvözlünk felhasználóink között!</h2>
    <div class="registration-submitted-text">Egy ellenőrző e-mailt küldtünk neked. Kérjük, ellenőrizd postafiókod, és kövesd a levélben kapott útmutatót!</div>
    <mindmegette-simple-button color="primary" icon="mindmegette-icon-white-right-arrow" iconPosition="right" routerLink="/">
      Tovább a főoldalra
    </mindmegette-simple-button>
    <app-latest-recipes-recommendation></app-latest-recipes-recommendation>
  </section>
</app-sticky-image-wrapper>
