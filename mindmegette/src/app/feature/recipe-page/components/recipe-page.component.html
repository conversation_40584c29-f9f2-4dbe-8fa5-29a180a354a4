<section *ngIf="recipe">
  <app-breadcrumb [data]="[{ label: recipe.breadcrumbTitle || recipe.title }]" />
  <div class="wrapper">
    <div class="wrapper-row">
      <div class="wrapper-col with-image">
        <ng-container *ngIf="thumbnailUrls?.length !== MAX_THUMBNAIL_SIZE; else hasSecondaryThumbnail">
          <ng-container *ngTemplateOutlet="coverImage"></ng-container>
        </ng-container>
        <i *ngIf="recipe?.mmeWarranty" class="icon mindmegette-icon-guarantee"></i>
      </div>
      <div class="wrapper-col centered">
        <div class="recipe-categories">
          <mindmegette-category-label [styleID]="CategoryLabelTypes.RECIPE" title="recept"></mindmegette-category-label>
          <mindmegette-category-label
            *ngIf="recipe?.difficulty"
            [styleID]="CategoryLabelTypes.CATEGORY"
            [title]="recipe?.difficulty?.title"
          ></mindmegette-category-label>
        </div>
        <div *ngIf="hasAllergics" class="recipe-allergics">
          <ng-container *ngFor="let allergics of recipe?.allergics">
            <img
              *ngIf="allergics?.icon?.thumbnailUrl as thumbnail"
              [alt]="allergics?.title"
              [src]="thumbnail"
              [title]="allergics?.title"
              class="recipe-allergics-item"
            />
          </ng-container>
        </div>
        <h1 class="recipe-title">{{ recipe?.title }}</h1>
        <div class="recipe-details">
          <div class="recipe-meta divider">{{ recipe?.totalTime }} perc</div>
          <div [ngClass]="{ divider: recipe?.cost?.title }" class="recipe-meta">{{ recipe?.madeForPeople }} adag</div>
          <div *ngIf="recipe?.cost?.title" [ngClass]="{ divider: recipe?.rateAverage }" class="recipe-meta">{{ recipe?.cost?.title }}</div>
          <div *ngIf="recipe?.rateAverage" class="recipe-rate"><i class="icon icon-star-full"></i> {{ getRoundedRateAverage(recipe?.rateAverage!) }}</div>
        </div>
        <div *ngIf="recipe?.sentByUser || recipe?.publicAuthor" class="recipe-author">
          <ng-container *ngIf="recipe?.sentByUser; else publicAuthor">
            <a [routerLink]="['/profil', recipe?.sentByUser?.id]" class="recipe-author-container">
              <img
                [alt]="'A receptet feltöltő ' + recipe?.sentByUser?.fullName + ' felhasználónk profilképe'"
                [src]="recipe?.sentByUser?.avatarThumbnail ?? recipe?.sentByUser?.avatar ?? '/assets/images/author-placeholder.svg'"
                class="recipe-author-avatar"
                loading="eager"
              />
              <div class="recipe-author-name">
                <div class="recipe-author-name-wrapper">
                  {{ recipe?.sentByUser?.fullName }}
                </div>
                <app-profile-badges [badgeData]="recipe?.sentByUser?.badges!" [isSmallStar]="true"></app-profile-badges>
              </div>
            </a>
          </ng-container>
          <ng-template #publicAuthor>
            <ng-container *ngIf="recipe?.publicAuthor">
              <a [routerLink]="['/szerzo', recipe?.publicAuthor?.slug || recipe?.publicAuthor?.fullName]" class="recipe-author-container">
                <img
                  [alt]="'A receptet feltöltő ' + recipe?.publicAuthor?.fullName + ' szerzőnk profilképe'"
                  [src]="recipe?.publicAuthor?.thumbnailUrl ?? recipe?.publicAuthor?.avatarFullSizeUrl ?? '/assets/images/author-placeholder.svg'"
                  class="recipe-author-avatar"
                  loading="eager"
                />
                <div class="recipe-author-name">
                  <div class="recipe-author-name-wrapper">
                    {{ recipe?.publicAuthor?.fullName }}
                    <i *ngIf="recipe?.publicAuthor?.isMaestroAuthor" class="icon mindmegette-icon-verified"></i>
                  </div>
                </div>
              </a>
            </ng-container>
          </ng-template>
        </div>
        @if (publishDates; as pub) {
          <div class="recipe-dates">
            <div class="recipe-author-modified">
              {{ pub.original | wtfDateToDate | publishDate: 'yyyy. MM. dd.' }}
            </div>
            <div *ngIf="pub.updated" class="recipe-author-modified pre-divider">
              Frissítve: {{ pub.updated | wtfDateToDate | publishDate: 'yyyy. MM. dd.' }}
            </div>
          </div>
        }
        <app-recipe-saver-popover [recipeSlug]="recipe.slug" [savedRecipeGroups]="(savedRecipeGroups$ | async) || []"> </app-recipe-saver-popover>
      </div>
    </div>
  </div>

  <div class="wrapper with-aside">
    <div class="left-column">
      <div class="sides">
        <div class="left-side">
          <app-article-social-share [emailSubject]="recipe?.title!"></app-article-social-share>
          <mindmegette-tag *ngIf="tagsAndCategories?.length" [data]="tagsAndCategories"></mindmegette-tag>
          <app-recipe-ingredients-content *ngIf="recipe?.recipeIngredients?.length" [adverts]="adverts" [data]="recipe"></app-recipe-ingredients-content>
        </div>
        <div class="right-side">
          <kesma-advertisement-adocean *ngIf="showDesktopAdLocal && adverts?.desktop?.['roadblock_1'] as ad" [ad]="ad" [style]="{ margin: 'var(--ad-margin)' }">
          </kesma-advertisement-adocean>

          <kesma-advertisement-adocean *ngIf="!showDesktopAdLocal && box_1_tablet as ad" [ad]="ad" [style]="{ margin: 'var(--ad-margin)' }">
          </kesma-advertisement-adocean>

          <kesma-advertisement-adocean *ngIf="adverts?.mobile?.['mobilrectangle_1'] as ad" [ad]="ad" [style]="{ margin: 'var(--ad-margin)' }">
          </kesma-advertisement-adocean>

          <app-recipe-energy-content [data]="recipe"></app-recipe-energy-content>

          <h2 class="wysiwyg-title">Elkészítés</h2>

          <ng-container [ngTemplateOutletContext]="{ body: recipe?.makingText }" [ngTemplateOutlet]="bodyContent"></ng-container>

          <app-article-info-box *ngIf="infoBox?.description" [data]="infoBox"></app-article-info-box>

          <app-recipe-rating
            *ngIf="recipe?.allowedRating"
            [isVoted]="(isVoted$ | async) === true"
            [rateAverage]="recipe?.rateAverage!"
            [rateCount]="recipe?.rateCount!"
            [slug]="recipe?.slug!"
          >
          </app-recipe-rating>

          <app-recipe-recommendation-content *ngIf="recipeRecommendations?.length" [data]="recipeRecommendations | slice: 0 : 6" title="Ajánljuk még">
          </app-recipe-recommendation-content>

          <kesma-advertisement-adocean *ngIf="showDesktopAdLocal && adverts?.desktop?.['roadblock_2'] as ad" [ad]="ad" [style]="{ margin: 'var(--ad-margin)' }">
          </kesma-advertisement-adocean>

          <kesma-advertisement-adocean *ngIf="!showDesktopAdLocal && box_2_tablet as ad" [ad]="ad" [style]="{ margin: 'var(--ad-margin)' }">
          </kesma-advertisement-adocean>

          <kesma-advertisement-adocean *ngIf="adverts?.mobile?.['mobilrectangle_2'] as ad" [ad]="ad" [style]="{ margin: 'var(--ad-margin)' }">
          </kesma-advertisement-adocean>

          <app-recipe-recommendation-content *ngIf="similarRecipes?.length" [data]="similarRecipes | slice: 0 : 6" title="Hasonló receptek">
          </app-recipe-recommendation-content>

          <app-comment-section
            #commentSection
            *ngIf="recipe && recipe?.allowComments"
            [articleID]="recipe?.id"
            [commentCount]="socialCount.commentCount ?? 0"
            id="kommentek"
            type="recipe"
          >
          </app-comment-section>

          <app-recipe-recommendation-content *ngIf="freshRecipes?.length" [data]="freshRecipes | slice: 0 : 6" title="Friss receptek">
          </app-recipe-recommendation-content>

          <kesma-advertisement-adocean *ngIf="showDesktopAdLocal && adverts?.desktop?.['roadblock_3'] as ad" [ad]="ad" [style]="{ margin: 'var(--ad-margin)' }">
          </kesma-advertisement-adocean>

          <kesma-advertisement-adocean *ngIf="!showDesktopAdLocal && box_3_tablet as ad" [ad]="ad" [style]="{ margin: 'var(--ad-margin)' }">
          </kesma-advertisement-adocean>

          <kesma-advertisement-adocean *ngIf="adverts?.mobile?.['mobilrectangle_3'] as ad" [ad]="ad" [style]="{ margin: 'var(--ad-margin)' }">
          </kesma-advertisement-adocean>

          <app-recommended-articles title="Friss sztorik" *ngIf="freshArticles?.length" [data]="freshArticles | slice: 0 : 6"></app-recommended-articles>

          <app-external-recommendations
            [mobilrectangle_ottboxextra]="adverts?.mobile?.['mobilrectangle_ottboxextra']"
            [roadblock_ottboxextra]="adverts?.desktop?.['roadblock_ottboxextra']"
            [mobilrectangle_ottboxextra_tablet]="mobilrectangle_ottboxextra_tablet"
            [externalRecommendations]="recommendations"
            [showDesktopAdLocal]="showDesktopAdLocal"
          ></app-external-recommendations>
        </div>
      </div>
    </div>
    <aside>
      <app-sidebar [adPageType]="adPageType"></app-sidebar>
    </aside>

    <div *ngFor="let localAdsByDevice of localAdverts" class="local-ads">
      <div class="ads">
        <ng-container *ngFor="let localAd of localAdsByDevice | slice: 0 : 2">
          <kesma-advertisement-adocean *ngIf="localAdverts as ad" [ad]="localAd" [style]="{ margin: 'var(--ad-margin)' }"> </kesma-advertisement-adocean>
        </ng-container>
      </div>
      <div class="ads">
        <ng-container *ngFor="let localAd of localAdsByDevice | slice: 2 : 7">
          <kesma-advertisement-adocean *ngIf="localAdverts as ad" [ad]="localAd" [style]="{ margin: 'var(--ad-margin)' }"> </kesma-advertisement-adocean>
        </ng-container>
      </div>
    </div>
  </div>

  <div [class.slided]="savedIngredientsNotification$ | async" class="ingredients-notification">
    <span class="ingredients-notification-message">Hozzávalók sikeresen mentve a bevásárlólistába!</span>
  </div>
</section>

<ng-template #hasSecondaryThumbnail>
  <div class="swiper-wrapper">
    <app-recipe-secondary-thumbnail [data]="thumbnailUrls"></app-recipe-secondary-thumbnail>
  </div>
</ng-template>

<ng-template #coverImage>
  <img
    [alt]="recipe?.title + ' recept'"
    [src]="thumbnailUrls?.[0] || '/assets/images/placeholder.jpg'"
    class="recipe-thumbnail"
    loading="eager"
    fetchpriority="high"
  />
</ng-template>

<ng-template #bodyContent let-body="body">
  <ng-container *ngFor="let element of body">
    <ng-container [ngSwitch]="element.type">
      <ng-container *ngSwitchCase="ArticleBodyType.Wysywyg">
        <ng-container *ngFor="let wysiwygDetail of element?.details">
          <mindmegette-wysiwyg-box [html]="(wysiwygDetail?.value | wrapTable) || ''" trArticleFileLink></mindmegette-wysiwyg-box>
        </ng-container>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.MediaVideo">
        <kesma-article-video [data]="element?.details[0]?.value"></kesma-article-video>
      </ng-container>
    </ng-container>
  </ng-container>
</ng-template>
