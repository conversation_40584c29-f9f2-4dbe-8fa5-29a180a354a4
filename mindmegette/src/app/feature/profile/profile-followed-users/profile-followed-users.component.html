<h3 class="title">
  Követéseim <sup>({{ limitables?.rowAllCount ?? 0 }})</sup>
</h3>

<section class="profiles">
  <div *ngFor="let profile of followedList; index as index" class="profile-card">
    <img [alt]="profile.userName + ' avatar képe'" [src]="profile.avatar || '/assets/images/author-placeholder.svg'" class="thumbnail" />
    <div class="title-wrapper">
      <h3 class="user-name">{{ profile.userName }}</h3>
      @if (profile.followedSince) {
        <span class="followed-since">{{ profile.followedSince | date: 'yyyy.MM.dd' }} óta követed</span>
      }
    </div>
    <a (click)="unfollowUserProfile(profile.id, index)" class="unfollow"><i class="icon icon-x"></i></a>
  </div>
</section>

<mindmegette-pager
  *ngIf="limitables && limitables.pageMax! > 0"
  [rowAllCount]="limitables?.rowAllCount!"
  [rowOnPageCount]="limitables?.rowOnPageCount!"
  [isListPager]="true"
  [isCountPager]="false"
  [hasFirstLastButton]="false"
  [hasSkipButton]="true"
  [allowAutoScrollToTop]="true"
  [maxDisplayedPages]="5"
>
</mindmegette-pager>
