<kesma-layout
  [adPageType]="adPageType"
  [blockTitleRef]="blockTitles"
  [blockTitleWrapperRef]="blockTitleWrapper"
  [breakingNews]="breakingNews"
  [configuration]="configuration"
  [contentComponentInnerWrapperRef]="contentComponentsInnerWrapper"
  [contentComponentWrapperRef]="contentComponentsWrapper"
  [contentComponentsRef]="contentComponents"
  [layoutType]="layoutType ?? LayoutPageTypes.HOME"
  [structure]="structure"
  [editorFrameSize]="editorFrameSize"
></kesma-layout>

<ng-template #blockTitles let-layoutElement="layoutElement" let-layoutType="layoutType">
  <metropol-block-title-row *ngIf="layoutType !== LayoutPageTypes.SIDEBAR" [data]="layoutElement.blockTitle" [isRow]="true"></metropol-block-title-row>
  <metropol-block-title-sidebar *ngIf="layoutType === LayoutPageTypes.SIDEBAR" [data]="layoutElement.blockTitle"></metropol-block-title-sidebar>
</ng-template>

<ng-template #contentComponents let-desktopWidth="desktopWidth" let-extractor="extractor" let-index="index" let-layoutElement="layoutElement">
  <ng-container *ngIf="layoutElement?.config || layoutElement?.configurable === false">
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Ad">
      <kesma-advertisement-adocean
        *ngIf="layoutElement.ad"
        [ad]="layoutElement.ad"
        [isHidden]="layoutElement.contentType !== LayoutElementContentType.Ad && !layoutElement.ad"
      ></kesma-advertisement-adocean>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Opinion">
      <metropol-opinion-layout-card
        *ngIf="layoutElement.extractorData[index] as data"
        [data]="data"
        [isSidebar]="layoutType === LayoutPageTypes.SIDEBAR"
        [styleID]="layoutElement.styleId"
      ></metropol-opinion-layout-card>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Article">
      <metropol-article-card
        *ngIf="layoutElement.extractorData?.[index] as data"
        [data]="data"
        [isSidebar]="layoutType === LayoutPageTypes.SIDEBAR"
        [styleID]="layoutElement.styleId"
      ></metropol-article-card>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.FastNews">
      <metropol-article-card
        *ngIf="layoutElement.extractorData?.[index] as data"
        [data]="data"
        [isSidebar]="layoutType === LayoutPageTypes.SIDEBAR"
        [styleID]="layoutElement.styleId"
      ></metropol-article-card>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.PrBlock">
      <metropol-article-card
        *ngIf="layoutElement.extractorData?.[index] as data"
        [data]="data"
        [isSidebar]="layoutType === LayoutPageTypes.SIDEBAR"
        [styleID]="layoutElement.styleId"
      ></metropol-article-card>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.FinalCountdown">
      <metropol-final-countdown *ngIf="layoutElement.config as data" [data]="data"></metropol-final-countdown>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.HelloBudapest">
      <metropol-several-element-box *ngIf="layoutElement.extractorData as data" [data]="data" [showLink]="true"> </metropol-several-element-box>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Dossier">
      @if (layoutElement.extractorData?.dossierData?.[0]?.sponsorship) {
        <app-dossier-sponsoration-header [data]="layoutElement.extractorData?.dossierData?.[0]"></app-dossier-sponsoration-header>
      }
      <metropol-several-element-box
        *ngIf="layoutElement.extractorData.articles as data"
        [data]="data"
        [title]="getSeveralElementBoxTitle(layoutElement)"
        type="article"
      >
      </metropol-several-element-box>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Vote">
      @if (layoutElement.extractorData; as extractorData) {
        @if ((voteCache[extractorData?.data?.id] | async) || extractorData; as voteData) {
          <metropol-voting (vote)="onVotingSubmit($event, voteData)" *ngIf="voteData.data" [data]="voteData.data" [voteId]="voteData.votedId" />
        }
      }
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.koponyeg">
      <kesma-koponyeg [data]="KoponyegDefinitions" [type]="KoponyegType"></kesma-koponyeg>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Breaking">
      <metropol-breaking-article-card
        *ngIf="layoutElement.extractorData?.[0] as data"
        [data]="data"
        [hasImage]="layoutElement.hasImage"
        [styleID]="layoutElement.styleId"
      >
      </metropol-breaking-article-card>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.HtmlEmbed">
      <kesma-html-embed *ngIf="layoutElement?.config?.htmlContent as data" [data]="data"></kesma-html-embed>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.BestRecommender && index === 0">
      <metropol-best-recommender [data]="layoutElement.extractorData" [isDetailPage]="layoutType !== LayoutPageTypes.HOME"> </metropol-best-recommender>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.IngatlanbazarSearch">
      <kesma-real-estate-bazaar-search-block
        [defaultLocation]="layoutElement.config.defaultLocation"
        [defaultType]="layoutElement.config.defaultType"
        [showAdvertiseButton]="layoutElement.config.showAdvertiseButton"
        [showBudapestLocations]="layoutElement.config.showBudapestLocations"
        [showCountyLocations]="layoutElement.config.showBudapestLocations"
        [showNewBuildButton]="layoutElement.config.showNewBuildButton"
        [showOtherLocations]="layoutElement.config.showOtherLocations"
        [utmSource]="layoutElement.config.utmSource"
      ></kesma-real-estate-bazaar-search-block>
    </ng-container>
  </ng-container>
  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.IngatlanbazarConfigurable">
    <kesma-real-estate-bazaar-block
      (initEvent)="handleRealEstateInitEvent()"
      [data]="realEstateData"
      [itemsToShow]="layoutElement.itemsToShow"
      [showHeader]="layoutElement.showHeader"
    ></kesma-real-estate-bazaar-block>
  </ng-container>
  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.INGATLANBAZAR">
    <kesma-real-estate-bazaar-block
      (initEvent)="handleRealEstateInitEvent()"
      [data]="realEstateData"
      [itemsToShow]="1"
      [showHeader]="true"
    ></kesma-real-estate-bazaar-block>
  </ng-container>
  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.CONFIGURABLE_SPONSORED_BOX">
    <kesma-sponsored-box [data]="layoutElement.config"></kesma-sponsored-box>
  </ng-container>
  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.RSS_BOX">
    <app-rss-box-wrapper
      [articleTitleTextColor]="layoutElement.articleTitleTextColor"
      [backgroundColor]="layoutElement.backgroundColor"
      [columnButtonBackgroundColor]="layoutElement.columnButtonBackgroundColor"
      [columnButtonTextColor]="layoutElement.columnButtonTextColor"
      [columnButtons]="layoutElement.columnButtons"
      [dividerColor]="layoutElement.dividerColor"
      [leadTextColor]="layoutElement.leadTextColor"
      [logoHref]="layoutElement.logoHref"
      [logo]="layoutElement.logo?.selectedVariant?.publicUrl ?? null"
      [numberOfArticles]="layoutElement.numberOfArticles ?? 7"
      [rssUrl]="layoutElement.rssUrl"
      [small]="layoutType === LayoutPageTypes.SIDEBAR"
      [textColor]="layoutElement.textColor"
      [title]="layoutElement.title"
    ></app-rss-box-wrapper>
  </ng-container>
  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.ELECTIONS_BOX">
    <kesma-elections-box [desktopWidth]="desktopWidth" [link]="electionsService.getElections2024Link()" [styleID]="layoutElement.styleId"></kesma-elections-box>
  </ng-container>
  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.DID_YOU_KNOW">
    <app-variable-sponsored-did-you-know-wrapper [id]="layoutElement.config?.selectedDidYouKnowBox?.[0]?.id"></app-variable-sponsored-did-you-know-wrapper>
  </ng-container>
</ng-template>
