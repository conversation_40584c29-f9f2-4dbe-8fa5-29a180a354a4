import { ChangeDetectionStrategy, ChangeDetectorRef, Component, inject, Input, TemplateRef, ViewChild } from '@angular/core';
import {
  AdvertisementAdoceanComponent,
  BlockWrapperTemplateData,
  BreakingNews,
  ElectionsBoxComponent,
  HtmlEmbedComponent,
  KoponyegComponent,
  KoponyegDefinitions,
  KoponyegType,
  LayoutComponent as KesmaLayoutComponent,
  LayoutContentItemWrapperTemplateData,
  LayoutContentParams,
  LayoutElement,
  LayoutElementContentConfiguration,
  LayoutElementContentType,
  LayoutElementRow,
  LayoutPageType,
  mapRealEstateApiDataToRealEstateData,
  PAGE_TYPES,
  provideLayoutDataExtractors,
  RealEstateBazaarApiData,
  RealEstateBazaarBackendResponse,
  RealEstateBazaarBlockComponent,
  RealEstateBazaarData,
  RealEstateBazaarSearchBlockComponent,
  SponsoredBoxComponent,
  VoteDataWithAnswer,
  VoteService,
} from '@trendency/kesma-ui';
import {
  CleanHttpService,
  ElectionsService,
  MetropolArticleCardComponent,
  MetropolBestRecommenderComponent,
  MetropolBlockTitleRowComponent,
  MetropolBlockTitleSidebarComponent,
  MetropolBreakingArticleCardComponent,
  MetropolFinalCountdownComponent,
  MetropolSeveralElementBoxComponent,
  MetropolVotingComponent,
  OpinionLayoutCardMetropolComponent,
  RssBoxWrapperComponent,
} from '../../../../shared';
import { environment } from '../../../../../environments/environment';
import { EnvironmentApiUrl, UtilService } from '@trendency/kesma-core';
import { AsyncPipe, NgIf } from '@angular/common';
import { METROPOL_EXTRACTORS_CONFIG } from '../../extractors/extractor.config';
import { DossierSponsorationHeaderComponent } from '../../../../shared/components/dossier-sponsoration-header/dossier-sponsoration-header.component';
import { VariableSponsoredDidYouKnowWrapperComponent } from '../../../../shared/components/variable-sponsored-did-you-know-wrapper/variable-sponsored-did-you-know-wrapper.component';

@Component({
  selector: 'app-layout',
  templateUrl: './layout.component.html',
  styleUrls: ['./layout.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    KesmaLayoutComponent,
    NgIf,
    AdvertisementAdoceanComponent,
    KoponyegComponent,
    HtmlEmbedComponent,
    RealEstateBazaarSearchBlockComponent,
    RealEstateBazaarBlockComponent,
    RssBoxWrapperComponent,
    ElectionsBoxComponent,
    MetropolBlockTitleRowComponent,
    MetropolBlockTitleSidebarComponent,
    OpinionLayoutCardMetropolComponent,
    MetropolArticleCardComponent,
    MetropolFinalCountdownComponent,
    MetropolSeveralElementBoxComponent,
    MetropolVotingComponent,
    MetropolBreakingArticleCardComponent,
    MetropolBestRecommenderComponent,
    SponsoredBoxComponent,
    DossierSponsorationHeaderComponent,
    AsyncPipe,
    VariableSponsoredDidYouKnowWrapperComponent,
  ],
  providers: [provideLayoutDataExtractors(METROPOL_EXTRACTORS_CONFIG, true)],
})
export class LayoutComponent {
  @Input() adPageType = PAGE_TYPES.all_articles_and_sub_pages;
  @Input() structure: LayoutElementRow[];
  @Input() configuration: LayoutElementContentConfiguration[];
  @Input() layoutType?: LayoutPageType;
  @Input() breakingNews: BreakingNews[] = [];
  @Input() contentComponentsWrapper: TemplateRef<LayoutContentItemWrapperTemplateData>;
  @Input() contentComponentsInnerWrapper: TemplateRef<LayoutContentItemWrapperTemplateData>;
  @Input() blockTitleWrapper: TemplateRef<BlockWrapperTemplateData>;
  @Input() editorFrameSize?: 'desktop' | 'mobile';

  @ViewChild('contentComponents', {
    read: TemplateRef,
    static: false,
  })
  contentComponents: TemplateRef<LayoutContentParams>;

  readonly LayoutPageTypes = LayoutPageType;
  readonly LayoutElementContentType = LayoutElementContentType;

  readonly KoponyegType: KoponyegType = KoponyegType.Light;
  readonly KoponyegDefinitions: KoponyegDefinitions = {
    width: 300,
    height: 100,
  };
  public realEstateData: RealEstateBazaarData[] = [];
  private extractedData: any = {};
  private realEstateDataLoading = false;

  private readonly httpService = inject(CleanHttpService);

  readonly voteCache = this.voteService.voteCache;

  constructor(
    private readonly changeDetector: ChangeDetectorRef,
    public readonly voteService: VoteService,
    public readonly electionsService: ElectionsService,
    public readonly util: UtilService
  ) {}

  get ingatlanbazarUrl(): string {
    if (typeof environment.ingatlanbazarApiUrl === 'string') {
      return environment.ingatlanbazarApiUrl as string;
    }

    const { clientApiUrl, serverApiUrl } = environment.ingatlanbazarApiUrl as EnvironmentApiUrl;
    return this.util.isBrowser() ? clientApiUrl : serverApiUrl;
  }

  getData(type: string, layoutElement: LayoutElement, extractor: any, index?: number): any {
    const id = type + layoutElement.id + index;
    if (!this.extractedData[id]) {
      this.extractedData[id] = index || index == 0 ? extractor[type]?.(layoutElement, index) : extractor[type]?.(layoutElement);
    }

    return this.extractedData[id];
  }

  getSeveralElementBoxTitle(layoutElement: any): string {
    const selectedDossier = layoutElement?.config?.selectedDossiers[0];
    return selectedDossier?.overwriteTitle || selectedDossier?.original?.title || 'Dosszié';
  }

  handleRealEstateInitEvent(): void {
    if (this.util.isBrowser() && !this.realEstateDataLoading && this.realEstateData.length < 1) {
      this.getRealEstateData();
    }
  }

  getRealEstateData(): void {
    this.realEstateDataLoading = true;
    const realEstates: Array<RealEstateBazaarData> = [];
    const apiUrl = this.ingatlanbazarUrl;
    this.httpService
      .get(`${apiUrl}/property-search?` + `property_location=6,1000000004,1000000005,1000000006,1000000007&amp;;property_newbuildonly=on&amp;property__2=3_2`)
      .subscribe((data: RealEstateBazaarBackendResponse) => {
        data?.hits?.forEach((realEstate: RealEstateBazaarApiData) => {
          realEstates.push(mapRealEstateApiDataToRealEstateData(realEstate));
        });
        this.realEstateData = realEstates;
        this.realEstateDataLoading = false;
        this.changeDetector.detectChanges();
      });
  }

  onVotingSubmit($event: string, voteData: VoteDataWithAnswer): void {
    this.voteService.onVotingSubmit($event, voteData).subscribe(() => this.changeDetector.detectChanges());
  }

  clearExtractedData(): void {
    this.extractedData = {};
  }
}
