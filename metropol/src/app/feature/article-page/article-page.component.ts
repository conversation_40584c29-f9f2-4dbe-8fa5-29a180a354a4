import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>f, NgSwitch, NgSwitchCase, NgTemplateOutlet, SlicePipe } from '@angular/common';
import { AfterViewInit, ChangeDetectorRef, Component, ElementRef, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { DomSanitizer, SafeHtml, SafeResourceUrl } from '@angular/platform-browser';
import { ActivatedRoute, NavigationEnd, Router, RouterLink } from '@angular/router';
import { FormatDatePipe, IMetaData, SchemaOrgService, SeoService, StorageService, UtilService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  AdvertsMeta,
  AnalyticsService,
  Article,
  ArticleAdvertisements,
  ArticleBody,
  ArticleBodyType,
  ArticleCard,
  ArticleFileLinkDirective,
  ArticleResolverData,
  ArticleRouteParams,
  ArticleVideoComponent,
  AutoArticleBodyAdService,
  BasicDossier,
  ElectionsBoxComponent,
  ElectionsBoxStyle,
  GalleryData,
  GalleryElementData,
  getStructuredDataForArticle,
  mapBackendArticleDataToArticleCard,
  NativtereloComponent,
  PAGE_TYPES,
  SearchBotService,
  SponsoredTag,
  ThumbnailImage,
  VoteDataWithAnswer,
  VoteService,
} from '@trendency/kesma-ui';
import { combineLatest, forkJoin, Observable, Subject, Subscription } from 'rxjs';
import { map, switchMap, takeUntil } from 'rxjs/operators';
import { defaultMetaInfo } from 'src/app/shared/constants/meta.consts';
import { GalleryService } from 'src/app/shared/services/gallery.service';
import { environment } from 'src/environments/environment';
import {
  ArticleCardType,
  ElectionsService,
  MetropolAdultComponent,
  MetropolArticleCardComponent,
  MetropolArticleHeaderComponent,
  MetropolArticleNewsletterBoxComponent,
  MetropolBlockTitleRowComponent,
  MetropolGalleryCardComponent,
  MetropolGalleryCardType,
  MetropolQuizComponent,
  MetropolVotingComponent,
  MetropolWysiwygBoxComponent,
  OpinionLayoutCardMetropolComponent,
  PersonalizedRecommendationService,
  PreviewAsArticleCardPipe,
  ScriptLoaderService,
  SponsoredTagBoxComponent,
  UrlService,
} from '../../shared';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { ArticleDossierRecommenderComponent } from './article-dossier-recommender/article-dossier-recommender.component';

@Component({
  selector: 'app-article-page',
  templateUrl: './article-page.component.html',
  styleUrls: ['./article-page.component.scss'],
  providers: [AutoArticleBodyAdService, FormatDatePipe],
  imports: [
    NgIf,
    NgTemplateOutlet,
    NgFor,
    ArticleDossierRecommenderComponent,
    AdvertisementAdoceanComponent,
    NativtereloComponent,
    ElectionsBoxComponent,
    SidebarComponent,
    NgSwitch,
    NgSwitchCase,
    ArticleFileLinkDirective,
    ArticleVideoComponent,
    RouterLink,
    SlicePipe,
    PreviewAsArticleCardPipe,
    MetropolAdultComponent,
    MetropolArticleHeaderComponent,
    OpinionLayoutCardMetropolComponent,
    MetropolBlockTitleRowComponent,
    MetropolArticleCardComponent,
    MetropolArticleNewsletterBoxComponent,
    MetropolWysiwygBoxComponent,
    MetropolVotingComponent,
    MetropolGalleryCardComponent,
    MetropolQuizComponent,
    SponsoredTagBoxComponent,
    AsyncPipe,
  ],
})
export class ArticlePageComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('dataTrigger', { static: false }) readonly dataTrigger: ElementRef<HTMLDivElement>;
  @ViewChild('externalRecommendationsBlock', { static: false }) readonly externalRecommendationsBlock: ElementRef<HTMLDivElement>;

  readonly ArticleCardType = ArticleCardType;
  readonly ArticleBodyType = ArticleBodyType;
  readonly MetropolGalleryCardType = MetropolGalleryCardType;

  article: Article;
  articleSlug = '';
  categorySlug = '';
  lowPriorityArticles: ArticleCard[] = [];
  highPriorityArticles: ArticleCard[] = [];
  lastThreeDaysMostReadArticles: ArticleCard[] = [];
  recommendedArticles: ArticleCard[] = [];
  externalRecommendation: ArticleCard[] = [];
  videos: ArticleCard[] = [];
  isUserAdultChoice: boolean;
  isSearchBot = false;
  metaData: IMetaData;
  galleries: Record<string, GalleryData> = {};
  dossier?: BasicDossier;
  fbShareUrl: string;
  twitterShareUrl: string;
  baseUrl: string;
  mailToLink: string;
  routerEventSub: Subscription;
  moreArticlesByAuthor: ArticleCard[];
  categoryArticles: ArticleCard[];
  advertisementMeta?: AdvertsMeta;
  public readonly MIDDLE_TOP_CARD = 1;
  adPageType = PAGE_TYPES.all_articles_and_sub_pages;
  adverts?: AdvertisementsByMedium;
  mobile_pr_cikkfix?: Advertisement[];
  mmeTereloUrl: SafeResourceUrl = this.sanitizer.bypassSecurityTrustResourceUrl(
    'https://cdn.nwmgroups.hu/s/partners/mme/html/mme-terelo.html?utm_source=metropol'
  );
  embedPrAdvert?: SafeHtml;
  interrupter?: ArticleAdvertisements;
  ElectionsBoxStyle = ElectionsBoxStyle;
  tereloUrl: string =
    'https://terelo.mediaworks.hu/nativterelo/nativterelo.html?utmSource=metropol.hu' + '&traffickingPlatforms=Metropol%20Nat%C3%ADv' + '&domain=Metropol';
  private url: string;
  private cannonicalUrl: string;
  private readonly unsubscribe$: Subject<boolean> = new Subject();
  isExceptionAdvertEnabled: boolean;
  sponsoredTag?: SponsoredTag;

  readonly voteCache = this.voteService.voteCache;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly seo: SeoService,
    //private readonly embedding: EmbeddingService,
    private readonly utilsService: UtilService,
    private readonly storage: StorageService,
    private readonly adStoreAdo: AdvertisementAdoceanStoreService,
    private readonly formatDate: FormatDatePipe,
    private readonly galleryService: GalleryService,
    private readonly schemaService: SchemaOrgService,
    private readonly analyticsService: AnalyticsService,
    public readonly voteService: VoteService,
    private readonly cd: ChangeDetectorRef,
    private readonly scriptLoaderService: ScriptLoaderService,
    private readonly sanitizer: DomSanitizer,
    private readonly urlService: UrlService,
    private readonly searchBotService: SearchBotService,
    private readonly personalizedRecommendationService: PersonalizedRecommendationService,
    private readonly autoArticleBodyAd: AutoArticleBodyAdService,
    public readonly electionsService: ElectionsService
  ) {}

  get galleriesData(): Record<string, GalleryData> {
    return this.galleries as unknown as Record<string, GalleryData>;
  }

  ngOnInit(): void {
    (this.route.data as Observable<{ data: ArticleResolverData }>)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(({ data: { article, recommendations, articleSlug, categorySlug, url } }: { data: ArticleResolverData }) => {
        let body = article?.data?.body;
        this.autoArticleBodyAd.init(body);
        body = this.autoArticleBodyAd.autoAd();

        this.article = { ...article?.data, body: this.#prepareArticleBody(body) };
        this.sponsoredTag = article?.meta?.['sponsoredTag'];
        this.articleSlug = articleSlug;
        if (this.article.body?.length) {
          const articleBodyModifiedData = this.setLazyLoadedArticleBodyImages(this.article.body);
          Object.assign(this.article.body, [...articleBodyModifiedData]);
        }
        if (article?.data?.isOpinion) {
          this.moreArticlesByAuthor = (article as any).articles.data.filter((res: any) => res.id !== this.article.id).map(mapBackendArticleDataToArticleCard);
        }
        this.adStoreAdo.getAdvertisementMeta(this.article?.tags, this.article.isAdultsOnly);

        this.categorySlug = categorySlug ?? this.article?.primaryColumn?.slug;
        this.adPageType = 'column_' + (this.article.primaryColumn?.parent?.slug || this.categorySlug);

        this.embedPrAdvert = this.sanitizer.bypassSecurityTrustHtml(this.article?.embedPrAdvert ?? '');

        this.url = url ?? '';
        this.cannonicalUrl = this.article.seo?.seoCanonicalUrl || this.article?.canonicalUrl || `${this.seo.hostUrl}/${this.url}`;

        this.dossier = this.article?.dossier;
        this.isSearchBot = this.searchBotService.isSearchBot();
        if (this.article.isAdultsOnly) {
          this.searchBotService.insertAdultMetaTag();
        } else {
          this.searchBotService.removeAdultMetaTag();
        }

        this.isUserAdultChoice = (this.storage.getSessionStorageData('isAdultChoice', false) ?? false) && this.article.isAdultsOnly;

        this.adStoreAdo.setIsAdultPage(this.isUserAdultChoice);

        const { lowPriorityArticles, highPriorityArticles, lastThreeDaysMostReadArticles, externalRecommendation, videos, categoryArticles } =
          recommendations?.data ?? {
            externalRecommendation: [],
            videos: [],
            lowPriorityArticles: [],
            highPriorityArticles: [],
            lastThreeDaysMostReadArticles: [],
            categoryArticles: [],
          };
        this.lastThreeDaysMostReadArticles = lastThreeDaysMostReadArticles;
        this.lowPriorityArticles = lowPriorityArticles;
        this.highPriorityArticles = highPriorityArticles;
        this.externalRecommendation = externalRecommendation;
        this.getPersonalizedRecommendations();
        this.recommendedArticles = [...this.highPriorityArticles, ...this.lowPriorityArticles];
        this.recommendedArticles = this.recommendedArticles.filter((article) => article.id !== this.article.id);
        this.recommendedArticles.length = 6;
        this.categoryArticles =
          categoryArticles && categoryArticles.length > 0
            ? categoryArticles
                .filter((article: ArticleCard) => article.id !== this.article.id)
                .map((article: ArticleCard) => ({
                  ...article,
                  thumbnail: { url: article?.thumbnail ? article?.thumbnail : '', alt: article.title } as ThumbnailImage,
                }))
            : [];

        this.videos = videos;
        this.seo.updateCanonicalUrl(this.cannonicalUrl, {
          addHostUrl: false,
          skipSeoMetaCheck: true,
        });
        // this.loadEmbedMediaContent();
        this.loadEmbeddedGalleries();
        if (this.article) {
          this.schemaService.removeStructuredData();
          this.schemaService.insertSchema(getStructuredDataForArticle(this.article, this.seo.currentUrl, environment?.siteUrl ?? ''));
        }
        this.setMetaData();
        this.setupShareLinks();
        this.voteService.initArticleVotes(this.article);
        this.adStoreAdo.setArticleParentCategory(this.adPageType);

        setTimeout(() => {
          this.analyticsService.sendPageView({
            pageCategory: this.categorySlug,
            customDim2: this.article?.topicLevel1,
            customDim1: this.article?.aniCode,
            title: this.article.title,
            articleSource: this.article.articleSource ? this.article.articleSource : 'no source',
            publishDate: this.formatDate.transform(this.article.publishDate as Date, 'dateTime'),
            lastUpdatedDate: this.formatDate.transform((this.article.lastUpdated ? this.article.lastUpdated : this.article.publishDate) as Date, 'dateTime'),
          });
        }, 0);
      });

    (
      combineLatest([this.route.data as Observable<{ data: ArticleResolverData }>, this.adStoreAdo.isAdult.asObservable()]) as any as Observable<
        [{ data: ArticleResolverData }, boolean]
      >
    )
      .pipe(takeUntil(this.unsubscribe$))
      .pipe(
        map<[{ data: ArticleResolverData }, boolean], boolean | undefined>(
          ([
            {
              data: { article },
            },
          ]) => {
            this.isExceptionAdvertEnabled = article?.data?.isExceptionAdvertEnabled;

            return article?.data?.withoutAds;
          }
        )
      )
      .pipe(
        switchMap((withoutAds) => {
          this.resetAds();
          withoutAds ? this.adStoreAdo.disableAds() : this.adStoreAdo.enableAds();
          return this.adStoreAdo.advertisemenets$;
        })
      )
      .subscribe((ads): void => {
        this.adverts = this.adStoreAdo.separateAdsByMedium(ads, this.adPageType);
        const prAndInterrupter = this.adStoreAdo.separateAdsByMedium(ads);
        this.interrupter = prAndInterrupter;

        this.mobile_pr_cikkfix = [
          prAndInterrupter.mobile.prcikkfix_1,
          prAndInterrupter.mobile.prcikkfix_2,
          prAndInterrupter.mobile.prcikkfix_3,
          prAndInterrupter.mobile.prcikkfix_4,
          prAndInterrupter.mobile.prcikkfix_5,
        ];

        this.adStoreAdo.onArticleLoaded();
        this.cd.detectChanges();
      });
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
    this.adStoreAdo.onArticleDestroy();
    this.adStoreAdo.enableAds(); // Ha letiltottuk a hirdetéseket egy cikkben és más oldalra navigálunk, újra kell engedélyezni
  }

  resetAds(): void {
    this.adverts = undefined;
    this.interrupter = undefined;
    this.mobile_pr_cikkfix = undefined;
    this.cd.detectChanges();
  }

  ngAfterViewInit(): void {
    //this.embedding.loadEmbedMedia();

    if (this.utilsService.isBrowser()) {
      this.scriptLoaderService.loadScript({ src: '//widget.block4d.com/js/block/widget.js' });

      setTimeout(() => {
        this.route.data.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {
          if ('IntersectionObserver' in window) {
            this.observeExternalRecommendations();
            this.observeArticleEnd();
          }
        });
      }, 1000);
    }
  }

  onIsUserAdultChoose(isUserAdult: boolean): void {
    this.isUserAdultChoice = isUserAdult;
    this.adStoreAdo.setIsAdultPage(isUserAdult);
    // this.initDataLayer();
  }

  setupShareLinks(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }

    this.mailToLink = 'mailto:?subject=' + this.metaData?.ogTitle + '&body=' + this.metaData?.ogTitle + ': ' + this.seo.currentUrl;
    this.baseUrl = this.seo.hostUrl;
    this.fbShareUrl = `https://www.facebook.com/sharer/sharer.php?u=${this.baseUrl}${this.router.url}`;
    this.twitterShareUrl = `https://twitter.com/intent/tweet?url=${this.baseUrl}${this.router.url}`;

    this.routerEventSub = this.router.events.pipe(takeUntil(this.unsubscribe$)).subscribe((event: any) => {
      if (event instanceof NavigationEnd) {
        this.fbShareUrl = `https://www.facebook.com/sharer/sharer.php?u=${this.baseUrl}${event.url}`;
        this.twitterShareUrl = `https://twitter.com/intent/tweet?url=${this.baseUrl}${event.url}`;
        this.mailToLink = 'mailto:?subject=' + this.metaData.ogTitle + '&body=' + this.metaData.ogTitle + ': ' + this.seo.currentUrl;
      }
    });
  }

  onVotingSubmit($event: string, voteData: VoteDataWithAnswer): void {
    this.voteService.onVotingSubmit($event, voteData).subscribe(({ data }) => {
      voteData.data = data;
      this.cd.markForCheck();
    });
  }

  setPreviousUrl(): void {
    this.urlService.setPreviousUrl(this.router.url);
  }

  getPersonalizedRecommendations(): void {
    if (this.utilsService.isBrowser()) {
      this.personalizedRecommendationService
        .getPersonalizedRecommendations()
        .pipe(takeUntil(this.unsubscribe$))
        .subscribe((data: ArticleCard[]): void => {
          this.externalRecommendation = data;
          this.cd.detectChanges();
        });
    }
  }

  private setMetaData(): void {
    const { thumbnail, publicAuthor, publishDate, alternativeTitle, metaThumbnail } = this.article || {};
    if (!this.article) {
      return;
    }

    const title = this.article.seo?.seoTitle || this.article.title;
    const finalTitle = alternativeTitle && alternativeTitle.length > 0 ? alternativeTitle : title;
    const finalOgTitle = alternativeTitle && alternativeTitle.length > 0 ? alternativeTitle : this.article.title;
    this.metaData = {
      ...defaultMetaInfo,
      title: finalTitle,
      description: this.article.seo?.seoDescription || this.article.excerpt || this.article.lead || defaultMetaInfo.description,
      robots: this.getRobotsMeta(),
      ogTitle: finalOgTitle,
      ogImage: metaThumbnail || thumbnail,
      ogType: 'article',
      articleAuthor: publicAuthor,
      articlePublishedTime: publishDate?.toISOString(),
    };

    this.seo.setMetaData(this.metaData);
  }

  private setLazyLoadedArticleBodyImages(articleBody: ArticleBody[]): ArticleBody[] {
    for (let i = 0; i < articleBody.length; i++) {
      const detailsValue = articleBody[i]?.details?.[0]?.value;
      if (articleBody[i]?.type === 'Basic.Wysiwyg.Wysiwyg' && detailsValue?.includes('<img') && !detailsValue?.includes('loading="lazy"')) {
        const value = detailsValue.replaceAll('<img', '<img loading="lazy"');
        Object.assign(articleBody[i].details[0], { value });
      }
    }
    return articleBody;
  }

  private getRobotsMeta(): string {
    if (this.route.snapshot.params['previewHash']?.length) {
      return 'noindex, nofollow';
    }

    if (this.article.seo?.seoRobotsMeta) {
      return this.article.seo?.seoRobotsMeta;
    }
    if (this.article.robotsTag && this.article.robotsTag !== 'index, follow') {
      //By default BE sends 'index, follow' for the columns seo Robots value, but we want to also have max-image-preview:large
      return this.article.robotsTag;
    }
    return 'index, follow, max-image-preview:large';
  }

  private loadEmbeddedGalleries(): void {
    const gallerySubs = (((this.article?.body as any) ?? []) as GalleryElementData[])
      .filter(({ type }) => type === ArticleBodyType.Gallery)
      .filter((bodyElem) => !!bodyElem.details[0].value)
      .map((bodyElem: GalleryElementData) => this.galleryService.getGalleryDetails(bodyElem.details[0]?.value?.slug));

    forkJoin(gallerySubs)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((galleries) => {
        galleries.forEach((gallery) => {
          const galleryData: GalleryData = {
            ...gallery,
            highlightedImageUrl: gallery.highlightedImage.url,
          } as any as GalleryData;
          this.galleries[gallery.id] = galleryData;
        });
      });
  }

  private observeArticleEnd(): void {
    if (!this.dataTrigger?.nativeElement) {
      return;
    }

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(({ isIntersecting }) => {
        if (isIntersecting) {
          this.sendEcommerceEvent();
          observer.unobserve(this.dataTrigger.nativeElement);
        }
      });
    });
    observer.observe(this.dataTrigger.nativeElement);
  }

  private sendEcommerceEvent(): void {
    const routeParams: ArticleRouteParams = this.route.snapshot.params as ArticleRouteParams;
    this.analyticsService.sendEcommerceEvent({
      id: `T${this.article.id}`,
      title: this.article.title,
      articleSlug: routeParams.articleSlug ? routeParams.articleSlug : 'cikk-elonezet',
      category: this.article.columnTitle ?? '',
      articleSource: this.article.articleSource ? this.article.articleSource : 'no source',
      publishDate: this.formatDate.transform(this.article.publishDate as Date, 'dateTime'),
      lastUpdatedDate: this.formatDate.transform((this.article.lastUpdated ? this.article.lastUpdated : this.article.publishDate) as Date, 'dateTime'),
    });
  }

  #prepareArticleBody(body: ArticleBody[]): ArticleBody[] {
    let advertIndex = 1;
    return body.map((bodyPart: ArticleBody) => ({
      ...bodyPart,
      ...(bodyPart.type === ArticleBodyType.Advert && {
        adverts: {
          mobile: `mobilinterrupter_${advertIndex}`,
          desktop: `desktopinterrupter_${advertIndex++}`,
        },
      }),
    }));
  }

  private observeExternalRecommendations(): void {
    if (!this.externalRecommendationsBlock?.nativeElement) {
      return;
    }

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(({ isIntersecting }) => {
        if (isIntersecting && this.externalRecommendation) {
          this.personalizedRecommendationService.sendPersonalizedRecommendationAv(this.externalRecommendation).subscribe();
          observer.unobserve(this.externalRecommendationsBlock.nativeElement);
        }
      });
    });
    observer.observe(this.externalRecommendationsBlock.nativeElement);
  }
}
