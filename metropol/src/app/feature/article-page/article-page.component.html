<ng-container *ngIf="!isUserAdultChoice && article?.isAdultsOnly && !isSearchBot; else adultContent" class="article-page">
  <metropol-adult (isUserAdult)="onIsUserAdultChoose($event)"></metropol-adult>
</ng-container>

<ng-template #adultContent>
  <section *ngIf="article" class="article-page">
    <div [class.with-aside]="!article.isAlternativeView" class="wrapper">
      <div class="left-column">
        <metropol-article-header
          [data]="article"
          [desktopAdvertAfterLead]="adverts?.desktop?.roadblock_1"
          [embedPrAdvert]="embedPrAdvert"
          [facebookLink]="fbShareUrl"
          [mailToLink]="mailToLink"
          [mobileAdvertAfterLead]="adverts?.mobile?.mobilrectangle_1"
          [twitterLink]="twitterShareUrl"
          [isExceptionAdvertEnabled]="isExceptionAdvertEnabled"
        ></metropol-article-header>

        <!-- body -->
        <ng-container [ngTemplateOutletContext]="{ body: article.body }" [ngTemplateOutlet]="bodyContent"></ng-container>

        <div *ngIf="article?.isOpinion && moreArticlesByAuthor?.length">
          <div class="more-articles-by-author-title">Szerző további cikkei:</div>
          <div class="more-articles-by-author">
            <metropol-opinion-layout-card
              *ngFor="let opinion of moreArticlesByAuthor | slice: 0 : 3; let i = index"
              [data]="opinion"
              class="opinion-card"
            ></metropol-opinion-layout-card>
          </div>
        </div>

        <div #dataTrigger *ngIf="article"></div>

        @if (sponsoredTag) {
          <app-sponsored-tag-box [sponsoredTag]="sponsoredTag" [excludedSlug]="article?.slug || ''" />
        }

        <app-article-dossier-recommender
          *ngIf="dossier && !sponsoredTag"
          [excludedArticleSlug]="articleSlug"
          [subsequentDossier]="dossier"
          class="dossier-recommender-wrapper"
        ></app-article-dossier-recommender>

        <!-- <metropol-brownbox [data]="categoryArticles | slice: 0:4"></metropol-brownbox> -->
        <div *ngIf="adverts?.desktop?.roadblock_2 as ad" class="desktop">
          <hr />
          <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
          <hr />
        </div>

        <div *ngIf="adverts?.mobile?.mobilrectangle_2 as ad" class="mobile">
          <hr />
          <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
          <hr />
        </div>

        <div *ngIf="adverts?.desktop?.roadblock_3 as ad" class="desktop">
          <hr />
          <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
          <hr />
        </div>

        <div *ngIf="adverts?.mobile?.mobilrectangle_3 as ad" class="mobile">
          <hr />
          <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
          <hr />
        </div>

        <metropol-block-title-row [data]="{ text: 'Top hírek' }" class="recommendation-title"></metropol-block-title-row>
        <div class="recommendation-block">
          <metropol-article-card
            *ngFor="let article of recommendedArticles | slice: 0 : 3; let i = index"
            [data]="article"
            [styleID]="ArticleCardType.SmallImgTagTitleLead"
          ></metropol-article-card>
        </div>

        <div *ngIf="adverts?.desktop?.roadblock_4 as ad" class="desktop">
          <hr />
          <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
          <hr />
        </div>

        <div *ngIf="adverts?.mobile?.mobilrectangle_4 as ad" class="mobile">
          <hr />
          <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
          <hr />
        </div>

        <kesma-nativterelo [tereloUrl]="tereloUrl"></kesma-nativterelo>

        <div #externalRecommendationsBlock class="recommendation-block recommendation-block-external">
          <ng-container *ngFor="let article of externalRecommendation; let i = index">
            <metropol-article-card [data]="article" [styleID]="ArticleCardType.ExternalRecommendation"></metropol-article-card>

            <div *ngIf="i === 3" class="full-row desktop">
              <kesma-advertisement-adocean *ngIf="adverts?.desktop?.roadblock_ottboxextra as ad" [ad]="ad" class="desktop"></kesma-advertisement-adocean>
            </div>
            <div *ngIf="i === 5" class="full-row mobile">
              <kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobilrectangle_ottboxextra as ad" [ad]="ad" class="mobile"></kesma-advertisement-adocean>
            </div>
          </ng-container>
        </div>

        <ng-container *ngIf="electionsService.isElections2024Enabled()">
          <kesma-elections-box
            [desktopWidth]="10"
            [link]="electionsService.getElections2024Link()"
            [styleID]="ElectionsBoxStyle.DIVERTER"
          ></kesma-elections-box>
        </ng-container>

        <!-- External ads -->
        <div class="blck4d" data-widget-id="9797982"></div>

        <metropol-article-newsletter-box
          [formUrl]="'hirlevel-feliratkozas'"
          [mobileHeader]="true"
          [signupText]="
            'Nem akar lemaradni a Metropol cikkeiről? Adja meg a nevét és az e-mail címét, és mi hetente három alkalommal elküldjük Önnek a legjobb írásokat!'
          "
        ></metropol-article-newsletter-box>
      </div>
      <aside>
        <app-sidebar
          *ngIf="articleSlug && categorySlug && !article.isAlternativeView"
          [adPageType]="adPageType"
          [articleId]="article.id"
          [articleSlug]="articleSlug"
          [categorySlug]="categorySlug"
        ></app-sidebar>
      </aside>
    </div>

    <div *ngIf="mobile_pr_cikkfix?.length" [style]="{ display: 'flex', width: '100%', 'justify-content': 'center', 'flex-direction': 'column' }">
      <ng-container *ngFor="let pr_cik_fix of mobile_pr_cikkfix">
        <kesma-advertisement-adocean *ngIf="pr_cik_fix as ad" [ad]="ad" [style]="{ 'margin-bottom': '20px' }"> </kesma-advertisement-adocean>
      </ng-container>
    </div>
  </section>
</ng-template>

<ng-template #bodyContent let-body="body">
  <ng-container *ngFor="let element of body">
    <ng-container [ngSwitch]="element.type">
      <ng-container *ngSwitchCase="ArticleBodyType.Wysywyg">
        <ng-container *ngFor="let wysiwygDetail of element?.details">
          <metropol-wysiwyg-box [html]="wysiwygDetail?.value" trArticleFileLink></metropol-wysiwyg-box>
        </ng-container>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.Advert">
        <kesma-advertisement-adocean *ngIf="interrupter?.mobile?.[element.adverts.mobile] as ad" [ad]="ad" class="mobile"></kesma-advertisement-adocean>
        <kesma-advertisement-adocean *ngIf="interrupter?.desktop?.[element.adverts.desktop] as ad" [ad]="ad" class="desktop"> </kesma-advertisement-adocean>
      </ng-container>

      <div *ngSwitchCase="ArticleBodyType.Voting" class="voting-block">
        @if (voteCache[element?.details?.[0]?.value?.id ?? ''] | async; as voteData) {
          <metropol-voting (vote)="onVotingSubmit($event, voteData)" [data]="voteData.data" [voteId]="voteData.votedId" />
        }
      </div>

      <ng-container *ngSwitchCase="ArticleBodyType.SubsequentDossier">
        <app-article-dossier-recommender
          [excludedArticleSlug]="dossier ? articleSlug : ''"
          [subsequentDossier]="element?.details[0]?.value"
          class="dossier-recommender-wrapper"
        ></app-article-dossier-recommender>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.Quiz">
        <metropol-quiz [data]="element?.details[0]?.value"></metropol-quiz>
      </ng-container>

      <div *ngSwitchCase="ArticleBodyType.MediaVideo" class="block-video">
        <kesma-article-video [data]="element?.details[0]?.value"></kesma-article-video>
      </div>

      <div *ngSwitchCase="ArticleBodyType.Gallery" class="block-gallery">
        <ng-container *ngIf="galleries[element?.details[0]?.value?.id]">
          <metropol-gallery-card
            (click)="setPreviousUrl()"
            [data]="galleriesData[element?.details[0]?.value?.id]"
            [isInsideAdultArticleBody]="article?.isAdultsOnly ?? false"
            [routerLink]="['/', 'galeria', galleries[element?.details[0]?.value?.id].slug, 1]"
            [styleID]="MetropolGalleryCardType.Small"
          ></metropol-gallery-card>
        </ng-container>
      </div>

      <div *ngSwitchCase="ArticleBodyType.Article" class="block-recommendation">
        <metropol-article-card [data]="element?.details[0]?.value | previewAsArticleCard" [styleID]="ArticleCardType.FeaturedImgTagTitle">
        </metropol-article-card>
      </div>
    </ng-container>
  </ng-container>
</ng-template>
