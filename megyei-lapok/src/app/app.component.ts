import { APP_BASE_HREF, DOCUMENT } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, Inject, inject, OnInit, Optional } from '@angular/core';
import { ChildActivationEnd, NavigationEnd, Router, RouterOutlet } from '@angular/router';
import { GoogleTagManagerService } from 'angular-google-tag-manager';
import { buffer, filter, map, tap } from 'rxjs/operators';
import { SeoService, UtilService } from '@trendency/kesma-core';
import { environment } from '../environments/environment';
import { defaultMetaInfo, JsonLDService, MegyeiUtils, PortalConfigService, ThemeService } from './shared';
import { AnalyticsService } from '@trendency/kesma-ui';
import { Observable } from 'rxjs';

// eslint-disable-next-line @typescript-eslint/naming-convention
declare let pp_gemius_hit: (id: string, page: string) => void;

@Component({
  selector: 'app-root',
  template: ` <router-outlet></router-outlet> `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterOutlet],
})
export class AppComponent implements OnInit, AfterViewInit {
  private readonly document: Document = inject(DOCUMENT);
  private isFirstNavigation = true;

  constructor(
    private readonly themeService: ThemeService,
    private readonly seoService: SeoService,
    private readonly router: Router,
    private readonly portalConfig: PortalConfigService,
    private readonly schema: JsonLDService,
    private readonly utilsService: UtilService,
    private readonly analyticsService: AnalyticsService,
    private readonly gtmService: GoogleTagManagerService,
    private readonly megyeiUtils: MegyeiUtils,
    @Optional() @Inject(APP_BASE_HREF) private readonly baseHref: string
  ) {}

  ngOnInit(): void {
    const { primaryColor, secondaryColor, linkColor, activeColor, tagColor, tagTextColor, logoWidth } = this.portalConfig;

    if (this.utilsService.isBrowser()) {
      this.themeService.setTheme(primaryColor, secondaryColor, linkColor, activeColor, tagColor, tagTextColor, logoWidth);

      // mandatory here in init, as it will not send the initial data on first load
      this.setupAnalyticsTracking();
      setTimeout(() => {
        this.gtmService.addGtmToDom();
      });
    }
    this.seoService.setMetaData(defaultMetaInfo(this.portalConfig), { skipSeoMetaCheck: true });
    this.applySchemaOrg();
  }

  ngAfterViewInit(): void {
    this.setBaseUrl();
    if (this.utilsService.isBrowser()) {
      // this.loadGoogleAnalyticsScript();
      setTimeout(() => {
        this.googleAds();
        // TODO: Later we need it
        // this.loadSurvey();
      });
      this.erdonExtraScript();
      this.gemiusKemmaBeolExtraScript();
      this.appendHeadRssLink();

      this.seoService.updateCanonicalUrl(this.document.location.href, { addHostUrl: false, skipSeoMetaCheck: true });
    }
  }

  private applySchemaOrg(): void {
    this.schema.removeStructuredData();
    this.schema.insertSchema();
  }

  private setupAnalyticsTracking(): void {
    const { gemiusId } = this.portalConfig;

    // Navigation end used to trigger gemius and gtag
    const navigationEnd$: Observable<NavigationEnd> = (this.router.events.pipe(filter((e) => e instanceof NavigationEnd)) as Observable<NavigationEnd>).pipe(
      tap((event: NavigationEnd) => {
        if (!this.isFirstNavigation) {
          pp_gemius_hit(gemiusId, `page=${event.urlAfterRedirects}`);
        }

        setTimeout(() => {
          this.isFirstNavigation = false;
        }, 0);
      })
    );

    // Child activationEnd to get the leaf route data in order to see if we send pageViews there.
    this.router.events
      .pipe(
        filter((e) => e instanceof ChildActivationEnd),
        // ChildActivationEnd triggers for every path activation, we need only the leaf so after navigation end
        // we get all of the childActivationEnd events and we only need the first one.
        buffer(navigationEnd$),
        map(([leafNode]) => (leafNode as ChildActivationEnd).snapshot.firstChild?.data),
        map((data) => data?.['omitGlobalPageView'])
      )
      .subscribe((shouldNotSendGlobalAnalytics: boolean) => {
        console.log('Should send global analytics: ', !shouldNotSendGlobalAnalytics);
        if (shouldNotSendGlobalAnalytics) {
          return;
        }
        this.analyticsService.sendPageView();
      });
  }

  private erdonExtraScript(): void {
    const { portalName } = this.portalConfig;
    if (portalName !== 'ERDON') {
      return;
    }
    this.megyeiUtils.createScriptWithSrc(`https://securepubads.g.doubleclick.net/tag/js/gpt.js`, true);
  }

  private gemiusKemmaBeolExtraScript(): void {
    const { portalName } = this.portalConfig;

    setTimeout((): void => {
      this.megyeiUtils.createScriptWithSrc(`https://hu.hit.gemius.pl/hmapxy.js`, true);
    });

    if (
      portalName === 'BEOL' ||
      portalName === 'KEMMA' ||
      portalName === 'HEOL' ||
      portalName === 'BAON' ||
      portalName === 'TEOL' ||
      portalName === 'BAMA' ||
      portalName === 'SZOLJON'
    ) {
      this.megyeiUtils.createScriptWithInnerText(
        `
        var ghmxy_type = 'absolute';var ghmxy_align = 'center';
        var ghmxy_identifier = 'zU1LoiNTUJQbvAtOL0DYGOVD7DndomxTf7KV4xFuT8X.C7';
        var ghmxy_hitcollector = 'hu.hit.gemius.pl';
    `,
        false,
        true
      );
    }
  }

  private googleAds(): void {
    const { portalName } = this.portalConfig;

    if (portalName === 'ERDON') {
      return;
    }
    this.megyeiUtils.createScriptWithSrc(`https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-8559195417632426`);
  }

  private appendHeadRssLink(): void {
    const { siteUrl, portalName } = this.portalConfig;

    const portal = portalName.toLowerCase();

    if (portalName === 'ERDON') {
      this.megyeiUtils.createHeadLink('https://www.erdon.ro', portal, 'application/rss+xml', 'alternate');
      return;
    }

    this.megyeiUtils.createHeadLink(siteUrl, portal, 'application/rss+xml', 'alternate');
  }

  private setBaseUrl(): void {
    let base: HTMLBaseElement = this.document.getElementsByTagName('base')[0];
    if (!base) {
      base = this.document.createElement('base');
      this.document.getElementsByTagName('head')[0].appendChild(base);
    }
    const isLocal = this.document.location.host.match(/^local/) || this.document.location.hostname.match(/local$/);
    const urlFallback = this.portalConfig.siteUrl ?? environment.siteUrl ?? this.baseHref ?? '/';
    base.setAttribute('href', isLocal ? '/' : urlFallback);
  }
}
