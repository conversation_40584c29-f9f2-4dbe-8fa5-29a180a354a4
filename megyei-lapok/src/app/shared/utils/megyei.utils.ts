import { inject, Injectable } from '@angular/core';
import { DOCUMENT } from '@angular/common';

@Injectable({
  providedIn: 'root',
})
export class MegyeiUtils {
  private readonly document: Document = inject(DOCUMENT);

  createScriptWithSrc(src: string, async = true, defer = true): void {
    const script = this.document.createElement('script');
    script.async = async;
    script.defer = defer;
    script.src = src;

    this.document.head.appendChild(script);
  }

  createScriptWithInnerText(innerText: string, async = true, defer = true): void {
    const script = this.document.createElement('script');
    script.async = async;
    script.defer = defer;
    script.innerHTML = innerText;

    this.document.head.appendChild(script);
  }

  createHeadLink(siteUrl: string, portal: string, type: string, rel: string): void {
    const link = this.document.createElement('link');
    link.type = type;
    link.rel = rel;
    link.href = `${siteUrl}/publicapi/hu/rss/${portal}/articles`;

    this.document.head.appendChild(link);
  }

  capitalizeString(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }
}
