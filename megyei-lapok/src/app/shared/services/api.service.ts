import { HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import { IHttpOptions, ReqService } from '@trendency/kesma-core';
import { AgroResponse, BackendAstrologyResponse, InitResponse, LayoutServiceResponse, MenuTreeResponse } from '../definitions';
import {
  ApiListResult,
  ApiResponseMetaList,
  ApiResult,
  ArticleCard,
  ArticleResponse,
  BackendAuthorData,
  buildPhpArrayParam,
  LayoutApiData,
  PortfolioResponse,
  SearchQuery,
  SecretDaysCalendar,
  CalendarDay,
  VariableDidYouKnowBox,
} from '@trendency/kesma-ui';
import { backendArticlesSearchResultsToArticleSearchResArticles } from '../../feature/article/article.utils';
import { AuthorData } from '../../feature/authors/api/author.definitions';
import { AdItem, AdsByCategoryResponse, CategoryItem } from '../../feature/ads/ads.definitions';
import { GriefItem, GriefResponse } from '../../feature/grief/grief.definitions';
import { ArticleSearchResult } from '../../feature/article/article.definitions';

@Injectable({
  providedIn: 'root',
})
export class ApiService {
  constructor(private readonly reqService: ReqService) {}

  init(): Observable<ApiResult<InitResponse>> {
    return this.reqService.get('/init');
  }

  getGriefList(pageNumber = 1): Observable<GriefResponse> {
    return this.reqService.get(`/marketplace/get-grief-articles/${pageNumber}`);
  }

  allAdsCategories(): Observable<unknown> {
    return this.reqService.get(`/marketplace/get-all-categories`);
  }

  validAdsCategories(): Observable<CategoryItem[]> {
    return this.reqService.get(`/marketplace/get-categories-have-article`, {
      params: {
        is_grief: '-1',
      },
    });
  }

  allValidAds(): Observable<AdItem[]> {
    return this.reqService.get(`/marketplace/get-all-valid-items`, {
      params: {
        is_grief: '-1',
      },
    });
  }

  adsByCategory(categorySlug: string, pageNumber = 1): Observable<AdsByCategoryResponse> {
    return this.reqService.get(`/marketplace/get-articles-by-category-slug/${categorySlug}/${pageNumber}`, {
      params: {
        is_grief: '-1',
      },
    });
  }

  adsSearch(query: string, categorySlug: string): Observable<AdItem[]> {
    return this.reqService.get(`/marketplace/search/${query}/${categorySlug}`, {
      params: {
        is_grief: '-1',
      },
    });
  }

  griefSearch(query: string, categorySlug: string): Observable<GriefItem[]> {
    return this.reqService.get(`/marketplace/search/${query}/${categorySlug}`, {
      params: {
        is_grief: '1',
      },
    });
  }

  getHomePage(): Observable<ApiResult<LayoutApiData>> {
    return this.reqService.get('homepage?fields[]=foundationTagSlug&fields[]=foundationTagTitle');
  }

  getAgro(): Observable<AgroResponse> {
    const params = new HttpParams().append('cache', new Date().getTime().toString());

    return this.reqService.get(`mediaworks/agrokep`, {
      params,
    });
  }

  getLayoutPreview(hash: string): Observable<ApiResult<Record<string, unknown>>> {
    return this.reqService.get(`layout/preview/view?previewHash=${hash}`);
  }

  getAstrology(): Observable<BackendAstrologyResponse> {
    return this.reqService.get<BackendAstrologyResponse>('mediaworks/astronomy');
  }

  getCategoryArticles(
    categorySlug: string,
    page = 0,
    itemsPerPage = 10,
    year?: string,
    month?: string,
    excludedIds: string[] = []
  ): Observable<ApiResult<ArticleCard[], ApiResponseMetaList>> {
    let { params }: IHttpOptions = {
      params: {
        columnSlug: categorySlug,
        rowCount_limit: itemsPerPage.toString(),
        page_limit: page.toString(),
        'excludedArticleIds[]': excludedIds,
      },
    };
    params = year ? { ...params, year } : params;
    params = month ? { ...params, month } : params;
    return this.reqService
      .get<ApiResult<ArticleCard[], ApiResponseMetaList>>(`content-page/articles-by-column`, {
        params,
      })
      .pipe(
        map(({ data, meta }) => {
          return {
            meta,
            data: data.map((article) => {
              const [publishYear, publishMonth] = (article.publishDate as string).split('-');
              return {
                ...article,
                publishYear,
                publishMonth,
              };
            }),
          };
        })
      );
  }

  getArticleRedirect(requestUrl: string): Observable<unknown> {
    return this.reqService.get(`portal/redirection?url=${requestUrl}`);
  }

  getMenu(): Observable<ApiResult<MenuTreeResponse>> {
    return this.reqService.get('menu/tree');
  }

  getSidebar(categorySlug?: string, articleSlug?: string): Observable<LayoutServiceResponse> {
    if (categorySlug) {
      return this.reqService.get<LayoutServiceResponse>(`/column-sidebar-layout/${categorySlug}`).pipe(
        switchMap((res) => (!res.data.struct ? this.getCommonSidebar(articleSlug) : of(res))),
        catchError(() => {
          return this.getCommonSidebar(articleSlug);
        })
      );
    }
    return this.getCommonSidebar(articleSlug);
  }

  getCommonSidebar(articleSlug?: string): Observable<LayoutServiceResponse> {
    if (articleSlug) {
      return this.reqService.get<LayoutServiceResponse>('/sidebar', {
        params: {
          currentArticleSlug: articleSlug,
        },
      });
    }
    return this.reqService.get<LayoutServiceResponse>('/sidebar');
  }

  getSidebarArticleRecommendations(count: number, columnSlug: string, excludedIds: string[] = []): Observable<ArticleResponse> {
    let request;
    let { params }: IHttpOptions = {
      params: {
        rowCount_limit: count.toString(),
        'excludeIds[]': excludedIds,
      },
    };
    params = columnSlug ? { ...params, columnSlug } : params;
    if (columnSlug) {
      request = this.reqService.get<ArticleResponse>(`/content-page/articles-by-column?dev`, { params });
    } else {
      request = this.reqService.get<any>('/content-page/articles-by-last-day', { params });
    }
    return request;
  }

  getSidebarOpinionRecommendations(count: number, categorySlug: string, excludedIds: string[] = []): Observable<ArticleResponse> {
    let { params }: IHttpOptions = {
      params: {
        rowCount_limit: count.toString(),
        'excludeIds[]': excludedIds,
      },
    };

    params = categorySlug ? { ...params, columnSlug: categorySlug } : params;

    return this.reqService.get<ArticleResponse>(`content-page/articles-by-opinion-type?dev`, { params });
  }

  getPortfolioFooter(): Observable<PortfolioResponse> {
    return this.reqService.get('portal/portfolio-footer');
  }

  getTenyekBoxArticles(): Observable<any> {
    return this.reqService.get('/external-feed/tenyek-feed');
  }

  getSearch(searchQuery: SearchQuery, page = 0, itemsPerPage = 20): Observable<ApiListResult<ArticleSearchResult>> {
    return this.reqService.get(`content-page/search?`, {
      params: {
        ...searchQuery,
        rowCount_limit: itemsPerPage.toString(),
        page_limit: page.toString(),
      },
    });
  }

  getArticlesByFoundationTag(foundationTag: string): Observable<ApiResult<ArticleSearchResult[], ApiResponseMetaList>> {
    return this.reqService
      .get(`content-page/search`, {
        params: {
          foundationTagSelect: foundationTag,
          rowCount_limit: '1',
          page_limit: '0',
          isFoundationContent: '1',
        },
      })
      .pipe(
        map(({ data, meta }: any) => ({
          data: data.map(backendArticlesSearchResultsToArticleSearchResArticles),
          meta,
        }))
      );
  }

  searchArticleByTags(tags: string[], page = 0, itemsPerPage = 10): Observable<ApiResult<ArticleSearchResult[], ApiResponseMetaList>> {
    return this.reqService
      .get(`content-page/search`, {
        params: {
          ...buildPhpArrayParam(tags, 'tags'),
          rowCount_limit: itemsPerPage.toString(),
          page_limit: page.toString(),
        },
      })
      .pipe(
        map(({ data, meta }: any) => ({
          data: data.map(backendArticlesSearchResultsToArticleSearchResArticles),
          meta,
        }))
      );
  }

  getAuthors(page = 0, perPage = 10, isInner?: boolean): Observable<ApiListResult<BackendAuthorData>> {
    const params = {
      rowCount_limit: perPage.toString(),
      page_limit: page.toString(),
      is_active: '1',
      is_inner: isInner ? '1' : '0',
    };

    return this.reqService.get('user/authors', {
      params,
    });
  }

  getPublicAuthorSocial(global_filter?: string, options?: IHttpOptions): Observable<ApiResult<AuthorData, ApiResponseMetaList>> {
    let params = { ...options?.params };
    params = global_filter ? { ...params, global_filter } : params;
    return this.reqService.get(`user/author_social`, { params });
  }

  searchArticles(page: number, perPage = 10, filters: any): Observable<ApiResult<ArticleSearchResult[], ApiResponseMetaList>> {
    const params = {
      rowCount_limit: perPage.toString(),
      page_limit: page.toString(),
      ...filters,
    };

    return this.reqService.get(`/content-page/search`, {
      params,
    });
  }

  getSecretDaysCalendar(id: string): Observable<ApiResult<SecretDaysCalendar, ApiResponseMetaList>> {
    return this.reqService.get(`secret-days-calendar/${id}`);
  }

  getSecretDaysCalendarDay(id: string): Observable<ApiResult<CalendarDay, ApiResponseMetaList>> {
    return this.reqService.get(`secret-days-calendar-day/${id}`);
  }

  getVariableSponsoredDidYouKnowBox(id: string): Observable<VariableDidYouKnowBox> {
    return this.reqService.get(`/content-group/did-you-know/${id}?getRandomData=1`);
  }
}
