<kesma-layout
  [breakingNews]="breakingNews"
  [blockTitleRef]="blockTitles"
  [configuration]="configuration"
  [contentComponentsRef]="contentComponents"
  [contentComponentWrapperRef]="contentComponentsWrapper"
  [contentComponentInnerWrapperRef]="contentComponentsInnerWrapper"
  [blockTitleWrapperRef]="blockTitleWrapper"
  [layoutType]="layoutType"
  [structure]="structure"
  [editorFrameSize]="editorFrameSize"
>
</kesma-layout>

<ng-template #blockTitles let-layoutElement="layoutElement">
  @if (layoutType !== layoutPageTypes.SIDEBAR) {
    <app-block-title-row
      [text]="layoutElement.blockTitle?.text"
      [url]="layoutElement.blockTitle?.url"
      [urlName]="layoutElement.blockTitle?.urlName"
    ></app-block-title-row>
  } @else {
    <app-block-title-sidebar [data]="layoutElement.blockTitle"></app-block-title-sidebar>
  }
</ng-template>

<ng-template #contentComponents let-desktopWidth="desktopWidth" let-index="index" let-layoutElement="layoutElement">
  <ng-container *ngIf="layoutElement.contentType === 'ad'">
    <kesma-advertisement-adocean *ngIf="layoutElement.ad" [ad]="layoutElement.ad" [isHidden]="layoutElement.contentType !== 'ad' && !layoutElement.ad">
    </kesma-advertisement-adocean>
  </ng-container>

  <ng-container
    *ngIf="
      layoutElement.contentType === LayoutElementContentType.Article &&
      layoutElement?.secondaryContentType === LayoutElementContentType.KompostBlock &&
      layoutElement.config
    "
  >
    <app-kompost-box [data]="layoutElement.extractorData"></app-kompost-box>
  </ng-container>

  <ng-container *ngIf="layoutElement?.secondaryContentType === LayoutElementContentType.YessfactorBlock && layoutElement.config">
    <app-yess-factor-box [data]="layoutElement.extractorData"></app-yess-factor-box>
  </ng-container>

  <ng-container *ngIf="layoutElement?.secondaryContentType === LayoutElementContentType.RIPOST7_BLOCK && layoutElement.config">
    <app-ripost7-box [data]="layoutElement.extractorData"></app-ripost7-box>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Opinion && layoutElement.config">
    <app-article-card
      *ngIf="layoutElement?.extractorData?.[index] as data"
      [type]="layoutElement.styleId"
      [data]="data"
      [isSidebar]="layoutType === layoutPageTypes.SIDEBAR"
    >
    </app-article-card>
  </ng-container>
  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Article && layoutElement.config">
    <app-article-card
      [desktopWidth]="desktopWidth"
      *ngIf="layoutElement?.extractorData?.[index]"
      [data]="layoutElement.extractorData[index]"
      [isSidebar]="layoutType === layoutPageTypes.SIDEBAR || desktopWidth < 5"
      [type]="layoutElement.styleId"
    >
    </app-article-card>
  </ng-container>
  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.PrBlock && layoutElement.config">
    <app-article-card
      *ngIf="layoutElement?.extractorData?.[index]"
      [data]="layoutElement.extractorData[index]"
      [isSidebar]="layoutType === layoutPageTypes.SIDEBAR"
      [type]="layoutElement.styleId"
    >
    </app-article-card>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Wysiwyg">
    <app-wysiwyg-box *ngIf="layoutElement.extractorData as data" [htmlArray]="data"></app-wysiwyg-box>
  </ng-container>
  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Dossier && layoutElement.config">
    @if (layoutElement.extractorData?.[0]?.sponsorship) {
      <app-dossier-sponsoration-header [data]="layoutElement.extractorData?.[0]"></app-dossier-sponsoration-header>
    }
    <app-dossier-box *ngIf="layoutElement.extractorData as data" [data]="data" [desktopWidth]="desktopWidth"> </app-dossier-box>
  </ng-container>
  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Image">
    <app-image-box [layoutImageData]="layoutElement.extractorData"></app-image-box>
  </ng-container>
  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Vote">
    <app-voting-box *ngIf="layoutElement.extractorData as data" [voting]="data['data']" type="layout"></app-voting-box>
  </ng-container>
  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Breaking && layoutElement.config">
    <app-breaking-block
      *ngIf="layoutElement.extractorData?.[0] as data"
      [data]="data"
      [articleColumn]="layoutElement.config?.selectedBreakings?.[0]?.original?.primaryColumn"
      [isLayout]="true"
    >
    </app-breaking-block>
  </ng-container>
  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Quiz && layoutElement.config">
    <app-quiz *ngIf="layoutElement.extractorData as data" [quiz]="data"></app-quiz>
  </ng-container>
  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.VideoBlock && layoutElement.config">
    <app-video-block *ngIf="layoutElement.extractorData as data" [videos]="data"></app-video-block>
  </ng-container>
  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.TrendingTagsBlock">
    <app-trending-tags-wrapper *ngIf="layoutElement.config as data" [trendingTagsBlockData]="data"></app-trending-tags-wrapper>
  </ng-container>
  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.TagBlock">
    <app-fresh-topics *ngIf="layoutElement.config?.tags as data" [data]="data" [isLayout]="true"> </app-fresh-topics>
  </ng-container>
  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.HtmlEmbed">
    <app-wysiwyg-box [html]="layoutElement?.config?.htmlContent?.desktop" class="hide-mobile"></app-wysiwyg-box>
    <app-wysiwyg-box [html]="layoutElement?.config?.htmlContent?.mobile" class="hide-desktop"></app-wysiwyg-box>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.IngatlanbazarSearch">
    <app-real-estate-bazaar-search-block
      [defaultLocation]="layoutElement.config.defaultLocation"
      [defaultType]="layoutElement.config.defaultType"
      [showAdvertiseButton]="layoutElement.config.showAdvertiseButton"
      [showBudapestLocations]="layoutElement.config.showBudapestLocations"
      [showCountyLocationsWithBudapest]="layoutElement.config.showCountyLocationsWithBudapest"
      [showCountyLocations]="layoutElement.config.showBudapestLocations"
      [showNewBuildButton]="layoutElement.config.showNewBuildButton"
      [showOtherLocations]="layoutElement.config.showOtherLocations"
      [utmSource]="layoutElement.config.utmSource"
    >
    </app-real-estate-bazaar-search-block>
  </ng-container>
  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.IngatlanbazarConfigurable">
    <app-real-estate-bazaar-block
      (initEvent)="handleRealEstateInitEvent()"
      [data]="realEstateBazaar.realEstateData"
      [itemsToShow]="layoutElement.itemsToShow"
      [showHeader]="layoutElement.showHeader"
    >
    </app-real-estate-bazaar-block>
  </ng-container>
  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.INGATLANBAZAR">
    <app-real-estate-bazaar-block
      (initEvent)="handleRealEstateInitEvent()"
      [data]="realEstateBazaar.realEstateData"
      [itemsToShow]="1"
      [showHeader]="true"
    ></app-real-estate-bazaar-block>
  </ng-container>
  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.BrandingBoxEx">
    <app-branding-box-ex
      *ngIf="layoutElement.extractorData as data"
      [brand]="data.brand"
      [articleLimit]="data.articleLimit"
      [disabledColumns]="data.disabledColumns"
    ></app-branding-box-ex>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.DID_YOU_KNOW">
    <app-variable-sponsored-did-you-know-wrapper [id]="layoutElement.config?.selectedDidYouKnowBox?.[0]?.id"></app-variable-sponsored-did-you-know-wrapper>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.CONFIGURABLE_SPONSORED_BOX">
    <kesma-sponsored-box [data]="layoutElement.config"></kesma-sponsored-box>
  </ng-container>
</ng-template>
