import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnChanges, OnInit, SimpleChanges, TemplateRef } from '@angular/core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  BlockWrapperTemplateData,
  BreakingNews,
  LayoutComponent as KesmaLayoutComponent,
  LayoutContentItemWrapperTemplateData,
  LayoutElement,
  LayoutElementColumn,
  LayoutElementContent,
  LayoutElementContentAd,
  LayoutElementContentConfiguration,
  LayoutElementContentType,
  LayoutElementRow,
  LayoutElementType,
  LayoutPageType,
  provideLayoutDataExtractors,
  SponsoredBoxComponent,
} from '@trendency/kesma-ui';
import { NgIf } from '@angular/common';
import {
  ArticleCardComponent,
  BlockTitleRowComponent,
  BlockTitleSidebarComponent,
  BrandingBoxExComponent,
  BreakingBlockComponent,
  Dossier<PERSON>oxComponent,
  FreshTopicsComponent,
  ImageBoxComponent,
  Kompost<PERSON>omponent,
  QuizComponent,
  RealEstateBazaarBlockComponent,
  RealEstateBazaarSearchBlockComponent,
  RealEstateBazaarService,
  Ripost7BoxComponent,
  TrendingTagsWrapperComponent,
  VideoBlockComponent,
  VotingBoxComponent,
  WysiwygBoxComponent,
  YessFactorComponent,
} from '../../../../shared';
import { RIPOST_EXTRACTORS_CONFIG } from '../../extractors/extractor.config';
import { DossierSponsorationHeaderComponent } from '../../../../shared/components/dossier-sponsoration-header/dossier-sponsoration-header.component';
import { VariableSponsoredDidYouKnowWrapperComponent } from '../../../../shared/components/variable-sponsored-did-you-know-wrapper/variable-sponsored-did-you-know-wrapper.component';

@Component({
  selector: 'app-layout',
  templateUrl: './layout.component.html',
  styleUrls: ['./layout.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    KesmaLayoutComponent,
    BlockTitleRowComponent,
    BlockTitleSidebarComponent,
    NgIf,
    AdvertisementAdoceanComponent,
    KompostComponent,
    YessFactorComponent,
    Ripost7BoxComponent,
    ArticleCardComponent,
    WysiwygBoxComponent,
    DossierBoxComponent,
    ImageBoxComponent,
    VotingBoxComponent,
    BreakingBlockComponent,
    QuizComponent,
    VideoBlockComponent,
    TrendingTagsWrapperComponent,
    FreshTopicsComponent,
    RealEstateBazaarSearchBlockComponent,
    RealEstateBazaarBlockComponent,
    BrandingBoxExComponent,
    BlockTitleRowComponent,
    BlockTitleSidebarComponent,
    AdvertisementAdoceanComponent,
    KompostComponent,
    YessFactorComponent,
    Ripost7BoxComponent,
    ArticleCardComponent,
    WysiwygBoxComponent,
    DossierBoxComponent,
    ImageBoxComponent,
    VotingBoxComponent,
    BreakingBlockComponent,
    VideoBlockComponent,
    TrendingTagsWrapperComponent,
    FreshTopicsComponent,
    RealEstateBazaarSearchBlockComponent,
    RealEstateBazaarBlockComponent,
    BrandingBoxExComponent,
    QuizComponent,
    SponsoredBoxComponent,
    DossierSponsorationHeaderComponent,
    VariableSponsoredDidYouKnowWrapperComponent,
  ],
  providers: [provideLayoutDataExtractors(RIPOST_EXTRACTORS_CONFIG, true)],
})
export class LayoutComponent implements OnInit, OnChanges {
  readonly layoutPageTypes = LayoutPageType;
  @Input() adPageType: string;
  @Input() structure: LayoutElementRow[];
  @Input() configuration: LayoutElementContentConfiguration[];
  @Input() layoutType: LayoutPageType;
  @Input() contentComponentsWrapper: TemplateRef<LayoutContentItemWrapperTemplateData>;
  @Input() contentComponentsInnerWrapper: TemplateRef<LayoutContentItemWrapperTemplateData>;
  @Input() blockTitleWrapper: TemplateRef<BlockWrapperTemplateData>;
  @Input() editorFrameSize?: 'desktop' | 'mobile';

  dataMapped: boolean;
  adZones: any[];
  @Input() breakingNews: BreakingNews[] = [];

  LayoutElementContentType = LayoutElementContentType;

  constructor(
    public readonly realEstateBazaar: RealEstateBazaarService,
    private readonly changeRef: ChangeDetectorRef,
    private readonly adStoreAdo: AdvertisementAdoceanStoreService
  ) {}

  ngOnInit(): void {
    this.adStoreAdo.advertisemenets$.subscribe((ads) => {
      this.adZones = ads;
      this.initADZones(this.structure);
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (!changes['structure']?.firstChange) {
      this.initADZones(this.structure);
    }

    if (changes['configuration']?.currentValue || changes['structure']?.currentValue) {
      this.dataMapped = false;
      this.mapNode(this.structure);
      this.dataMapped = true;
      this.changeRef.detectChanges();
    }
  }

  handleRealEstateInitEvent(): void {
    this.realEstateBazaar.handleRealEstateInitEvent()?.subscribe(() => {
      this.changeRef.detectChanges();
    });
  }

  initADZones(elements: LayoutElement[]): void {
    if (elements) {
      elements.forEach((el) => {
        // recursions on row & column children
        if (el.type === LayoutElementType.Row || el.type === LayoutElementType.Column) {
          this.initADZones((el as LayoutElementRow | LayoutElementColumn).elements);
        } else if ((el as LayoutElementContent).contentType === LayoutElementContentType.Ad) {
          (el as LayoutElementContentAd).ad = this.getAd(el as LayoutElementContentAd);
        }
      });
      this.changeRef.detectChanges();
    }
  }

  mapNode(elements: LayoutElement[]): void {
    elements.forEach((el) => {
      // recursions on row & column children
      if (el.type === LayoutElementType.Row || el.type === LayoutElementType.Column) {
        this.mapNode((el as LayoutElementRow | LayoutElementColumn).elements);
      } else {
        (el as LayoutElementContent).config = this.getConfig(el as LayoutElementContent);
      }
    });
  }

  getAd(element: LayoutElementContentAd): Advertisement | undefined {
    const zoneIds = this.adStoreAdo.separateAdsByMedium(this.adZones, this.adPageType, [element?.bannerName]);

    if (element?.medium === 'desktop' && zoneIds?.desktop) {
      return zoneIds?.desktop[element?.bannerName];
    }
    if (element?.medium === 'mobile' && zoneIds?.mobile) {
      return zoneIds?.mobile[element?.bannerName];
    }

    return undefined;
  }

  getConfig(element: LayoutElementContent): LayoutElementContentConfiguration | undefined {
    return this.configuration.find((c) => c?.layoutElementId === element?.id);
  }
}
