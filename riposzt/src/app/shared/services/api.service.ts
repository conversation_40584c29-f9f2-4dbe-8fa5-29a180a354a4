import { Injectable } from '@angular/core';
import { IHttpOptions, ReqService } from '@trendency/kesma-core';
import { Observable } from 'rxjs';
import {
  ApiListResult,
  ApiResponseMetaList,
  ApiResult,
  ArticleResponse,
  ArticleSearchResult,
  BackendAuthorData,
  InitResponse,
  LayoutApiData,
  MenuTreeResponse,
  TrendingTag,
  VariableDidYouKnowBox,
} from '@trendency/kesma-ui';
import { AuthorData } from '../../feature/authors/api/author.definitions';

@Injectable({
  providedIn: 'root',
})
export class ApiService {
  constructor(private readonly reqService: ReqService) {}

  init(): Observable<ApiResult<InitResponse>> {
    return this.reqService.get<ApiResult<InitResponse>>('/init');
  }

  getMenu(): Observable<ApiResult<MenuTreeResponse>> {
    return this.reqService.get<ApiResult<MenuTreeResponse>>('menu/tree');
  }

  getHomePage(): Observable<ApiResult<LayoutApiData>> {
    return this.reqService.get<ApiResult<LayoutApiData>>('homepage');
  }

  getLayoutPreview(hash: string): Observable<any> {
    return this.reqService.get(`layout/preview/view?previewHash=${hash}`);
  }

  getSidebarArticleRecommendations(count: number, columnSlug: string, excludedIds: string[] = []): Observable<ArticleResponse> {
    let request;
    let { params }: IHttpOptions = {
      params: {
        rowCount_limit: count.toString(),
        'excludedArticleIds[]': excludedIds,
      },
    };
    params = columnSlug ? { ...params, columnSlug } : params;
    if (columnSlug) {
      request = this.reqService.get<ArticleResponse>(`/content-page/articles-by-column?dev`, {
        params,
      });
    } else {
      request = this.reqService.get<any>('/content-page/articles-by-last-day', { params });
    }
    return request;
  }

  getTrendingTagDetailsByTagIds(tagIds: string[]): Observable<ApiResult<TrendingTag[], ApiResponseMetaList>> {
    const { params }: IHttpOptions = {
      params: {
        'ids[]': tagIds,
        rowCount_limit: String(tagIds.length),
      },
    };
    return this.reqService.get<ApiResult<TrendingTag[], ApiResponseMetaList>>(`/source/content-group/tags`, { params });
  }

  getVideo(slug: string): Observable<any> {
    return this.reqService.get(`media/videos/${slug}`);
  }

  getTagsOnHeaderBar(): Observable<any> {
    return this.reqService.get('/content-group/tags-on-header-bar');
  }

  getAuthors(page: number, perPage = 10, isInner?: boolean): Observable<ApiListResult<BackendAuthorData>> {
    const params: Record<string, string> = {
      rowCount_limit: perPage.toString(),
      page_limit: page.toString(),
      is_active: '1',
      is_inner: isInner ? '1' : '0',
    };

    return this.reqService.get('user/authors', {
      params,
    });
  }

  getPublicAuthorSocial(global_filter?: string, options?: IHttpOptions): Observable<ApiResult<AuthorData, ApiResponseMetaList>> {
    let params = { ...options?.params };
    params = global_filter ? { ...params, global_filter } : params;
    return this.reqService.get(`user/author_social`, { params });
  }

  searchArticles(page: number, perPage = 10, filters: any): Observable<ApiResult<ArticleSearchResult[], ApiResponseMetaList>> {
    const params = {
      rowCount_limit: perPage.toString(),
      page_limit: page.toString(),
      ...filters,
    };

    return this.reqService.get(`/content-page/search`, {
      params,
    });
  }

  getVariableSponsoredDidYouKnowBox(id: string): Observable<VariableDidYouKnowBox> {
    return this.reqService.get(`/content-group/did-you-know/${id}?getRandomData=1`);
  }
}
