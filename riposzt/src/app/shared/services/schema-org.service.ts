import { Injectable, Renderer2, RendererFactory2 } from '@angular/core';
import { environment } from '../../../environments/environment';
import { ArticleSchema, SchemaOrgUtils, SchemaOrgWebpageDataTemplate, SeoService } from '@trendency/kesma-core';
import { Article } from '@trendency/kesma-ui';

@Injectable({
  providedIn: 'root',
})
export class JsonLDService {
  private readonly renderer: Renderer2;
  private readonly schemaScriptTag: any;

  constructor(
    private readonly seo: SeoService,
    private readonly rendererFactory: RendererFactory2
  ) {
    this.renderer = this.rendererFactory.createRenderer(null, null);
    this.schemaScriptTag = this.renderer.selectRootElement(SchemaOrgUtils.className, true);
  }

  removeStructuredData(): void {
    this.renderer.setProperty(this.schemaScriptTag, 'innerHTML', '');
  }

  insertSchema(schemaArg: ArticleSchema): void {
    this.removeStructuredData();
    const schema = schemaArg || SchemaOrgWebpageDataTemplate;
    const scriptText = this.renderer.createText(JSON.stringify(schema));
    this.renderer.appendChild(this.schemaScriptTag, scriptText);
  }

  getStructuredDataForArticle(article: Article): ArticleSchema {
    return (
      article && {
        '@type': 'NewsArticle',
        headline: article.title,
        alternativeHeadline: article.title,
        image: article.thumbnail,
        url: `${this.seo.currentUrl}`,
        mainEntityOfPage: `${this.seo.currentUrl}`,
        description: article.excerpt,
        dateModified: article.lastUpdated || article.publishDate,
        author: article.publicAuthor,
        datePublished: article.publishDate,
        publisher: {
          '@type': 'Organization',
          name: 'Mediaworks Hungary Zrt.',
          logo: {
            '@type': 'ImageObject',
            height: '100',
            width: '100',
            url: article.avatar || `${environment?.siteUrl}/assets/images/mn-avatar.png`,
          },
        },
      }
    );
  }
}
