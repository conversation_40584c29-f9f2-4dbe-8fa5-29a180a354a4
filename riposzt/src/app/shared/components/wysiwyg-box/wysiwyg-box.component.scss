@use 'shared' as *;
@use 'sass:color';

$box-shadow-size: 0px 0px 0px 0.5rem;

@mixin block-margin() {
  margin-bottom: $block-bottom-margin;
  @include media-breakpoint-down(sm) {
    margin-bottom: 32px;
  }
}

.clearfix {
  @include clearfix();
}

.block-content::ng-deep {
  @include block-margin;

  p,
  li,
  span {
    font-family: $font-poppins;
    font-size: 18px;
    line-height: 26px;
  }

  h2 {
    font-size: 24px;
    font-family: $font-barlowc;
    line-height: 29px;
    font-weight: bold;
    margin-bottom: 30px;
    @include media-breakpoint-down(sm) {
      font-size: 20px;
      line-height: 24px;
    }
  }

  p {
    @include block-margin;
  }

  a {
    color: $red;
    font-weight: bold;
    text-decoration: underline;
  }

  strong,
  b {
    font-weight: bold;
  }

  i,
  em {
    font-style: italic;
  }

  figure.image {
    display: table;
    clear: both;
    text-align: center;
    margin-inline: auto;
    @include block-margin;

    &.image-style-align-left {
      float: left;
      margin-right: 20px;

      @include clearfix();
      @include media-breakpoint-down(sm) {
        margin-right: 10px;
      }
    }

    &.image-style-align-right {
      float: right;
      margin-left: 20px;

      @include clearfix();
      @include media-breakpoint-down(sm) {
        margin-left: 10px;
      }
    }

    figcaption {
      display: table-caption;
      caption-side: bottom;
      text-align: left;
      padding: 10px 15px;
      background-color: $grey-22;
      position: relative;
      font-size: 16px;
      line-height: 22px;
      @include media-breakpoint-down(sm) {
        font-size: 14px;
        line-height: 16px;
      }

      &::before {
        content: '';
        background: $red;
        position: absolute;
        left: 0;
        top: 0;
        width: 5px;
        height: 100%;
      }
    }
  }

  ul {
    margin: 30px 0;

    li {
      margin: 15px 0;
      padding-left: 60px;
      position: relative;

      &:before {
        position: absolute;
        height: 3px;
        width: 30px;
        background: $yellow;
        content: ' ';
        display: block;
        top: 16px;
        left: 0;
      }
    }
  }

  ol {
    list-style-type: auto;
    padding-left: 40px;
    margin: 30px 0;

    li {
      margin: 15px 0;
      padding-left: 10px;
    }
  }

  .table-scroller {
    width: 100%;
    overflow: auto;
  }

  figure.table {
    max-width: 100%;
    overflow-x: auto;
    display: table;

    table tr,
    td {
      background-color: transparent;
    }

    figcaption {
      background-color: $grey-3;
      display: table-caption;
      caption-side: top;
      text-align: left;
      padding: 0.6em;
      font-size: 0.75em;
    }
  }

  table {
    max-width: 100%;
    border: 0;
    overflow-x: auto;
    border-spacing: 0;

    thead {
      tr {
        td,
        th {
          background: color.adjust($red, $lightness: -90%);
          background: white;
          padding: 9px 16px;
          font-size: 18px;
          border: 1px solid $red;
        }

        th {
          text-transform: uppercase;
        }

        @include media-breakpoint-down(md) {
          padding: 5px 16px;
        }
      }
    }

    tr {
      td,
      th {
        background: white;
        color: $red;
        font-size: 16px;
        line-height: 30px;
        padding: 22px 16px;
        border-left: 1px solid $red;
        border-top: 1px solid $red;

        @include media-breakpoint-down(md) {
          padding: 10px 16px;
        }

        &:first-child {
          padding-left: 33px;
        }

        &:last-child {
          padding-right: 33px;
        }
      }

      th {
        font-weight: 500;
      }
    }

    td:last-of-type,
    th:last-of-type {
      border-right: 1px solid $red;
    }

    tr:last-of-type {
      > td,
      > th {
        border-bottom: 1px solid $red;
      }
    }
  }

  blockquote {
    margin: 30px 0;
    position: relative;

    @include media-breakpoint-down(sm) {
      margin: 30px 0 51px;
      padding: 20px 0 5px 20px;
    }

    p {
      &:last-child {
        margin-bottom: 0;
      }
    }

    &.quote {
      position: relative;
      z-index: 1;
      font-size: 30px;
      font-style: italic;
      letter-spacing: 0;
      line-height: 46px;
      font-family: $font-barlowc;
      color: $black;
      @include media-breakpoint-down(sm) {
        font-size: 24px;
        line-height: 29px;
      }

      &,
      p {
        position: relative;
        z-index: 1;
        font-size: 48px;
        letter-spacing: 0;
        line-height: 52px;
        @include media-breakpoint-down(sm) {
          font-size: 32px;
          line-height: 36px;
        }
      }
    }

    &.highlight {
      margin-bottom: 30px;
      padding: 0 0.5rem;

      p {
        display: inline;
        padding: 0 0;
        font-size: 20px;
        line-height: 200%;
        background: $yellow;
        box-shadow: $box-shadow-size $yellow;
        @include media-breakpoint-down(sm) {
          font-size: 16px;
        }

        &.yellow {
          background: $yellow;
          box-shadow: $box-shadow-size $yellow;
        }

        &.red {
          background: $red;
          color: $white;
          box-shadow: $box-shadow-size $red;
        }

        &.purple {
          background: $purple-2;
          color: $white;
          box-shadow: $box-shadow-size $purple-2;
        }

        &.pink {
          background: $pink;
          box-shadow: $box-shadow-size $pink;
        }

        &.blue {
          background: $blue;
          box-shadow: $box-shadow-size $blue;
        }

        &.orange {
          background: $orange;
          box-shadow: $box-shadow-size $orange;
        }

        &.turquoise {
          background: $turquoise;
          box-shadow: $box-shadow-size $turquoise;
        }

        &.grey {
          background: $grey-5;
          box-shadow: $box-shadow-size $grey-5;
        }

        &.darkgreen {
          background: $darkgreen;
          box-shadow: $box-shadow-size $darkgreen;
        }

        &.darkblue {
          background: $darkblue;
          color: $white;
          box-shadow: $box-shadow-size $darkblue;
        }
      }
    }

    &.border-text {
      border-top: 6px solid #e2003b;
      background: rgba(226, 0, 59, 0.1);
      padding: 30px;
    }
  }

  .custom-text-style {
    display: block;
    margin: 30px 0;
    position: relative;
    clear: both;

    &:last-child {
      margin-bottom: 0;
    }

    @include media-breakpoint-down(sm) {
      margin: 30px 0 51px;
      padding: 20px 0 5px 20px;
    }

    &.quote {
      position: relative;
      z-index: 1;
      font-size: 30px;
      font-style: italic;
      letter-spacing: 0;
      line-height: 46px;
      font-family: $font-barlowc;
      padding: 15px 0 15px 30px;
      border-left: 10px solid $yellow;
      color: $black;
      @include media-breakpoint-down(sm) {
        font-size: 24px;
        line-height: 29px;
      }

      p {
        margin-bottom: 0;

        &:last-child:after {
          content: '\201D';
        }

        &:first-child:before {
          content: '\201E';
        }
      }
    }

    &.highlight {
      margin-bottom: 30px;

      padding: 0 0.5rem;
      color: white;
      background-color: $red;

      p {
        padding: 0 0;
        font-size: 20px;
        line-height: 160%;
        margin-bottom: 0;
        @include media-breakpoint-down(sm) {
          font-size: 16px;
        }

        &.yellow {
          background: $yellow;
          box-shadow: $box-shadow-size $yellow;
        }

        &.red {
          background: $red;
          color: $white;
          box-shadow: $box-shadow-size $red;
        }

        &.purple {
          background: $purple-2;
          color: $white;
          box-shadow: $box-shadow-size $purple-2;
        }

        &.pink {
          background: $pink;
          box-shadow: $box-shadow-size $pink;
        }

        &.blue {
          background: $blue;
          box-shadow: $box-shadow-size $blue;
        }

        &.orange {
          background: $orange;
          box-shadow: $box-shadow-size $orange;
        }

        &.turquoise {
          background: $turquoise;
          box-shadow: $box-shadow-size $turquoise;
        }

        &.grey {
          background: $grey-5;
          box-shadow: $box-shadow-size $grey-5;
        }

        &.darkgreen {
          background: $darkgreen;
          box-shadow: $box-shadow-size $darkgreen;
        }

        &.darkblue {
          background: $darkblue;
          color: $white;
          box-shadow: $box-shadow-size $darkblue;
        }

        a {
          color: $white;
        }
      }
    }

    &.border-text {
      border-top: 6px solid #e2003b;
      background: rgba(226, 0, 59, 0.1);
      padding: 16px 30px;

      p {
        margin-bottom: 0;
      }
    }

    &.underlined-text {
      font-weight: bold;
      font-size: 20px;
      margin: 40px 0;
      text-decoration: underline;
      text-decoration-color: #e2003b;
      text-decoration-thickness: 3px;
      word-break: break-word;
    }
  }

  .raw-html-embed {
    width: 100%;

    // Do not use flex here, because some 3rd party stuff (iframe.ly) doesn't like it
    display: block;

    > * {
      margin: 0 auto;
    }

    .instagram-media {
      margin: auto !important;
    }

    iframe {
      max-width: 100%;
      display: flex;
      justify-content: center;
    }
  }
}
